# 💳 LemonSqueezy Payment Integration

Complete payment system for WhatsApp Habit Tracker bot with LemonSqueezy integration.

## 🏗️ Features Implemented

✅ **Payment Processing**
- LemonSqueezy webhook integration
- Automatic access code generation (format: HABIT-ABC123)
- Monthly ($5) and Yearly ($30) subscription support
- Test mode for development

✅ **User Management**
- Payment verification before bot access
- Access code activation via "START HABIT-ABC123" command
- Automatic user unlocking upon payment

✅ **Email System**
- Welcome emails with access codes
- Different templates for monthly vs yearly subscribers
- Email queue with retry logic
- SMTP configuration support

✅ **Affiliate Program**
- Automatic affiliate enrollment for yearly subscribers
- 30% commission tracking
- Unique affiliate codes (format: AFF-ABC123)
- Referral tracking

✅ **Database Schema**
- `paid_users` table for subscription tracking
- `webhook_events` table for audit logging
- `affiliate_referrals` table for commission tracking
- `email_queue` table for email management

## 🚀 Quick Start

### 1. Environment Configuration

Update `/var/www/lockin/.env`:

```bash
# Payment Configuration
PAYMENT_TEST_MODE=true  # Set to false for production
LEMONSQUEEZY_WEBHOOK_SECRET=your_webhook_secret
LEMONSQUEEZY_API_KEY=your_api_key
LEMONSQUEEZY_STORE_ID=your_store_id
LEMONSQUEEZY_MONTHLY_PRODUCT_ID=your_monthly_product_id
LEMONSQUEEZY_MONTHLY_VARIANT_ID=your_monthly_variant_id
LEMONSQUEEZY_YEARLY_PRODUCT_ID=your_yearly_product_id
LEMONSQUEEZY_YEARLY_VARIANT_ID=your_yearly_variant_id

# Email Configuration (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
EMAIL_FROM=<EMAIL>
```

### 2. Database Setup

Tables are automatically created. To verify:

```bash
PGPASSWORD=postgres psql -U postgres -h localhost -d lockin -c "\\d paid_users"
```

### 3. Restart Server

```bash
pm2 restart lockin
```

## 🧪 Testing

### Test Mode Features
- Bypasses LemonSqueezy signature verification
- Provides test endpoints for payment simulation
- Allows existing users to access bot features

### Test Endpoints (available when PAYMENT_TEST_MODE=true)

```bash
# Create test payment
curl -X POST http://localhost:3001/test/create-payment \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "subscriptionType": "yearly"}'

# Check test status
curl http://localhost:3001/test/status

# Clear test data
curl -X DELETE http://localhost:3001/test/clear-data

# Simulate webhook
curl -X POST http://localhost:3001/test/simulate-webhook \
  -H "Content-Type: application/json" \
  -d '{"eventType": "order_created", "email": "<EMAIL>"}'
```

### Quick Test Script

```bash
node test-payment-flow.js
```

## 🔄 User Flow

### New User (Unpaid)
1. User texts bot → Receives paywall message
2. User purchases subscription → Receives email with access code
3. User texts "START HABIT-ABC123" → Bot activates access
4. User can now use full bot features

### Paid User
1. User texts bot → Full access granted immediately
2. Bot checks payment status in database
3. Normal habit tracking functionality available

## 📊 LemonSqueezy Webhook Events

The system handles these webhook events:

- `order_created` → Creates paid user, sends welcome email
- `subscription_created` → Links subscription to user
- `subscription_updated` → Updates subscription details
- `subscription_cancelled` → Marks subscription as cancelled
- `subscription_expired` → Locks user access
- `subscription_payment_success` → Reactivates subscription
- `subscription_payment_failed` → Handles payment failures

## 🔧 Production Setup

### 1. Configure LemonSqueezy

1. Create products for monthly and yearly subscriptions
2. Set up webhook pointing to: `https://yourdomain.com/webhook/lemonsqueezy`
3. Copy webhook secret and product/variant IDs to `.env`

### 2. Set Production Mode

```bash
# In .env
PAYMENT_TEST_MODE=false
LEMONSQUEEZY_WEBHOOK_SECRET=your_real_webhook_secret
```

### 3. Configure Email (Optional)

Set up SMTP credentials in `.env`. If not configured, emails will be logged instead of sent.

### 4. Monitor Logs

```bash
pm2 logs lockin
```

## 📈 Database Queries

### Check Payment Status
```sql
SELECT email, access_code, subscription_type, status 
FROM paid_users 
WHERE status = 'active' 
ORDER BY created_at DESC;
```

### View Affiliate Data
```sql
SELECT pu.email, pu.affiliate_code, COUNT(ar.id) as referrals
FROM paid_users pu
LEFT JOIN affiliate_referrals ar ON pu.id = ar.affiliate_id
WHERE pu.is_affiliate = true
GROUP BY pu.id, pu.email, pu.affiliate_code;
```

### Check Webhook Events
```sql
SELECT event_type, processed, created_at 
FROM webhook_events 
ORDER BY created_at DESC 
LIMIT 10;
```

## 🛠️ Troubleshooting

### Common Issues

1. **Webhook signature validation fails**
   - Check `LEMONSQUEEZY_WEBHOOK_SECRET` in .env
   - Verify webhook URL in LemonSqueezy dashboard

2. **Emails not sending**
   - Check SMTP credentials in .env
   - Monitor `email_queue` table for failed emails

3. **Access codes not working**
   - Verify user texted exact format: "START HABIT-ABC123"
   - Check `paid_users` table for code status

4. **Test mode not working**
   - Ensure `PAYMENT_TEST_MODE=true` in .env
   - Restart server after environment changes

### Debug Commands

```bash
# Check server status
pm2 status

# View real-time logs
pm2 logs lockin --lines 50

# Check database connection
PGPASSWORD=postgres psql -U postgres -h localhost -d lockin -c "SELECT NOW();"

# Test webhook endpoint
curl -X POST http://localhost:3001/webhook/lemonsqueezy \
  -H "Content-Type: application/json" \
  -d '{"meta":{"event_name":"test","test_mode":true},"data":{}}'
```

## 📧 Support

- Check logs: `pm2 logs lockin`
- Monitor database: Use provided SQL queries
- Test endpoints: Available in test mode
- Email queue: Check `email_queue` table for delivery status

---

**Status**: ✅ Complete and Production Ready
**Test Coverage**: Full payment flow tested
**Database**: All tables created and indexed
**Webhooks**: LemonSqueezy integration active
**Affiliate System**: Implemented with 30% commission