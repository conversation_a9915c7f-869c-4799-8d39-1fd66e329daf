# FastSpring Payment Configuration
# Copy this to .env and update with your actual values

# Payment Test Mode - Set to false for production
PAYMENT_TEST_MODE=true

# FastSpring Webhook Configuration
FASTSPRING_WEBHOOK_SECRET=your_webhook_secret_here
FASTSPRING_CHECKOUT_URL=https://your-store.fastspring.com/checkout

# Email Configuration (Optional - will log to console if not configured)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
EMAIL_FROM=<EMAIL>

# Existing Twilio Configuration (keep your current values)
TWILIO_ACCOUNT_SID=your_existing_sid
TWILIO_AUTH_TOKEN=your_existing_token
TWILIO_PHONE_NUMBER=+***********

# Database Configuration (keep your current values)
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/lockin