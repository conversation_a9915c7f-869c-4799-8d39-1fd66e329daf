# ThriveCart Integration - Production Deployment Checklist

## Pre-Deployment Requirements

### 1. ThriveCart Account Setup ✓
- [ ] ThriveCart account created and verified
- [ ] Payment processor connected (Stripe/PayPal)
- [ ] Tax settings configured
- [ ] Refund policy added

### 2. Product Configuration ✓
- [ ] Monthly product created: `Habit Tracker Monthly` ($5/month)
- [ ] Yearly product created: `Habit Tracker Yearly` ($30/year)
- [ ] Product descriptions updated
- [ ] Checkout pages customized with branding

### 3. Legal & Compliance ✓
- [ ] Terms of Service page live at `/legal/terms-of-service.html`
- [ ] Privacy Policy page live at `/legal/privacy-policy.html`
- [ ] GDPR compliance settings configured
- [ ] Cookie policy implemented

---

## Server Configuration

### 4. Environment Variables ✓
```bash
# Update .env file with production values
- [ ] THRIVECART_CHECKOUT_URL=https://habittracker.thrivecart.com/habit-tracker-monthly/
- [ ] THRIVECART_WEBHOOK_SECRET=[actual_secret_from_thrivecart]
- [ ] PAYMENT_TEST_MODE=false
- [ ] NODE_ENV=production
- [ ] DATABASE_URL=[production_database_url]
- [ ] SMTP credentials configured for email sending
```

### 5. SSL & Security ✓
- [ ] SSL certificate valid for webhook domain
- [ ] Firewall configured to allow port 3001
- [ ] Rate limiting configured
- [ ] DDoS protection enabled

### 6. Database Preparation ✓
- [ ] Production database backed up
- [ ] Test data cleaned from database
- [ ] Indexes optimized for production load
- [ ] Connection pool settings optimized

---

## Integration Testing

### 7. Webhook Configuration ✓
- [ ] Webhook URL added to ThriveCart: `https://*************:3001/webhook/thrivecart`
- [ ] All required events selected in ThriveCart
- [ ] Webhook secret copied and added to .env
- [ ] Test webhook sent from ThriveCart dashboard

### 8. Payment Flow Testing ✓
```bash
# Run complete integration test
- [ ] node test-thrivecart-integration.js
- [ ] All tests passing
```

### 9. Access Code Verification ✓
- [ ] Test purchase through ThriveCart
- [ ] Access code received via email
- [ ] Access code activates successfully in bot
- [ ] Bot features accessible after activation

### 10. Email System ✓
- [ ] SMTP credentials verified
- [ ] Welcome email template tested
- [ ] Email queue processing confirmed
- [ ] Spam folder check completed

---

## Deployment Steps

### 11. Code Deployment ✓
```bash
# On production server
- [ ] git pull origin main
- [ ] npm install --production
- [ ] npm run build (if applicable)
- [ ] Database migrations run
```

### 12. Service Management ✓
```bash
- [ ] pm2 restart lockin --update-env
- [ ] pm2 save
- [ ] pm2 startup (ensure auto-restart on server reboot)
```

### 13. Health Checks ✓
```bash
# Verify service is running
- [ ] curl http://localhost:3001/health
- [ ] pm2 status
- [ ] pm2 logs lockin --lines 50
```

---

## Post-Deployment Monitoring

### 14. Initial Monitoring (First 24 Hours) ✓
- [ ] Watch logs for errors: `pm2 logs lockin --err`
- [ ] Monitor webhook processing
- [ ] Check email delivery rates
- [ ] Verify payment processing

### 15. Database Monitoring ✓
```sql
-- Check new users
- [ ] SELECT COUNT(*) FROM paid_users WHERE created_at > NOW() - INTERVAL '24 hours';

-- Check webhook events
- [ ] SELECT COUNT(*), event_type FROM webhook_events 
      WHERE source = 'thrivecart' AND created_at > NOW() - INTERVAL '24 hours'
      GROUP BY event_type;

-- Check failed transactions
- [ ] SELECT * FROM payment_transactions 
      WHERE created_at > NOW() - INTERVAL '24 hours' 
      AND status = 'failed';
```

### 16. Performance Metrics ✓
- [ ] Response time < 500ms for webhook processing
- [ ] Database query time < 100ms
- [ ] Email queue processing < 5 minutes
- [ ] Memory usage stable

---

## Rollback Plan

### 17. Emergency Procedures ✓
```bash
# If issues occur, rollback steps:
- [ ] pm2 stop lockin
- [ ] git checkout [previous_commit]
- [ ] Restore database backup if needed
- [ ] Update .env to previous configuration
- [ ] pm2 restart lockin
- [ ] Notify ThriveCart support if webhook issues
```

### 18. Backup Verification ✓
- [ ] Database backup tested and restorable
- [ ] Code repository backup available
- [ ] Environment variables backed up
- [ ] PM2 configuration backed up

---

## Communication

### 19. Team Notifications ✓
- [ ] Development team notified of deployment
- [ ] Support team briefed on new integration
- [ ] Documentation updated in team wiki
- [ ] Monitoring alerts configured

### 20. Customer Communication ✓
- [ ] Support documentation updated
- [ ] FAQ section updated with ThriveCart info
- [ ] Customer support templates prepared
- [ ] Announcement prepared if needed

---

## Final Verification

### 21. End-to-End Testing ✓
- [ ] Complete customer journey tested
- [ ] Refund process tested
- [ ] Subscription cancellation tested
- [ ] Affiliate program tested (yearly subscribers)

### 22. Documentation ✓
- [ ] API documentation updated
- [ ] Webhook event reference documented
- [ ] Troubleshooting guide created
- [ ] Support runbook updated

---

## Sign-off

### Deployment Approval
- [ ] Technical Lead: ___________________ Date: ___________
- [ ] Operations Manager: _______________ Date: ___________
- [ ] Product Owner: ___________________ Date: ___________

### Post-Deployment Review (After 7 Days)
- [ ] Success metrics reviewed
- [ ] Issues documented and resolved
- [ ] Performance optimizations identified
- [ ] Lessons learned documented

---

## Quick Commands Reference

```bash
# Check service status
pm2 status

# View logs
pm2 logs lockin --lines 100

# Restart service
pm2 restart lockin --update-env

# Test webhook
curl -X POST https://*************:3001/webhook/thrivecart \
  -H "Content-Type: application/json" \
  -d '{"event":"test"}'

# Database health check
PGPASSWORD=postgres psql -U postgres -h localhost -d lockin -c \
  "SELECT COUNT(*) as users FROM paid_users WHERE status = 'active';"

# Check recent errors
pm2 logs lockin --err --lines 50

# Monitor webhook events
watch -n 5 "psql -U postgres -d lockin -c 'SELECT event_type, created_at FROM webhook_events ORDER BY created_at DESC LIMIT 5;'"
```

---

## Support Contacts

- **ThriveCart Support**: <EMAIL>
- **Server Admin**: <EMAIL>
- **Development Team**: <EMAIL>
- **Emergency Contact**: [Phone Number]

---

**Document Version**: 1.0
**Last Updated**: August 2025
**Next Review**: September 2025