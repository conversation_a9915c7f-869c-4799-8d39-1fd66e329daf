#!/usr/bin/env node

require('dotenv').config();

async function checkActualCustomerEmail() {
  try {
    console.log('🔍 ANALYZING ACTUAL CUSTOMER EMAIL (ID 139)...');
    
    const pool = require('./src/db/connection');
    
    // Get the actual customer email record
    const result = await pool.query(`
      SELECT id, to_email, template, template_data, subject, status, created_at, sent_at
      FROM email_queue 
      WHERE id = 139
    `);
    
    if (!result.rows.length) {
      console.log('❌ Email ID 139 not found');
      return;
    }
    
    const emailRecord = result.rows[0];
    
    console.log('\n📧 ACTUAL CUSTOMER EMAIL:');
    console.log('ID:', emailRecord.id);
    console.log('Email:', emailRecord.to_email);
    console.log('Template:', emailRecord.template);
    console.log('Status:', emailRecord.status);
    console.log('Subject:', emailRecord.subject);
    console.log('Created:', emailRecord.created_at);
    console.log('Sent:', emailRecord.sent_at);
    
    // Parse template data
    const templateData = typeof emailRecord.template_data === 'string' 
      ? JSON.parse(emailRecord.template_data) 
      : emailRecord.template_data;
    
    console.log('\n📊 TEMPLATE DATA ANALYSIS:');
    console.log('Access codes count:', templateData.accessCodes?.length || 0);
    console.log('Has bumps:', templateData.hasBumps);
    console.log('Bumps count:', templateData.bumps?.length || 0);
    console.log('Subscription type:', templateData.subscription_type);
    console.log('Primary code:', templateData.primary_access_code);
    
    // Check which template should have been used
    const codeCount = templateData.accessCodes?.length || 1;
    const expectedTemplate = codeCount > 1 ? 'welcome_multi_code' : 'welcome_dynamic';
    
    console.log('\n🎯 TEMPLATE LOGIC:');
    console.log('Actual template used:', emailRecord.template);
    console.log('Expected template:', expectedTemplate);
    console.log('Template match:', emailRecord.template === expectedTemplate ? '✅ CORRECT' : '❌ WRONG!');
    
    // Generate content with the template that was actually used
    const emailService = require('./src/services/emailService');
    const templateFunc = emailService.templates[emailRecord.template];
    
    if (!templateFunc) {
      console.log('❌ Template function not found:', emailRecord.template);
      return;
    }
    
    console.log('\n⚡ GENERATING CONTENT WITH ACTUAL TEMPLATE...');
    
    const { subject, html, text } = templateFunc(templateData);
    
    console.log('Generated HTML length:', html.length);
    console.log('Generated text length:', text.length);
    
    // Check for step 5 and footer content
    const step5Index = html.indexOf('Start building unstoppable habits!');
    if (step5Index !== -1) {
      const afterStep5 = html.substring(step5Index + 'Start building unstoppable habits!'.length);
      console.log('Characters after step 5:', afterStep5.length);
      
      // Check footer components
      const footerChecks = [
        { name: 'Rich signature', check: afterStep5.includes("Let's Lock In") && afterStep5.includes('Rich') },
        { name: 'Affiliate section', check: afterStep5.includes('💰 Earn with Lock In') },
        { name: 'Social links', check: afterStep5.includes('@richvieren') },
        { name: 'Copyright', check: afterStep5.includes('© 2025 Lock In') }
      ];
      
      console.log('\n🔍 FOOTER CONTENT CHECK:');
      footerChecks.forEach(({ name, check }) => {
        console.log(`  ${check ? '✅' : '❌'} ${name}`);
      });
      
      const allFooterGood = footerChecks.every(({ check }) => check);
      console.log(`\n${allFooterGood ? '🎉 FOOTER IS COMPLETE IN TEMPLATE!' : '❌ FOOTER IS MISSING!'}`);
      
      if (allFooterGood) {
        console.log('\n🚨 CRITICAL FINDING:');
        console.log('The template generates COMPLETE content with footer.');
        console.log('But the user reports the actual email was truncated.');
        console.log('This suggests a DIFFERENT EMAIL was sent than what the template generates!');
      }
    } else {
      console.log('❌ Step 5 text not found in generated content!');
    }
    
    // Save both the template data and generated content for inspection
    require('fs').writeFileSync('/var/www/lockin/customer-139-template-data.json', JSON.stringify(templateData, null, 2));
    require('fs').writeFileSync('/var/www/lockin/customer-139-generated.html', html);
    
    console.log('\n💾 SAVED FILES:');
    console.log('- customer-139-template-data.json');
    console.log('- customer-139-generated.html');
    
    await pool.end();
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
}

checkActualCustomerEmail().catch(console.error);