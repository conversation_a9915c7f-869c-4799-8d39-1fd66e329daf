#!/usr/bin/env node

require('dotenv').config();
const axios = require('axios');
const crypto = require('crypto');

// Ensure DATABASE_URL is set
if (!process.env.DATABASE_URL) {
  process.env.DATABASE_URL = 'postgresql://postgres:postgres@localhost:5432/lockin';
}

const pool = require('./src/db/connection');

async function testCompleteFlow() {
  console.log('🚀 Testing Complete Purchase → Email → WhatsApp Flow');
  console.log('=' .repeat(50));

  const testEmail = `brevo-test-${Date.now()}@example.com`;
  const testPhone = '+12125551234';
  
  try {
    // Step 1: Simulate ThriveCart webhook
    console.log('\n1️⃣ Simulating ThriveCart monthly purchase...');
    
    const payload = {
      event: 'order.success',
      thrivecart_secret: process.env.THRIVECART_WEBHOOK_SECRET || 'FUQ2A97V0Q8A',
      customer_email: testEmail,
      customer_first_name: 'Test',
      customer_last_name: 'User',
      order_id: `ORDER-${Date.now()}`,
      order_total: '5.00',
      order_currency: 'USD',
      product_name: 'Habit Tracker Monthly',
      item_name: 'Habit Tracker Monthly'
    };

    const formData = new URLSearchParams(payload);

    const response = await axios.post('http://localhost:3001/webhook/thrivecart', formData, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
    });

    console.log('   ✅ Webhook processed successfully');
    
    // Step 2: Check if user was created with access code
    console.log('\n2️⃣ Checking database for new user...');
    
    const userResult = await pool.query(
      `SELECT pu.*, ac.code as access_code 
       FROM paid_users pu
       JOIN access_codes ac ON pu.access_code = ac.code
       WHERE pu.email = $1
       ORDER BY pu.created_at DESC
       LIMIT 1`,
      [testEmail]
    );

    if (userResult.rows.length === 0) {
      throw new Error('User not found in database');
    }

    const user = userResult.rows[0];
    console.log('   ✅ User created successfully');
    console.log(`   📧 Email: ${user.email}`);
    console.log(`   🔑 Access Code: ${user.access_code}`);
    console.log(`   💳 Plan: ${user.subscription_type}`);

    // Step 3: Check email queue
    console.log('\n3️⃣ Checking email queue...');
    
    const emailResult = await pool.query(
      `SELECT * FROM email_queue 
       WHERE to_email = $1
       ORDER BY created_at DESC
       LIMIT 1`,
      [testEmail]
    );

    if (emailResult.rows.length === 0) {
      throw new Error('Email not found in queue');
    }

    const email = emailResult.rows[0];
    console.log('   ✅ Email queued successfully');
    console.log(`   📬 Status: ${email.status}`);
    console.log(`   📝 Template: ${email.template}`);
    console.log(`   🕐 Created: ${email.created_at}`);
    
    if (email.status === 'sent' && email.sent_at) {
      console.log(`   ✉️ Sent at: ${email.sent_at}`);
    }

    // Step 4: Wait for email to be processed
    if (email.status === 'pending') {
      console.log('\n⏳ Waiting for email processor (up to 70 seconds)...');
      
      for (let i = 0; i < 14; i++) {
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        const checkResult = await pool.query(
          `SELECT status, sent_at, error_message FROM email_queue WHERE id = $1`,
          [email.id]
        );
        
        const currentStatus = checkResult.rows[0];
        
        if (currentStatus.status === 'sent') {
          console.log('\n   ✅ Email sent successfully via Brevo SMTP!');
          console.log(`   ✉️ Sent at: ${currentStatus.sent_at}`);
          break;
        } else if (currentStatus.status === 'failed') {
          console.log('\n   ❌ Email failed to send');
          console.log(`   Error: ${currentStatus.error_message}`);
          break;
        }
        
        process.stdout.write('.');
      }
    }

    // Step 5: Display WhatsApp activation instructions
    console.log('\n4️⃣ WhatsApp Bot Activation Instructions:');
    console.log('   📱 To activate the habit tracker:');
    console.log(`   1. Open WhatsApp`);
    console.log(`   2. Send a message to: +19035155547`);
    console.log(`   3. Type: START ${user.access_code}`);
    console.log(`   4. Follow the setup instructions`);

    // Final summary
    console.log('\n' + '=' .repeat(50));
    console.log('✅ COMPLETE FLOW TEST SUCCESSFUL!');
    console.log('=' .repeat(50));
    console.log('\n📊 Summary:');
    console.log(`   • ThriveCart webhook: ✅ Processed`);
    console.log(`   • User creation: ✅ Complete`);
    console.log(`   • Access code: ✅ Generated (${user.access_code})`);
    console.log(`   • Email queue: ✅ ${email.status}`);
    console.log(`   • Brevo SMTP: ✅ Configured`);
    console.log(`   • WhatsApp ready: ✅ Awaiting activation`);

    // Cleanup test data
    console.log('\n🧹 Cleaning up test data...');
    await pool.query('DELETE FROM email_queue WHERE to_email = $1', [testEmail]);
    await pool.query('DELETE FROM access_codes WHERE code = $1', [user.access_code]);
    await pool.query('DELETE FROM paid_users WHERE email = $1', [testEmail]);
    console.log('   ✅ Test data cleaned');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
    process.exit(1);
  } finally {
    await pool.end();
  }
}

testCompleteFlow();
