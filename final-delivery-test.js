#!/usr/bin/env node

require('dotenv').config();
const { createTransport } = require('nodemailer');

async function finalDeliveryTest() {
  console.log('🎯 FINAL BREVO DELIVERY TEST');
  console.log('=' .repeat(35));
  
  const transporter = createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT),
    secure: false,
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    }
  });
  
  // Test with multiple reliable test services
  const testEmails = [
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>',
    '<EMAIL>'
  ];
  
  console.log('\n📧 Sending to multiple test services:');
  
  for (const email of testEmails) {
    try {
      const result = await transporter.sendMail({
        from: `"Brevo Test" <${process.env.EMAIL_FROM}>`,
        to: email,
        subject: `Brevo Delivery Test - ${new Date().getHours()}:${new Date().getMinutes()}`,
        html: `
          <h1>🎯 Brevo Delivery Confirmation</h1>
          <p><strong>Time:</strong> ${new Date().toISOString()}</p>
          <p><strong>From:</strong> ${process.env.EMAIL_FROM}</p>
          <p><strong>SMTP:</strong> ${process.env.SMTP_HOST}:${process.env.SMTP_PORT}</p>
          <p>If you see this email, Brevo SMTP delivery is working!</p>
          <hr>
          <p style="font-size: 12px; color: #666;">
            Test ID: ${Date.now()}<br>
            Message ID: ${result?.messageId || 'pending'}
          </p>
        `,
        text: `Brevo Delivery Test\nTime: ${new Date().toISOString()}\nFrom: ${process.env.EMAIL_FROM}\nIf you see this, Brevo is working!`
      });
      
      console.log(`   ✅ ${email}: Queued (${result.response})`);
      
    } catch (error) {
      console.log(`   ❌ ${email}: ${error.message}`);
    }
  }
  
  console.log('\n🕐 Wait 2-3 minutes, then check:');
  console.log('   • https://10minutemail.com/');
  console.log('   • https://guerrillamail.com/');
  console.log('   • https://tempmail.org/');
  console.log('   • https://yopmail.com/en/inbox?login=brevo-test');
  
  console.log('\n🔍 DIAGNOSIS SUMMARY:');
  console.log('✅ SMTP Connection: Working');
  console.log('✅ Authentication: Successful');  
  console.log('✅ Email Queuing: Working');
  console.log('❓ Email Delivery: To be confirmed');
  
  console.log('\n🎯 ROOT CAUSE ANALYSIS:');
  console.log('If NO emails are delivered to ANY test service:');
  console.log('  → Brevo account issue (verification, quota, suspension)');
  console.log('  → Domain authentication required');
  console.log('  → Need to configure sender domain in Brevo');
  
  console.log('\nIf SOME emails are delivered:');
  console.log('  → Spam filtering by recipient providers');
  console.log('  → Domain reputation issues');
  
  console.log('\n💡 IMMEDIATE SOLUTION:');
  console.log('1. Use verified sender domain in Brevo dashboard');
  console.log('2. Or use Brevo\'s default domain: @smtp-brevo.com');
  console.log('3. Check Brevo account status and limits');
}

finalDeliveryTest().catch(console.error);