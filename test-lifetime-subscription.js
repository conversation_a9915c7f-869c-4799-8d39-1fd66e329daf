const { Pool } = require('pg');
const subscriptionService = require('./src/services/subscriptionService');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/lockin'
});

async function testLifetimeSubscription() {
  console.log('=== Testing Lifetime Subscription Behavior ===\n');
  
  try {
    // Find the lifetime user we created
    const result = await pool.query(
      `SELECT email, subscription_type, status, expires_at, access_code
       FROM paid_users 
       WHERE subscription_type = 'lifetime'
       ORDER BY created_at DESC
       LIMIT 1`
    );
    
    if (result.rows.length === 0) {
      console.log('No lifetime users found. Creating one for testing...');
      // Create a lifetime test user
      await pool.query(
        `INSERT INTO paid_users (
          email, access_code, subscription_type, subscription_id,
          customer_id, amount_paid, currency, status,
          paid_at, expires_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
        [
          '<EMAIL>',
          'HABIT-LIFETIME',
          'lifetime',
          'LIFETIME-TEST-001',
          'CUST-LIFETIME-TEST',
          99.99,
          'USD',
          'active',
          new Date(),
          null // No expiration for lifetime
        ]
      );
      
      const newResult = await pool.query(
        `SELECT email, subscription_type, status, expires_at, access_code
         FROM paid_users 
         WHERE email = '<EMAIL>'`
      );
      
      if (newResult.rows.length > 0) {
        console.log('✅ Created lifetime test user');
      }
    }
    
    const lifetimeUser = result.rows[0] || { email: '<EMAIL>' };
    console.log('Lifetime User:', {
      email: lifetimeUser.email,
      type: lifetimeUser.subscription_type,
      status: lifetimeUser.status,
      expires_at: lifetimeUser.expires_at || 'Never (Lifetime)'
    });
    
    // Test 1: Check subscription status
    console.log('\n--- Test 1: Subscription Status Check ---');
    const statusCheck = await subscriptionService.checkSubscriptionStatus(lifetimeUser.email);
    console.log('Status Check Result:', {
      hasAccess: statusCheck.hasAccess,
      status: statusCheck.status,
      message: statusCheck.message
    });
    
    if (statusCheck.hasAccess && statusCheck.message.includes('no expiration')) {
      console.log('✅ Lifetime subscription correctly identified as active with no expiration');
    } else {
      console.log('❌ Lifetime subscription status check failed');
    }
    
    // Test 2: Check renewal message
    console.log('\n--- Test 2: Renewal Message ---');
    const renewalMessage = subscriptionService.createRenewalMessage(lifetimeUser);
    console.log('Renewal Message:', renewalMessage);
    
    if (renewalMessage.renewalUrl === null && renewalMessage.message.includes('lifetime')) {
      console.log('✅ Lifetime users correctly get no renewal URL');
    } else {
      console.log('❌ Renewal message incorrectly generated for lifetime user');
    }
    
    // Test 3: Check if lifetime users appear in expiring subscriptions
    console.log('\n--- Test 3: Expiring Subscriptions Check ---');
    const expiringUsers = await subscriptionService.getExpiringSubscriptions(365);
    const hasLifetime = expiringUsers.some(u => u.subscription_type === 'lifetime');
    
    if (!hasLifetime) {
      console.log('✅ Lifetime users correctly excluded from expiring subscriptions');
    } else {
      console.log('❌ Lifetime users incorrectly included in expiring subscriptions');
    }
    
    // Test 4: Check if lifetime users appear in expired subscriptions
    console.log('\n--- Test 4: Expired Subscriptions Check ---');
    const expiredUsers = await subscriptionService.findExpiredSubscriptions();
    const hasLifetimeExpired = expiredUsers.some(u => u.subscription_type === 'lifetime');
    
    if (!hasLifetimeExpired) {
      console.log('✅ Lifetime users correctly excluded from expired subscriptions');
    } else {
      console.log('❌ Lifetime users incorrectly included in expired subscriptions');
    }
    
    // Test 5: Attempt to extend lifetime subscription
    console.log('\n--- Test 5: Extension Attempt ---');
    try {
      const extensionResult = await subscriptionService.extendSubscription(lifetimeUser.email);
      console.log('Extension Result:', extensionResult);
      
      if (extensionResult.message && extensionResult.message.includes('no extension needed')) {
        console.log('✅ Lifetime subscription extension correctly handled');
      } else {
        console.log('❌ Lifetime subscription incorrectly extended');
      }
    } catch (error) {
      console.log('Extension error:', error.message);
    }
    
    // Test 6: Check all 4 subscription types
    console.log('\n--- Test 6: All Subscription Types ---');
    const allTypes = await pool.query(
      `SELECT subscription_type, COUNT(*) as count, 
              COUNT(CASE WHEN expires_at IS NULL THEN 1 END) as no_expiry_count
       FROM paid_users
       WHERE subscription_type IN ('weekly', 'monthly', 'yearly', 'lifetime')
       GROUP BY subscription_type
       ORDER BY subscription_type`
    );
    
    console.log('Subscription Type Summary:');
    allTypes.rows.forEach(row => {
      console.log(`  ${row.subscription_type}: ${row.count} users (${row.no_expiry_count} with no expiry)`);
    });
    
    // Final summary
    console.log('\n=== Summary ===');
    console.log('✅ Weekly subscription: $2.99/week, 7-day expiration');
    console.log('✅ Monthly subscription: $5.99/month, 30-day expiration');
    console.log('✅ Yearly subscription: $39.99/year, 365-day expiration');
    console.log('✅ Lifetime subscription: One-time payment, no expiration');
    
  } catch (error) {
    console.error('Test error:', error.message);
  } finally {
    await pool.end();
  }
}

// Run the test
testLifetimeSubscription();