#!/usr/bin/env node

require('dotenv').config();
const thrivecartController = require('./src/controllers/thrivecartController');

async function simulateNewPurchase() {
  try {
    console.log('🛒 SIMULATING NEW CUSTOMER PURCHASE');
    console.log('='.repeat(60));
    
    // Create a mock request like ThriveCart would send
    const mockReq = {
      body: {
        // ThriveCart webhook payload for a yearly purchase
        thrivecart_account: process.env.THRIVECART_ACCOUNT,
        event: 'order.success',
        event_id: `TEST_${Date.now()}`,
        product_name: 'Lock In - Annual',
        product_permalink: 'lock-in-annual',
        customer_email: '<EMAIL>',
        customer_name: 'New Customer',
        customer_first_name: 'New',
        customer_last_name: 'Customer',
        order_total: '39.99',
        currency: 'USD',
        affiliate_id: 'test-affiliate-123',
        thrivecart_secret: process.env.THRIVECART_WEBHOOK_SECRET || 'test-secret',
        transaction_id: `TXN_${Date.now()}`,
        subscription_id: `SUB_${Date.now()}`,
        order_id: `ORDER_${Date.now()}`,
        created_at: new Date().toISOString()
      }
    };
    
    const mockRes = {
      status: (code) => ({
        json: (data) => {
          console.log(`Response ${code}:`, JSON.stringify(data, null, 2));
          return mockRes;
        },
        send: (data) => {
          console.log(`Response ${code}:`, data);
          return mockRes;
        }
      }),
      json: (data) => {
        console.log('Response:', JSON.stringify(data, null, 2));
        return mockRes;
      },
      send: (data) => {
        console.log('Response:', data);
        return mockRes;
      }
    };
    
    console.log('📧 Processing mock purchase...');
    await thrivecartController.handleWebhook(mockReq, mockRes);
    
    console.log('\n✅ Purchase processed! Check email queue for new entry.');
    
    // Check if email was queued
    const pool = require('./src/db/connection');
    const result = await pool.query('SELECT id, to_email, template FROM email_queue ORDER BY id DESC LIMIT 1');
    
    if (result.rows[0]?.to_email === '<EMAIL>') {
      console.log(`📧 New email queued: ID ${result.rows[0].id}, Template: ${result.rows[0].template}`);
      console.log('\n🎯 This email should have complete footer content after step 5');
    }
    
    await pool.end();
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
}

simulateNewPurchase().catch(console.error);