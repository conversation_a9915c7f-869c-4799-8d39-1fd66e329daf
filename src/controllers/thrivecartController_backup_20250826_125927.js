const crypto = require('crypto');
const pool = require('../db/connection');
const logger = require('../config/logger');
const emailService = require('../services/emailService');
const { generateAccessCode, generateAffiliateCode } = require('../utils/codeGenerator');

class ThriveCartController {
  constructor() {
    this.testMode = process.env.PAYMENT_TEST_MODE === 'true';
    this.webhookSecret = process.env.THRIVECART_WEBHOOK_SECRET;
  }

  /**
   * Main webhook handler for ThriveCart events
   */
  async handleWebhook(req, res) {
    try {
      const payload = req.body;
      
      // Log webhook event
      await this.logWebhookEvent(payload, req.headers);

      // Verify ThriveCart secret if provided
      if (!this.verifyThriveCartSecret(payload)) {
        logger.error('Invalid or missing ThriveCart secret');
        return res.status(401).json({ error: 'Invalid secret' });
      }

      // Process different event types
      const eventType = payload.event;
      logger.info('Processing ThriveCart webhook', { eventType, testMode: this.testMode });

      switch (eventType) {
        case 'order.success':
        case 'order.purchase':
          await this.handleOrderSuccess(payload);
          break;
        case 'order.subscription_payment':
        case 'order.rebill_success':
          await this.handleSubscriptionPayment(payload);
          break;
        case 'order.refund':
          await this.handleRefund(payload);
          break;
        case 'order.subscription_cancelled':
        case 'order.subscription_paused':
          await this.handleSubscriptionCancelled(payload);
          break;
        case 'order.subscription_resumed':
          await this.handleSubscriptionResumed(payload);
          break;
        default:
          logger.info('Unhandled ThriveCart event type', { eventType });
      }

      res.status(200).json({ received: true });
    } catch (error) {
      logger.error('ThriveCart webhook error', { error: error.message, stack: error.stack });
      res.status(500).json({ error: 'Webhook processing failed' });
    }
  }

  /**
   * Verify ThriveCart secret parameter
   */
  verifyThriveCartSecret(payload) {
    // ThriveCart sends the secret as a form parameter
    const providedSecret = payload.thrivecart_secret;
    
    // If no webhook secret is configured, skip verification (for development)
    if (!this.webhookSecret) {
      logger.warn('ThriveCart webhook secret not configured - skipping verification');
      return true;
    }
    
    // Verify the secret matches
    return providedSecret === this.webhookSecret;
  }

  /**
   * Legacy signature verification method (kept for backwards compatibility)
   */
  verifySignature(req) {
    // ThriveCart sends signature in X-TC-Hmac-SHA256 header
    const signature = req.headers['x-tc-hmac-sha256'];
    if (!signature) return false;

    const computedSignature = crypto
      .createHmac('sha256', this.webhookSecret)
      .update(JSON.stringify(req.body))
      .digest('hex');

    return computedSignature === signature;
  }

  /**
   * Handle successful order (new purchase)
   */
  async handleOrderSuccess(payload) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Extract customer and order data from ThriveCart payload
      // ThriveCart sends customer data in nested 'customer' object
      const customerData = payload.customer || {};
      const customer = {
        email: customerData.email || payload.email || payload.customer_email,
        customer_id: customerData.id || payload.customer_id,
        name: customerData.name || payload.customer_name || `${customerData.first_name || ''} ${customerData.last_name || ''}`.trim()
      };
      
      // Validate email is present
      if (!customer.email) {
        throw new Error('Customer email is required but was not provided in webhook payload');
      }
      
      // Extract order data - ThriveCart sends order data in nested 'order' object
      const orderData = payload.order || {};
      // ThriveCart sends total as cents (2999 = $29.99) or as string with decimal
      let totalAmount = 0;
      if (orderData.total_str) {
        // If total_str exists, it's already formatted as dollars (e.g., "29.99")
        totalAmount = parseFloat(orderData.total_str);
      } else if (orderData.total) {
        // If total exists as number, it's in cents
        totalAmount = parseFloat(orderData.total) / 100;
      } else {
        // Fallback to other fields
        totalAmount = parseFloat(payload.order_total || payload.total || 0);
      }
      
      const order = {
        order_id: orderData.id || payload.order_id || payload.transaction_id || `TC_${Date.now()}_${customer.email}`,
        total: totalAmount,
        currency: payload.currency || orderData.currency || 'USD'
      };
      
      // Extract subscription details from webhook
      const subscriptionDetails = this.extractSubscriptionDetails(payload);
      
      // Check if user already exists
      const existingUser = await client.query(
        'SELECT * FROM paid_users WHERE email = $1',
        [customer.email]
      );

      let paidUser;
      
      if (existingUser.rows.length > 0) {
        // Update existing user with new subscription details
        const updateResult = await client.query(
          `UPDATE paid_users 
           SET status = 'active',
               subscription_type = $1,
               amount_paid = $2,
               next_billing_date = $3,
               billing_frequency_days = $4,
               payment_plan_id = $5,
               payment_plan_name = $6,
               expires_at = $7,
               updated_at = NOW()
           WHERE email = $8
           RETURNING *`,
          [
            subscriptionDetails.subscriptionType,
            order.total,
            subscriptionDetails.nextBillingDate,
            subscriptionDetails.billingFrequencyDays,
            subscriptionDetails.paymentPlanId,
            subscriptionDetails.paymentPlanName,
            subscriptionDetails.expiresAt,
            customer.email
          ]
        );
        paidUser = updateResult.rows[0];
        
        logger.info('Updated existing paid user with subscription details', {
          email: customer.email,
          subscriptionType: subscriptionDetails.subscriptionType,
          expiresAt: subscriptionDetails.expiresAt
        });
      } else {
        // Generate unique access code
        const accessCode = await generateAccessCode();
        
        // Generate affiliate code for yearly subscribers
        const affiliateCode = subscriptionDetails.subscriptionType === 'yearly' ? await generateAffiliateCode() : null;

        // Create new paid user record with full subscription details
        const paidUserResult = await client.query(
          `INSERT INTO paid_users (
            email, access_code, subscription_type, subscription_id,
            customer_id, amount_paid, currency, status,
            is_affiliate, affiliate_code, paid_at, expires_at,
            next_billing_date, billing_frequency_days,
            payment_plan_id, payment_plan_name,
            fastspring_order_id, test_mode
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
          RETURNING *`,
          [
            customer.email,
            accessCode,
            subscriptionDetails.subscriptionType,
            order.order_id,
            customer.customer_id || customer.email,
            order.total,
            order.currency || 'USD',
            'active',
            subscriptionDetails.subscriptionType === 'yearly',
            affiliateCode,
            new Date(),
            subscriptionDetails.expiresAt,
            subscriptionDetails.nextBillingDate,
            subscriptionDetails.billingFrequencyDays,
            subscriptionDetails.paymentPlanId,
            subscriptionDetails.paymentPlanName,
            order.order_id,
            this.testMode
          ]
        );

        paidUser = paidUserResult.rows[0];

        // Create access code record
        await client.query(
          `INSERT INTO access_codes (code, paid_user_id, is_active)
           VALUES ($1, $2, $3)`,
          [accessCode, paidUser.id, true]
        );

        logger.info('Created new paid user from ThriveCart order', {
          email: customer.email,
          subscriptionType: subscriptionDetails.subscriptionType,
          orderId: order.order_id,
          expiresAt: subscriptionDetails.expiresAt
        });
      }

      // Log transaction
      await client.query(
        `INSERT INTO payment_transactions (
          paid_user_id, transaction_id, type, amount, currency,
          fastspring_order_id, transaction_date
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [
          paidUser.id,
          order.order_id,
          'payment',
          order.total,
          order.currency || 'USD',
          order.order_id,
          new Date()
        ]
      );

      // Queue welcome email for new users
      if (existingUser.rows.length === 0) {
        await this.queueWelcomeEmail(paidUser);
      }

      // Mark webhook as processed
      await this.markWebhookProcessed(payload);

      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error processing ThriveCart order', { error: error.message });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Handle subscription payment (renewal)
   */
  async handleSubscriptionPayment(payload) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      const customerData = payload.customer || {};
      const customer = {
        email: customerData.email || payload.email || payload.customer_email
      };
      
      // Validate email is present
      if (!customer.email) {
        logger.error('Customer email missing in subscription payment', { payload });
        throw new Error('Customer email is required');
      }
      
      const orderData = payload.order || {};
      let totalAmount = 0;
      if (orderData.total_str) {
        totalAmount = parseFloat(orderData.total_str);
      } else if (orderData.total) {
        totalAmount = parseFloat(orderData.total) / 100;
      } else {
        totalAmount = parseFloat(payload.order_total || payload.total || 0);
      }
      
      const order = {
        order_id: orderData.id || payload.order_id || payload.transaction_id || `TC_${Date.now()}_${customer.email}`,
        total: totalAmount,
        currency: payload.currency || orderData.currency || 'USD'
      };
      
      // Extract subscription details for renewal
      const subscriptionDetails = this.extractSubscriptionDetails(payload);
      
      // Update subscription status and extend expiration
      await client.query(
        `UPDATE paid_users 
         SET status = 'active',
             expires_at = $1,
             next_billing_date = $2,
             updated_at = NOW()
         WHERE email = $3`,
        [subscriptionDetails.expiresAt, subscriptionDetails.nextBillingDate, customer.email]
      );

      // Get user ID for transaction logging
      const userResult = await client.query(
        'SELECT id, subscription_type FROM paid_users WHERE email = $1',
        [customer.email]
      );

      if (userResult.rows[0]) {
        // Log transaction that extends expiration
        await client.query(
          `INSERT INTO payment_transactions (
            paid_user_id, transaction_id, type, amount, currency,
            fastspring_order_id, transaction_date, extends_expiration
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)`,
          [
            userResult.rows[0].id,
            order.order_id,
            'payment',
            order.total,
            order.currency || 'USD',
            order.order_id,
            new Date(),
            true
          ]
        );
      }

      await client.query('COMMIT');
      
      logger.info('Subscription payment processed - access extended', {
        email: customer.email,
        amount: order.total,
        newExpiresAt: subscriptionDetails.expiresAt,
        subscriptionType: subscriptionDetails.subscriptionType
      });
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error handling subscription payment', { error: error.message });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Handle refund
   */
  async handleRefund(payload) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      const customerData = payload.customer || {};
      const customer = {
        email: customerData.email || payload.email || payload.customer_email
      };
      
      if (!customer.email) {
        logger.error('Customer email missing in refund', { payload });
        throw new Error('Customer email is required');
      }
      
      const orderData = payload.order || {};
      let totalAmount = 0;
      if (orderData.total_str) {
        totalAmount = parseFloat(orderData.total_str);
      } else if (orderData.total) {
        totalAmount = parseFloat(orderData.total) / 100;
      } else {
        totalAmount = parseFloat(payload.order_total || payload.total || 0);
      }
      
      const order = {
        order_id: orderData.id || payload.order_id || payload.transaction_id || `TC_${Date.now()}_${customer.email}`,
        total: totalAmount,
        currency: payload.currency || orderData.currency || 'USD'
      };
      
      // Update user status
      await client.query(
        `UPDATE paid_users 
         SET status = 'expired',
             updated_at = NOW()
         WHERE email = $1`,
        [customer.email]
      );

      // Get user ID
      const userResult = await client.query(
        'SELECT id FROM paid_users WHERE email = $1',
        [customer.email]
      );

      if (userResult.rows[0]) {
        // Log refund transaction
        await client.query(
          `INSERT INTO payment_transactions (
            paid_user_id, transaction_id, type, amount, currency,
            fastspring_order_id, transaction_date
          ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
          [
            userResult.rows[0].id,
            order.order_id,
            'refund',
            -Math.abs(order.total),
            order.currency || 'USD',
            order.order_id,
            new Date()
          ]
        );

        // Deactivate access code
        await client.query(
          `UPDATE access_codes 
           SET is_active = false 
           WHERE paid_user_id = $1`,
          [userResult.rows[0].id]
        );
      }

      await client.query('COMMIT');
      
      logger.info('Refund processed', {
        email: customer.email,
        amount: order.total
      });
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error handling refund', { error: error.message });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Handle subscription cancellation
   */
  async handleSubscriptionCancelled(payload) {
    try {
      const customerData = payload.customer || {};
      const customer = {
        email: customerData.email || payload.email || payload.customer_email
      };
      
      if (!customer.email) {
        logger.error('Customer email missing in cancellation', { payload });
        return; // Don't throw, just log and return
      }
      
      await pool.query(
        `UPDATE paid_users 
         SET status = 'cancelled',
             expires_at = NOW(),
             updated_at = NOW()
         WHERE email = $1`,
        [customer.email]
      );

      logger.info('Subscription cancelled', {
        email: customer.email
      });
    } catch (error) {
      logger.error('Error handling subscription cancellation', { error: error.message });
      throw error;
    }
  }

  /**
   * Handle subscription resumption
   */
  async handleSubscriptionResumed(payload) {
    try {
      const customerData = payload.customer || {};
      const customer = {
        email: customerData.email || payload.email || payload.customer_email
      };
      
      if (!customer.email) {
        logger.error('Customer email missing in resumption', { payload });
        return;
      }
      
      await pool.query(
        `UPDATE paid_users 
         SET status = 'active',
             expires_at = NULL,
             updated_at = NOW()
         WHERE email = $1`,
        [customer.email]
      );

      logger.info('Subscription resumed', {
        email: customer.email
      });
    } catch (error) {
      logger.error('Error handling subscription resumption', { error: error.message });
      throw error;
    }
  }

  /**
   * Determine subscription type from ThriveCart data
   */
  getSubscriptionType(payload) {
    // First try to get from payment plan name
    if (payload.order?.charges?.[0]?.payment_plan_name) {
      const planName = payload.order.charges[0].payment_plan_name.toLowerCase();
      
      // Check for lifetime/founder first
      if (planName.includes('lifetime') || planName.includes('founder')) {
        return 'lifetime';
      } else if (planName.includes('annual') || planName.includes('year')) {
        return 'yearly';
      } else if (planName.includes('month')) {
        return 'monthly';
      } else if (planName.includes('week')) {
        return 'weekly';
      }
    }
    
    // Check product name for lifetime
    const productName = payload.product_name || payload.item_name || '';
    const productNameLower = productName.toLowerCase();
    
    if (productNameLower.includes('lifetime') || productNameLower.includes('founder')) {
      return 'lifetime';
    }
    
    // Try to get from future charges frequency
    if (payload.order?.future_charges?.[0]?.frequency) {
      const frequency = payload.order.future_charges[0].frequency.toLowerCase();
      
      if (frequency === 'year') {
        return 'yearly';
      } else if (frequency === 'month') {
        return 'monthly';
      } else if (frequency === 'week') {
        return 'weekly';
      }
    }
    
    // Try to determine from frequency_days
    if (payload.order?.future_charges?.[0]?.frequency_days) {
      const days = parseInt(payload.order.future_charges[0].frequency_days);
      
      if (days >= 360) {
        return 'yearly';
      } else if (days >= 28 && days <= 31) {
        return 'monthly';
      } else if (days === 7) {
        return 'weekly';
      }
    }
    
    // Fallback to additional product name checking
    if (productNameLower.includes('year') || productNameLower.includes('annual')) {
      return 'yearly';
    } else if (productNameLower.includes('month')) {
      return 'monthly';
    } else if (productNameLower.includes('week') || productNameLower.includes('7 day') || productNameLower.includes('7-day')) {
      return 'weekly';
    }
    
    // Default to monthly if we can't determine
    logger.warn('Could not determine subscription type from webhook data, defaulting to monthly', {
      planName: payload.order?.charges?.[0]?.payment_plan_name,
      frequency: payload.order?.future_charges?.[0]?.frequency,
      productName
    });
    return 'monthly';
  }

  /**
   * Extract subscription details from ThriveCart webhook
   */
  extractSubscriptionDetails(payload) {
    const details = {
      subscriptionType: this.getSubscriptionType(payload),
      nextBillingDate: null,
      billingFrequencyDays: null,
      paymentPlanId: null,
      paymentPlanName: null,
      expiresAt: null
    };
    
    // Extract from charges
    if (payload.order?.charges?.[0]) {
      const charge = payload.order.charges[0];
      details.paymentPlanId = charge.payment_plan_id || null;
      details.paymentPlanName = charge.payment_plan_name || null;
    }
    
    // Check if it's a lifetime subscription - no expiration
    if (details.subscriptionType === 'lifetime') {
      // Lifetime subscriptions never expire
      details.expiresAt = null;
      details.nextBillingDate = null;
      details.billingFrequencyDays = null;
      return details;
    }
    
    // Extract from future charges for non-lifetime subscriptions
    if (payload.order?.future_charges?.[0]) {
      const futureCharge = payload.order.future_charges[0];
      
      // Parse next billing date
      if (futureCharge.due) {
        try {
          details.nextBillingDate = new Date(futureCharge.due);
        } catch (e) {
          logger.error('Failed to parse next billing date', { due: futureCharge.due });
        }
      }
      
      // Get frequency in days
      if (futureCharge.frequency_days) {
        details.billingFrequencyDays = parseInt(futureCharge.frequency_days);
      } else {
        // Calculate from frequency string
        const frequency = (futureCharge.frequency || '').toLowerCase();
        if (frequency === 'year') {
          details.billingFrequencyDays = 365;
        } else if (frequency === 'month') {
          details.billingFrequencyDays = 30;
        } else if (frequency === 'week') {
          details.billingFrequencyDays = 7;
        }
      }
    }
    
    // Calculate expiration date (next billing date or calculated from frequency)
    if (details.nextBillingDate) {
      details.expiresAt = details.nextBillingDate;
    } else if (details.billingFrequencyDays) {
      const expirationDate = new Date();
      expirationDate.setDate(expirationDate.getDate() + details.billingFrequencyDays);
      details.expiresAt = expirationDate;
    } else {
      // Fallback: calculate based on subscription type
      const expirationDate = new Date();
      if (details.subscriptionType === 'weekly') {
        expirationDate.setDate(expirationDate.getDate() + 7);
        details.billingFrequencyDays = 7;
      } else if (details.subscriptionType === 'monthly') {
        expirationDate.setDate(expirationDate.getDate() + 30);
        details.billingFrequencyDays = 30;
      } else if (details.subscriptionType === 'yearly') {
        expirationDate.setDate(expirationDate.getDate() + 365);
        details.billingFrequencyDays = 365;
      }
      details.expiresAt = expirationDate;
    }
    
    return details;
  }

  /**
   * Queue welcome email for new subscriber
   */
  async queueWelcomeEmail(paidUser) {
    try {
      // Get pricing info based on subscription type
      const pricingInfo = this.getPricingInfo(paidUser.subscription_type, paidUser.amount_paid);
      
      const templateData = {
        accessCode: paidUser.access_code,
        subscriptionType: paidUser.subscription_type,
        subscriptionTypeDisplay: this.getSubscriptionTypeDisplay(paidUser.subscription_type),
        pricingText: pricingInfo.text,
        amountPaid: paidUser.amount_paid,
        affiliateCode: paidUser.affiliate_code,
        botPhone: process.env.TWILIO_PHONE_NUMBER || '+19035155547',
        isAffiliate: paidUser.subscription_type === 'yearly',
        isLifetime: paidUser.subscription_type === 'lifetime'
      };

      // Use a single dynamic template for all subscription types
      // Pass NULL for subject to use the template's subject
      await pool.query(
        `INSERT INTO email_queue (to_email, subject, template, template_data, priority)
         VALUES ($1, $2, $3, $4, $5)`,
        [
          paidUser.email,
          null,  // Let the email template provide the subject
          'welcome_dynamic',
          JSON.stringify(templateData),
          1
        ]
      );

      // Process email queue immediately
      await emailService.processQueue();
    } catch (error) {
      logger.error('Error queueing welcome email', { error: error.message });
    }
  }

  /**
   * Get pricing information for subscription type
   */
  getPricingInfo(subscriptionType, amountPaid) {
    const pricing = {
      weekly: { text: '$2.99/week', display: 'Weekly' },
      monthly: { text: '$5.99/month', display: 'Monthly' },
      yearly: { text: '$39.99/year', display: 'Yearly' },
      lifetime: { text: 'One-time payment', display: 'Lifetime' }
    };

    // Use actual amount paid if available
    if (amountPaid && subscriptionType !== 'lifetime') {
      const frequency = subscriptionType === 'weekly' ? 'week' : 
                      subscriptionType === 'monthly' ? 'month' : 'year';
      return {
        text: `$${amountPaid}/${frequency}`,
        display: pricing[subscriptionType]?.display || subscriptionType
      };
    }

    return pricing[subscriptionType] || { text: 'Subscription', display: subscriptionType };
  }

  /**
   * Get display text for subscription type
   */
  getSubscriptionTypeDisplay(subscriptionType) {
    const displays = {
      weekly: 'Weekly',
      monthly: 'Monthly', 
      yearly: 'Yearly',
      lifetime: 'Lifetime'
    };
    return displays[subscriptionType] || subscriptionType.charAt(0).toUpperCase() + subscriptionType.slice(1);
  }

  /**
   * Log webhook event for auditing
   */
  async logWebhookEvent(payload, headers) {
    try {
      await pool.query(
        `INSERT INTO webhook_events (event_type, payload, headers, source, test_mode)
         VALUES ($1, $2, $3, $4, $5)`,
        [
          payload.event || 'unknown',
          JSON.stringify(payload),
          JSON.stringify(headers),
          'thrivecart',
          this.testMode
        ]
      );
    } catch (error) {
      logger.error('Error logging webhook event', { error: error.message });
    }
  }

  /**
   * Mark webhook as processed
   */
  async markWebhookProcessed(payload) {
    try {
      const eventId = payload.order?.order_id || payload.event_id;
      await pool.query(
        `UPDATE webhook_events 
         SET processed = true, processed_at = NOW()
         WHERE payload->>'order_id' = $1 OR event_id = $1`,
        [eventId]
      );
    } catch (error) {
      logger.error('Error marking webhook processed', { error: error.message });
    }
  }

  /**
   * Test endpoint to simulate webhook events
   */
  async testWebhook(req, res) {
    if (!this.testMode) {
      return res.status(403).json({ error: 'Test mode not enabled' });
    }

    try {
      const { email, subscriptionType = 'monthly' } = req.body;

      const testPayload = {
        event: 'order.success',
        customer: {
          customer_id: `CUST-${Date.now()}`,
          email: email || '<EMAIL>',
          name: 'Test User'
        },
        order: {
          order_id: `TEST-${Date.now()}`,
          total: subscriptionType === 'yearly' ? 30.00 : 5.00,
          currency: 'USD'
        },
        product: {
          product_name: subscriptionType === 'yearly' ? 'Habit Tracker Yearly' : 'Habit Tracker Monthly'
        }
      };

      // Process as regular webhook
      req.body = testPayload;
      await this.handleWebhook(req, res);
    } catch (error) {
      logger.error('Test webhook error', { error: error.message });
      res.status(500).json({ error: error.message });
    }
  }
}

module.exports = new ThriveCartController();