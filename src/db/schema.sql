-- WhatsApp Habit Tracker Database Schema
-- Production-ready with constraints, indexes, and GDPR compliance

-- Drop existing tables if they exist (for clean setup)
DROP TABLE IF EXISTS audit_logs CASCADE;
DROP TABLE IF EXISTS habit_logs CASCADE;
DROP TABLE IF EXISTS habits CASCADE;
DROP TABLE IF EXISTS access_codes CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- Users table (Enhanced with constraints and timezone support)
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  phone VARCHAR(20) UNIQUE NOT NULL CHECK (phone ~ '^\+[1-9]\d{1,14}$'),  -- E.164 format enforcement
  display_name VARCHAR(50),
  status VARCHAR(20) DEFAULT 'LOCKED' CHECK (status IN ('LOCKED', 'ONBOARDING', 'ACTIVE', 'PAUSED')),
  timezone VARCHAR(50) DEFAULT 'UTC',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Payment/Access
  is_unlocked BOOLEAN DEFAULT FALSE,
  access_code VARCHAR(10),
  paid_at TIMESTAMPTZ,
  
  -- Preferences (GDPR compliance)
  marketing_consent BOOLEAN DEFAULT FALSE,
  analytics_consent BOOLEAN DEFAULT TRUE,
  opted_out_at TIMESTAMPTZ,  -- Track opt-out for compliance
  
  -- Session Management
  current_state VARCHAR(50) DEFAULT 'MAIN_MENU',
  session_context JSONB DEFAULT '{}',
  last_active TIMESTAMPTZ DEFAULT NOW()
);

-- Habits table
CREATE TABLE habits (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  habit_number INTEGER NOT NULL CHECK (habit_number BETWEEN 1 AND 5),
  habit_name VARCHAR(100) NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id, habit_number)
);

-- Daily habit logs
CREATE TABLE habit_logs (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
  habit_id INTEGER REFERENCES habits(id) ON DELETE CASCADE,
  log_date DATE NOT NULL,
  completed BOOLEAN NOT NULL,
  logged_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id, habit_id, log_date)
);

-- System audit log (Privacy-compliant - no phone numbers)
CREATE TABLE audit_logs (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id),  -- Use user_id instead of phone for privacy
  event_type VARCHAR(50) NOT NULL,
  event_data JSONB,
  timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Access codes for redemption
CREATE TABLE access_codes (
  id SERIAL PRIMARY KEY,
  code VARCHAR(10) UNIQUE NOT NULL,
  used_by VARCHAR(20),
  used_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ NOT NULL
);

-- Performance indexes for common queries
CREATE INDEX idx_habit_logs_user_date ON habit_logs(user_id, log_date);
CREATE INDEX idx_habits_user ON habits(user_id);
CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_last_active ON users(last_active);
CREATE INDEX idx_access_codes_code ON access_codes(code);
CREATE INDEX idx_access_codes_expires ON access_codes(expires_at);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers to auto-update updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_habits_updated_at BEFORE UPDATE ON habits
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();