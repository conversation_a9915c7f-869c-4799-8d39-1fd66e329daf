const { Pool } = require('pg');
const logger = require('../config/logger');

// Create connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
});

// Log pool errors
pool.on('error', (err, client) => {
  logger.error('Unexpected error on idle database client', { error: err.message });
});

// Test connection on startup
pool.connect((err, client, release) => {
  if (err) {
    logger.error('Error acquiring database client', { error: err.message });
  } else {
    logger.info('Database connection pool established successfully');
    release();
  }
});

module.exports = pool;