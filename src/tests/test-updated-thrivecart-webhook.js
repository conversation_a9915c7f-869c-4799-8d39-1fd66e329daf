#!/usr/bin/env node

/**
 * Test script for updated ThriveCart webhook integration with 4 separate products and bump orders
 */

const axios = require('axios');
const { testPayloads, generateTestPayload, scenarios } = require('./thrivecart-webhook-payloads');

const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3001';

class ThriveCartWebhookTester {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      errors: []
    };
  }

  async testWebhook(name, payload, expectedCodes = null) {
    try {
      console.log(`\n🧪 Testing: ${name}`);
      console.log(`📧 Email: ${payload.customer?.email || 'N/A'}`);
      console.log(`💰 Total: $${payload.order?.total_str || payload.order?.total / 100 || 'N/A'}`);
      console.log(`🎯 Event: ${payload.event}`);
      
      if (expectedCodes !== null) {
        console.log(`🔑 Expected codes: ${expectedCodes}`);
      }

      const response = await axios.post(`${BASE_URL}/webhook/thrivecart`, payload, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      if (response.status === 200) {
        console.log(`✅ ${name} - SUCCESS`);
        this.results.passed++;
        return { success: true, data: response.data };
      } else {
        console.log(`❌ ${name} - FAILED (Status: ${response.status})`);
        this.results.failed++;
        this.results.errors.push({ test: name, error: `HTTP ${response.status}` });
        return { success: false, error: `HTTP ${response.status}` };
      }
    } catch (error) {
      console.log(`❌ ${name} - ERROR: ${error.message}`);
      this.results.failed++;
      this.results.errors.push({ 
        test: name, 
        error: error.response?.data || error.message 
      });
      return { success: false, error: error.message };
    }
  }

  async testDatabaseState(email, expectedState) {
    try {
      // This would require a database check endpoint - for now just log
      console.log(`🔍 Should verify database state for ${email}:`, expectedState);
      return true;
    } catch (error) {
      console.log(`❌ Database check failed: ${error.message}`);
      return false;
    }
  }

  async runBasicTests() {
    console.log('\n🚀 Running Basic Product Tests');
    console.log('=' .repeat(60));

    // Test each product type
    const basicTests = [
      'annual_basic',
      'monthly_basic', 
      'weekly_basic',
      'lifetime_basic'
    ];

    for (const testName of basicTests) {
      await this.testWebhook(testName, testPayloads[testName], 1);
      await this.sleep(1000); // 1 second between tests
    }
  }

  async runBumpTests() {
    console.log('\n🎁 Running Bump Order Tests');
    console.log('=' .repeat(60));

    const bumpTests = [
      { name: 'annual_friend_bump', expectedCodes: 2 },
      { name: 'annual_tribe_bump', expectedCodes: 4 },
      { name: 'annual_both_bumps', expectedCodes: 5 }
    ];

    for (const test of bumpTests) {
      await this.testWebhook(test.name, testPayloads[test.name], test.expectedCodes);
      await this.sleep(1000);
    }
  }

  async runLifecycleTests() {
    console.log('\n🔄 Running Subscription Lifecycle Tests');
    console.log('=' .repeat(60));

    const lifecycleTests = [
      { name: 'annual_renewal', expectedCodes: 0 },
      { name: 'monthly_renewal', expectedCodes: 0 },
      { name: 'annual_refund', expectedCodes: 0 },
      { name: 'annual_cancellation', expectedCodes: 0 },
      { name: 'annual_resumption', expectedCodes: 0 }
    ];

    for (const test of lifecycleTests) {
      await this.testWebhook(test.name, testPayloads[test.name], test.expectedCodes);
      await this.sleep(1000);
    }
  }

  async runCustomTests() {
    console.log('\n⚡ Running Custom Generated Tests');
    console.log('=' .repeat(60));

    // Generate custom test scenarios
    const customTests = [
      {
        name: 'Custom Annual + Friend',
        payload: generateTestPayload('annual', {
          email: '<EMAIL>',
          includeFriendBump: true
        }),
        expectedCodes: 2
      },
      {
        name: 'Custom Annual + Tribe',
        payload: generateTestPayload('annual', {
          email: '<EMAIL>',
          includeTribeBump: true
        }),
        expectedCodes: 4
      },
      {
        name: 'Custom Annual + Both',
        payload: generateTestPayload('annual', {
          email: '<EMAIL>',
          includeFriendBump: true,
          includeTribeBump: true
        }),
        expectedCodes: 5
      }
    ];

    for (const test of customTests) {
      await this.testWebhook(test.name, test.payload, test.expectedCodes);
      await this.sleep(1000);
    }
  }

  async runErrorTests() {
    console.log('\n⚠️ Running Error Scenario Tests');
    console.log('=' .repeat(60));

    // Test with invalid data
    const errorTests = [
      {
        name: 'Missing Customer Email',
        payload: {
          ...testPayloads.annual_basic,
          customer: { ...testPayloads.annual_basic.customer, email: null }
        }
      },
      {
        name: 'Invalid Product Type',
        payload: {
          ...testPayloads.annual_basic,
          product_name: 'Unknown Product'
        }
      },
      {
        name: 'Missing Order Data',
        payload: {
          ...testPayloads.annual_basic,
          order: null
        }
      }
    ];

    for (const test of errorTests) {
      const result = await this.testWebhook(test.name, test.payload);
      // Error tests should fail gracefully, not crash
      if (result.success) {
        console.log(`⚠️  ${test.name} should have failed but didn't`);
      }
      await this.sleep(1000);
    }
  }

  async checkServerHealth() {
    try {
      console.log('\n❤️ Checking Server Health');
      const response = await axios.get(`${BASE_URL}/health`, { timeout: 5000 });
      console.log(`✅ Server is healthy: ${response.data.status}`);
      return true;
    } catch (error) {
      console.log(`❌ Server health check failed: ${error.message}`);
      return false;
    }
  }

  async checkTestMode() {
    console.log('\n🔧 Environment Check');
    console.log(`Base URL: ${BASE_URL}`);
    console.log(`Test Mode: ${process.env.PAYMENT_TEST_MODE || 'not set'}`);
    
    if (process.env.PAYMENT_TEST_MODE !== 'true') {
      console.log('⚠️  Warning: PAYMENT_TEST_MODE is not set to true');
      console.log('Set PAYMENT_TEST_MODE=true in your .env file to enable test endpoints');
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  printSummary() {
    console.log('\n📊 Test Results Summary');
    console.log('=' .repeat(60));
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`📈 Success Rate: ${((this.results.passed / (this.results.passed + this.results.failed)) * 100).toFixed(1)}%`);
    
    if (this.results.errors.length > 0) {
      console.log('\n❌ Failed Tests:');
      this.results.errors.forEach(error => {
        console.log(`  • ${error.test}: ${error.error}`);
      });
    }
    
    console.log('\n✨ Testing Complete!');
  }

  async runAllTests() {
    console.log('🚀 ThriveCart Webhook Integration Tests');
    console.log('Updated for 4 Products + Bump Orders');
    console.log('=' .repeat(60));

    await this.checkTestMode();

    const isHealthy = await this.checkServerHealth();
    if (!isHealthy) {
      console.log('❌ Server is not healthy. Aborting tests.');
      return;
    }

    await this.runBasicTests();
    await this.runBumpTests();
    await this.runLifecycleTests();
    await this.runCustomTests();
    await this.runErrorTests();

    this.printSummary();
  }
}

// Specific test functions for individual scenarios
async function testSingleScenario(scenarioName) {
  const tester = new ThriveCartWebhookTester();
  
  if (testPayloads[scenarioName]) {
    await tester.checkServerHealth();
    await tester.testWebhook(scenarioName, testPayloads[scenarioName]);
    tester.printSummary();
  } else {
    console.log(`❌ Scenario '${scenarioName}' not found`);
    console.log('Available scenarios:');
    Object.keys(testPayloads).forEach(key => {
      console.log(`  - ${key}`);
    });
  }
}

async function generateAndTestCustom(productType, options = {}) {
  const tester = new ThriveCartWebhookTester();
  
  try {
    const payload = generateTestPayload(productType, options);
    await tester.checkServerHealth();
    await tester.testWebhook(`Custom ${productType}`, payload);
    tester.printSummary();
  } catch (error) {
    console.log(`❌ Error generating test payload: ${error.message}`);
  }
}

// CLI interface
async function runCLI() {
  const args = process.argv.slice(2);
  const command = args[0];

  switch (command) {
    case 'all':
      new ThriveCartWebhookTester().runAllTests();
      break;
    case 'basic':
      {
        const tester = new ThriveCartWebhookTester();
        await tester.runBasicTests();
        tester.printSummary();
      }
      break;
    case 'bumps':
      {
        const tester = new ThriveCartWebhookTester();
        await tester.runBumpTests();
        tester.printSummary();
      }
      break;
    case 'lifecycle':
      {
        const tester = new ThriveCartWebhookTester();
        await tester.runLifecycleTests();
        tester.printSummary();
      }
      break;
    case 'single':
      testSingleScenario(args[1]);
      break;
    case 'custom':
      generateAndTestCustom(args[1], {
        includeFriendBump: args.includes('--friend'),
        includeTribeBump: args.includes('--tribe'),
        email: args.find(arg => arg.startsWith('--email='))?.split('=')[1]
      });
      break;
    default:
      console.log('ThriveCart Webhook Tester');
      console.log('Usage:');
      console.log('  node test-updated-thrivecart-webhook.js all           # Run all tests');
      console.log('  node test-updated-thrivecart-webhook.js basic         # Run basic product tests');
      console.log('  node test-updated-thrivecart-webhook.js bumps         # Run bump order tests');
      console.log('  node test-updated-thrivecart-webhook.js lifecycle     # Run subscription lifecycle tests');
      console.log('  node test-updated-thrivecart-webhook.js single <name> # Run single test');
      console.log('  node test-updated-thrivecart-webhook.js custom annual --friend --tribe # Custom test');
      console.log('');
      console.log('Available single test names:');
      Object.keys(testPayloads).forEach(key => {
        console.log(`  - ${key}`);
      });
      new ThriveCartWebhookTester().runAllTests();
  }
}

if (require.main === module) {
  runCLI().catch(console.error);
}

module.exports = {
  ThriveCartWebhookTester,
  testSingleScenario,
  generateAndTestCustom
};