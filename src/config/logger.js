const winston = require('winston');
const path = require('path');

// Custom format that excludes PII
const sanitizeFormat = winston.format.printf(({ timestamp, level, message, ...meta }) => {
  // Remove any phone numbers from logs (GDPR compliance)
  let sanitizedMessage = message;
  if (typeof sanitizedMessage === 'string') {
    sanitizedMessage = sanitizedMessage.replace(/\+?[1-9]\d{1,14}/g, '[PHONE_REDACTED]');
  }
  
  // Remove any potential PII from meta
  const sanitizedMeta = { ...meta };
  if (sanitizedMeta.phone) {
    sanitizedMeta.phone = '[REDACTED]';
  }
  if (sanitizedMeta.user && sanitizedMeta.user.phone) {
    sanitizedMeta.user.phone = '[REDACTED]';
  }
  
  return `${timestamp} [${level}]: ${sanitizedMessage} ${Object.keys(sanitizedMeta).length ? JSON.stringify(sanitizedMeta) : ''}`;
});

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    sanitizeFormat
  ),
  transports: [
    // Console transport
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        sanitizeFormat
      )
    })
  ]
});

// Add file transport in production
if (process.env.NODE_ENV === 'production') {
  logger.add(new winston.transports.File({
    filename: path.join(process.env.LOG_DIR || '/var/log', 'error.log'),
    level: 'error',
    maxsize: 5242880, // 5MB
    maxFiles: 5
  }));
  
  logger.add(new winston.transports.File({
    filename: path.join(process.env.LOG_DIR || '/var/log', 'combined.log'),
    maxsize: 5242880, // 5MB
    maxFiles: 5
  }));
}

module.exports = logger;