const pool = require('../db/connection');
const logger = require('../config/logger');
const { USER_STATUS, STATES } = require('../config/constants');

class User {
  // Helper function to normalize phone numbers
  static normalizePhone(phone) {
    return phone.replace('whatsapp:', '');
  }
  static async findByPhone(phone) {
    try {
      const normalizedPhone = this.normalizePhone(phone);
      const result = await pool.query(
        'SELECT * FROM users WHERE phone = $1',
        [normalizedPhone]
      );
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error finding user by phone', { error: error.message });
      throw error;
    }
  }

  static async create(phone) {
    try {
      const normalizedPhone = this.normalizePhone(phone);
      const result = await pool.query(
        `INSERT INTO users (phone, status, created_at, updated_at, last_active, 
                          consent_given, terms_accepted, age_verified, 
                          consent_timestamp, terms_accepted_date, age_verification_date,
                          consent_version, terms_version) 
         VALUES ($1, $2, NOW(), NOW(), NOW(), 
                TRUE, TRUE, TRUE,
                NOW(), NOW(), NOW(),
                '1.0', '1.0') 
         RETURNING *`,
        [normalizedPhone, USER_STATUS.LOCKED]
      );
      return result.rows[0];
    } catch (error) {
      logger.error('Error creating user', { error: error.message });
      throw error;
    }
  }

  static async findOrCreate(phone) {
    let user = await this.findByPhone(phone);
    if (!user) {
      user = await this.create(phone);
    }
    return user;
  }

  static async updateStatus(userId, status) {
    try {
      const result = await pool.query(
        'UPDATE users SET status = $1, updated_at = NOW() WHERE id = $2 RETURNING *',
        [status, userId]
      );
      return result.rows[0];
    } catch (error) {
      logger.error('Error updating user status', { error: error.message });
      throw error;
    }
  }

  static async updateState(userId, state, context = {}) {
    try {
      const result = await pool.query(
        `UPDATE users 
         SET current_state = $1, session_context = $2, last_active = NOW(), updated_at = NOW() 
         WHERE id = $3 
         RETURNING *`,
        [state, JSON.stringify(context), userId]
      );
      return result.rows[0];
    } catch (error) {
      logger.error('Error updating user state', { error: error.message });
      throw error;
    }
  }

  static async updateLastActive(userId) {
    try {
      await pool.query(
        'UPDATE users SET last_active = NOW() WHERE id = $1',
        [userId]
      );
    } catch (error) {
      logger.error('Error updating last active', { error: error.message });
      throw error;
    }
  }


  static async setName(userId, displayName) {
    try {
      const result = await pool.query(
        'UPDATE users SET display_name = $1, updated_at = NOW() WHERE id = $2 RETURNING *',
        [displayName, userId]
      );
      return result.rows[0];
    } catch (error) {
      logger.error('Error setting user name', { error: error.message });
      throw error;
    }
  }

  static async setTimezone(userId, timezone) {
    try {
      const result = await pool.query(
        'UPDATE users SET timezone = $1, updated_at = NOW() WHERE id = $2 RETURNING *',
        [timezone, userId]
      );
      return result.rows[0];
    } catch (error) {
      logger.error('Error setting user timezone', { error: error.message });
      throw error;
    }
  }

  static async optOut(userId) {
    try {
      const result = await pool.query(
        `UPDATE users 
         SET status = 'PAUSED', opted_out_at = NOW(), updated_at = NOW() 
         WHERE id = $1 
         RETURNING *`,
        [userId]
      );
      return result.rows[0];
    } catch (error) {
      logger.error('Error opting out user', { error: error.message });
      throw error;
    }
  }

  static async getInactiveSessions(timeoutMs) {
    try {
      const cutoffTime = new Date(Date.now() - timeoutMs);
      const result = await pool.query(
        `SELECT * FROM users 
         WHERE last_active < $1 
         AND current_state != 'MAIN_MENU' 
         AND status != 'PAUSED'`,
        [cutoffTime]
      );
      return result.rows;
    } catch (error) {
      logger.error('Error getting inactive sessions', { error: error.message });
      throw error;
    }
  }

  static async resetToOnboarding(userId) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Delete all existing habits for the user
      await client.query('DELETE FROM habits WHERE user_id = $1', [userId]);
      
      // Delete all habit logs for the user
      await client.query('DELETE FROM habit_logs WHERE user_id = $1', [userId]);

      // Reset user to onboarding state - start at main menu so they get proper intro
      const result = await client.query(
        `UPDATE users 
         SET status = $1, 
             current_state = $2, 
             session_context = $3,
             updated_at = NOW() 
         WHERE id = $4 
         RETURNING *`,
        [USER_STATUS.ONBOARDING, STATES.MAIN_MENU, JSON.stringify({}), userId]
      );

      await client.query('COMMIT');
      logger.info('User reset to onboarding successfully', { userId });
      return result.rows[0];
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error resetting user to onboarding', { 
        error: error.message,
        userId
      });
      throw error;
    } finally {
      client.release();
    }
  }
}

module.exports = User;