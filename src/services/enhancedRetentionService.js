const pool = require('../db/connection');
const logger = require('../config/logger');
const complianceAuditService = require('./complianceAuditService');

class EnhancedRetentionService {
  
  constructor() {
    this.isRunning = false;
    this.retentionPolicies = {
      inactiveUserThreshold: '1 year',      // Delete inactive users after 1 year
      exportFileExpiry: '7 days',           // Export files expire after 7 days  
      requestExpiry: '30 days',             // Data requests expire after 30 days
      auditLogRetention: '2 years',         // Keep audit logs for 2 years
      deletionGracePeriod: '30 days',       // Grace period for deletion requests
      sessionDataExpiry: '30 minutes',      // Session data expires after 30 minutes
      tempDataExpiry: '24 hours',           // Temporary data expires after 24 hours
      consentRecordRetention: '7 years'     // Keep consent records for 7 years
    };
  }

  /**
   * Start the enhanced retention service
   */
  start() {
    if (this.isRunning) {
      logger.warn('Enhanced retention service already running');
      return;
    }

    this.isRunning = true;
    logger.info('Starting enhanced data retention service');

    // Run immediately on startup
    this.runCleanupCycle();

    // Schedule to run every 4 hours
    const cleanupInterval = 4 * 60 * 60 * 1000; // 4 hours
    this.intervalId = setInterval(() => {
      this.runCleanupCycle();
    }, cleanupInterval);

    // Also run daily comprehensive cleanup at 2 AM
    const now = new Date();
    const tomorrow2AM = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 2, 0, 0, 0);
    const msUntil2AM = tomorrow2AM.getTime() - now.getTime();
    
    setTimeout(() => {
      this.runComprehensiveCleanup();
      
      // Then run comprehensive cleanup daily
      const dailyInterval = 24 * 60 * 60 * 1000; // 24 hours
      setInterval(() => {
        this.runComprehensiveCleanup();
      }, dailyInterval);
    }, msUntil2AM);
  }

  /**
   * Stop the retention service
   */
  stop() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    this.isRunning = false;
    logger.info('Enhanced retention service stopped');
  }

  /**
   * Run regular cleanup cycle
   */
  async runCleanupCycle() {
    logger.info('Starting regular retention cleanup cycle');

    try {
      const stats = await this.performRegularCleanup();
      
      await complianceAuditService.logRetentionEvent('regular_cleanup_completed', stats);
      logger.info('Regular retention cleanup completed', stats);
    } catch (error) {
      logger.error('Regular retention cleanup failed', { error: error.message });
      await complianceAuditService.logRetentionEvent('regular_cleanup_failed', { error: error.message });
    }
  }

  /**
   * Run comprehensive cleanup (daily)
   */
  async runComprehensiveCleanup() {
    logger.info('Starting comprehensive retention cleanup');

    try {
      const stats = await this.performComprehensiveCleanup();
      
      await complianceAuditService.logRetentionEvent('comprehensive_cleanup_completed', stats);
      logger.info('Comprehensive retention cleanup completed', stats);
    } catch (error) {
      logger.error('Comprehensive retention cleanup failed', { error: error.message });
      await complianceAuditService.logRetentionEvent('comprehensive_cleanup_failed', { error: error.message });
    }
  }

  /**
   * Perform regular cleanup operations (every 4 hours)
   */
  async performRegularCleanup() {
    const client = await pool.connect();
    const stats = {
      deletedExports: 0,
      expiredRequests: 0,
      cleanedSessions: 0,
      startTime: new Date().toISOString()
    };

    try {
      await client.query('BEGIN');

      // 1. Clean up expired data exports
      const expiredExports = await client.query(`
        DELETE FROM data_exports 
        WHERE expiry_date < NOW()
        RETURNING id, user_id, export_type
      `);
      stats.deletedExports = expiredExports.rowCount;

      // 2. Clean up expired data requests  
      const expiredRequests = await client.query(`
        UPDATE data_requests 
        SET status = 'expired'
        WHERE expiry_date < NOW() 
        AND status IN ('pending', 'processing')
        RETURNING id, user_id, request_type
      `);
      stats.expiredRequests = expiredRequests.rowCount;

      // 3. Clean up expired sessions (if stored in DB)
      // Note: Currently using in-memory sessions, but this is for future-proofing
      const sessionCleanup = await client.query(`
        DELETE FROM user_sessions 
        WHERE last_active < NOW() - INTERVAL '${this.retentionPolicies.sessionDataExpiry}'
      `).catch(() => ({ rowCount: 0 })); // Table might not exist yet
      stats.cleanedSessions = sessionCleanup.rowCount;

      await client.query('COMMIT');

      stats.endTime = new Date().toISOString();
      return stats;

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Perform comprehensive cleanup (daily)
   */
  async performComprehensiveCleanup() {
    const client = await pool.connect();
    const stats = {
      deletedUsers: 0,
      processedDeletionRequests: 0,
      expiredRequests: 0,
      archivedAuditLogs: 0,
      cleanedTempData: 0,
      startTime: new Date().toISOString()
    };

    try {
      await client.query('BEGIN');

      // 1. Process deletion requests after grace period
      const deletionRequests = await client.query(`
        SELECT id, phone FROM users 
        WHERE account_deletion_requested = TRUE 
        AND deletion_request_date < NOW() - INTERVAL '${this.retentionPolicies.deletionGracePeriod}'
      `);

      for (const user of deletionRequests.rows) {
        await this.deleteUserCompletely(client, user.id);
        stats.deletedUsers++;
        
        await complianceAuditService.logComplianceEvent(user.id, 'account_deleted_retention', {
          reason: 'deletion_request_grace_period_expired',
          gracePeriod: this.retentionPolicies.deletionGracePeriod
        });
      }
      stats.processedDeletionRequests = deletionRequests.rows.length;

      // 2. Delete inactive users without consent (GDPR compliance)
      const inactiveUsers = await client.query(`
        SELECT id FROM users 
        WHERE last_active < NOW() - INTERVAL '${this.retentionPolicies.inactiveUserThreshold}'
        AND (consent_given = FALSE OR consent_given IS NULL)
        AND account_deletion_requested = FALSE
        AND current_state != 'BLOCKED_MINOR'
      `);

      for (const user of inactiveUsers.rows) {
        await this.deleteUserCompletely(client, user.id);
        stats.deletedUsers++;
        
        await complianceAuditService.logComplianceEvent(user.id, 'account_deleted_retention', {
          reason: 'inactive_without_consent',
          inactiveThreshold: this.retentionPolicies.inactiveUserThreshold
        });
      }

      // 3. Archive old audit logs (keep but compress/archive)
      const oldAuditLogs = await client.query(`
        UPDATE audit_log 
        SET notes = 'ARCHIVED: ' || COALESCE(notes, '')
        WHERE timestamp < NOW() - INTERVAL '${this.retentionPolicies.auditLogRetention}'
        AND notes NOT LIKE 'ARCHIVED:%'
        RETURNING id
      `);
      stats.archivedAuditLogs = oldAuditLogs.rowCount;

      // 4. Update data retention dates for active users
      await client.query(`
        UPDATE users 
        SET data_retention_date = NOW() + INTERVAL '${this.retentionPolicies.inactiveUserThreshold}'
        WHERE last_active > NOW() - INTERVAL '1 month'
        AND consent_given = TRUE
      `);

      // 5. Clean up temporary data
      const tempDataCleanup = await client.query(`
        DELETE FROM temp_data 
        WHERE created_at < NOW() - INTERVAL '${this.retentionPolicies.tempDataExpiry}'
      `).catch(() => ({ rowCount: 0 })); // Table might not exist
      stats.cleanedTempData = tempDataCleanup.rowCount;

      await client.query('COMMIT');

      stats.endTime = new Date().toISOString();
      return stats;

    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Completely delete a user and all associated data with compliance audit
   */
  async deleteUserCompletely(client, userId) {
    logger.info('Deleting user completely with compliance audit', { userId });

    // Audit before deletion
    await complianceAuditService.logDataModification(
      userId, 
      'user_deletion_started', 
      'user', 
      userId,
      { status: 'exists' },
      { status: 'being_deleted' },
      { deletionReason: 'retention_policy' }
    );

    // Delete in correct order due to foreign key constraints
    const deletionSteps = [
      { table: 'habit_logs', description: 'habit completion logs' },
      { table: 'habits', description: 'user habits' },
      { table: 'user_consents', description: 'consent records' },
      { table: 'data_requests', description: 'data requests' },
      { table: 'data_exports', description: 'data exports' },
      { table: 'user_sessions', description: 'active sessions' }
    ];

    for (const step of deletionSteps) {
      try {
        const result = await client.query(`DELETE FROM ${step.table} WHERE user_id = $1`, [userId]);
        logger.info(`Deleted ${step.description}`, { 
          userId, 
          deletedRows: result.rowCount,
          table: step.table 
        });
        
        await complianceAuditService.logDataModification(
          userId,
          'data_deleted',
          step.table,
          null,
          { recordCount: result.rowCount },
          { recordCount: 0 },
          { deletionStep: step.description }
        );
      } catch (error) {
        // Some tables might not exist or have foreign key constraints
        logger.warn(`Failed to delete from ${step.table}`, { 
          userId, 
          error: error.message 
        });
      }
    }

    // Archive audit logs for this user (don't delete - legal requirement)
    await client.query(`
      UPDATE audit_log 
      SET notes = 'USER_DELETED: ' || COALESCE(notes, ''),
          user_id = NULL
      WHERE user_id = $1
    `, [userId]);

    // Finally delete the user record
    await client.query('DELETE FROM users WHERE id = $1', [userId]);

    // Final compliance audit log
    await complianceAuditService.logComplianceEvent(null, 'user_deletion_completed', {
      deletedUserId: userId,
      deletionReason: 'retention_policy',
      legalBasis: 'GDPR Article 17 - Right to erasure / Article 5(1)(e) - Storage limitation',
      auditTrailPreserved: true
    });

    logger.info('User deletion completed with compliance audit', { userId });
  }

  /**
   * Get data minimization recommendations
   */
  async getDataMinimizationRecommendations() {
    try {
      const recommendations = [];

      // Check for users with excessive data export requests
      const excessiveExports = await pool.query(`
        SELECT user_id, COUNT(*) as export_count
        FROM data_exports
        WHERE export_date > NOW() - INTERVAL '30 days'
        GROUP BY user_id
        HAVING COUNT(*) > 10
      `);

      if (excessiveExports.rows.length > 0) {
        recommendations.push({
          type: 'data_access_limit',
          description: 'Consider implementing rate limiting for data exports',
          affected_users: excessiveExports.rows.length,
          priority: 'medium'
        });
      }

      // Check for users with old unconsented data
      const unconsentedUsers = await pool.query(`
        SELECT COUNT(*) as count
        FROM users 
        WHERE created_at < NOW() - INTERVAL '6 months'
        AND (consent_given = FALSE OR consent_given IS NULL)
      `);

      if (unconsentedUsers.rows[0].count > 0) {
        recommendations.push({
          type: 'consent_cleanup',
          description: 'Delete old unconsented user data',
          affected_users: unconsentedUsers.rows[0].count,
          priority: 'high'
        });
      }

      // Check for stale temporary data
      const staleData = await pool.query(`
        SELECT 'data_exports' as table_name, COUNT(*) as count
        FROM data_exports 
        WHERE expiry_date < NOW() - INTERVAL '7 days'
        UNION ALL
        SELECT 'data_requests', COUNT(*)
        FROM data_requests 
        WHERE expiry_date < NOW() - INTERVAL '30 days'
      `);

      staleData.rows.forEach(row => {
        if (row.count > 0) {
          recommendations.push({
            type: 'stale_data_cleanup',
            description: `Clean up expired ${row.table_name}`,
            affected_records: row.count,
            priority: 'low'
          });
        }
      });

      return {
        recommendations,
        generatedAt: new Date().toISOString(),
        totalRecommendations: recommendations.length
      };

    } catch (error) {
      logger.error('Data minimization analysis failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Get retention statistics for compliance reporting
   */
  async getRetentionStats() {
    try {
      const stats = await pool.query(`
        SELECT 
          COUNT(*) as total_users,
          COUNT(CASE WHEN last_active > NOW() - INTERVAL '30 days' THEN 1 END) as active_30_days,
          COUNT(CASE WHEN last_active > NOW() - INTERVAL '1 year' THEN 1 END) as active_1_year,
          COUNT(CASE WHEN account_deletion_requested = TRUE THEN 1 END) as deletion_requested,
          COUNT(CASE WHEN consent_given = TRUE THEN 1 END) as consented_users,
          COUNT(CASE WHEN data_retention_date < NOW() THEN 1 END) as expired_retention,
          AVG(EXTRACT(DAYS FROM NOW() - last_active)) as avg_days_inactive,
          COUNT(CASE WHEN current_state = 'BLOCKED_MINOR' THEN 1 END) as blocked_minors
        FROM users
      `);

      const exportStats = await pool.query(`
        SELECT 
          COUNT(*) as total_exports,
          COUNT(CASE WHEN expiry_date < NOW() THEN 1 END) as expired_exports,
          SUM(file_size) as total_export_size,
          AVG(file_size) as avg_export_size
        FROM data_exports
      `);

      const auditStats = await pool.query(`
        SELECT 
          COUNT(*) as total_audit_logs,
          COUNT(CASE WHEN timestamp < NOW() - INTERVAL '2 years' THEN 1 END) as old_audit_logs,
          COUNT(CASE WHEN resource_type = 'compliance_event' THEN 1 END) as compliance_events,
          MIN(timestamp) as oldest_log,
          MAX(timestamp) as newest_log
        FROM audit_log
      `);

      return {
        users: stats.rows[0],
        exports: exportStats.rows[0],
        audit: auditStats.rows[0],
        retentionPolicies: this.retentionPolicies,
        generated_at: new Date().toISOString()
      };

    } catch (error) {
      logger.error('Retention stats generation failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Manually trigger retention cleanup (for admin use)
   */
  async manualCleanup(type = 'regular') {
    logger.info('Manual retention cleanup triggered', { type });
    
    await complianceAuditService.logRetentionEvent('manual_cleanup_triggered', { 
      type,
      triggeredBy: 'admin'
    });

    if (type === 'comprehensive') {
      return await this.performComprehensiveCleanup();
    } else {
      return await this.performRegularCleanup();
    }
  }

  /**
   * Check retention policy compliance
   */
  async checkRetentionCompliance() {
    try {
      const issues = [];

      // Check for users past retention date
      const overRetention = await pool.query(`
        SELECT COUNT(*) as count
        FROM users 
        WHERE data_retention_date < NOW() - INTERVAL '30 days'
        AND account_deletion_requested = FALSE
      `);

      if (overRetention.rows[0].count > 0) {
        issues.push({
          type: 'over_retention',
          description: 'Users past data retention date',
          count: overRetention.rows[0].count,
          severity: 'high'
        });
      }

      // Check for expired but unprocessed deletion requests
      const pendingDeletions = await pool.query(`
        SELECT COUNT(*) as count
        FROM users 
        WHERE account_deletion_requested = TRUE 
        AND deletion_request_date < NOW() - INTERVAL '35 days'
      `);

      if (pendingDeletions.rows[0].count > 0) {
        issues.push({
          type: 'pending_deletions',
          description: 'Deletion requests past grace period',
          count: pendingDeletions.rows[0].count,
          severity: 'critical'
        });
      }

      return {
        compliant: issues.length === 0,
        issues,
        checkedAt: new Date().toISOString()
      };

    } catch (error) {
      logger.error('Retention compliance check failed', { error: error.message });
      throw error;
    }
  }
}

module.exports = new EnhancedRetentionService();