const User = require('../models/User');
const Habit = require('../models/Habit');
const AuditLog = require('../models/AuditLog');
const paymentService = require('./paymentService');
const userRightsService = require('./userRightsService');
const complianceService = require('./complianceService');
const pool = require('../db/connection');
const { STATES, USER_STATUS, AUDIT_EVENTS, HABITS } = require('../config/constants');
const logger = require('../config/logger');
const moment = require('moment-timezone');
const motivationalQuotes = require('../utils/motivationalQuotes');

class StateMachineCompliant {
  constructor() {
    // Copy the original state machine transitions
    this.originalStateMachine = require('./stateMachinePaymentEnforced');
    this.transitions = this.originalStateMachine.transitions;
  }

  async processMessage(user, message) {
    try {
      // Update last active with compliance audit
      await User.updateLastActive(user.id);
      await this.auditUserActivity(user.id, 'message_received', { messageLength: message.length });

      const upperMessage = message.trim().toUpperCase();
      const lowerMessage = message.trim().toLowerCase();
      const normalizedPhone = User.normalizePhone(user.phone);

      // PRIORITY 1: DATA SUBJECT RIGHTS COMMANDS (Always processed first)
      if (upperMessage === 'DELETE MY DATA' || upperMessage === 'DELETE ACCOUNT') {
        return await this.handleDataDeletion(user);
      }

      if (upperMessage === 'EXPORT MY DATA' || upperMessage === 'DOWNLOAD MY DATA') {
        return await this.handleDataExport(user);
      }

      // PRIORITY 2: COMMUNICATION PREFERENCES (TCPA/CAN-SPAM compliance)
      if (upperMessage === 'STOP' || upperMessage === 'UNSUBSCRIBE' || upperMessage === 'OPT OUT') {
        return await this.handleOptOut(user);
      }

      if (upperMessage === 'OPT IN' || upperMessage === 'START') {
        return await this.handleOptIn(user);
      }

      // PRIORITY 3: COMPLIANCE HELP COMMANDS
      if (lowerMessage.includes('privacy help') || upperMessage === 'PRIVACY') {
        return this.getPrivacyHelp();
      }

      if (lowerMessage.includes('data rights') || upperMessage === 'MY RIGHTS') {
        return this.getDataRightsHelp();
      }

      // PRIORITY 4: Check if user has opted out
      if (user.opt_out_communications) {
        return {
          message: `You have opted out of all communications.

*To resume service:*
Reply "OPT IN" to restart receiving messages.

*Your data rights:*
• "EXPORT MY DATA" - Download your data
• "DELETE MY DATA" - Permanently delete account`,
          newState: user.current_state
        };
      }

      // PRIORITY 5: Skip compliance - handled by store/website
      // Compliance checks are now done at purchase time, not in WhatsApp flow

      // PRIORITY 6: Handle START HABIT-XXXXX command (always allowed)
      if (upperMessage.startsWith('START HABIT-')) {
        return await this.handleAccessCodeActivation(user, message);
      }

      // PRIORITY 7: Handle test commands
      if (upperMessage === 'RESET_PAYMENT_TEST' && process.env.PAYMENT_TEST_MODE === 'true') {
        return await this.originalStateMachine.handlePaymentTestReset(user);
      }

      if (upperMessage === 'RESET_TEST') {
        return await this.originalStateMachine.handleResetTest(user);
      }

      // PRIORITY 8: CHECK PAYMENT STATUS FOR ALL OTHER COMMANDS
      const accessCheck = await paymentService.checkUserAccess(normalizedPhone);
      
      if (!accessCheck.hasAccess) {
        // NO ACCESS - Handle different cases
        if (accessCheck.isExpired && accessCheck.user) {
          // Existing user with expired subscription - show renewal message
          logger.info('Subscription expired - showing renewal message', { 
            userId: user.id, 
            phone: normalizedPhone.substring(0, 5) + '***',
            subscriptionType: accessCheck.user.subscription_type,
            expiredAt: accessCheck.expiresAt
          });
          
          const renewalMessage = this.getSubscriptionExpiredMessage(accessCheck.user);
          return {
            message: renewalMessage,
            newState: STATES.MAIN_MENU
          };
        } else {
          // New user or other access issues - Show general paywall
          logger.info('Payment required - showing compliant paywall', { 
            userId: user.id, 
            phone: normalizedPhone.substring(0, 5) + '***',
            attemptedMessage: message.substring(0, 20)
          });
          
          return {
            message: this.getCompliantPaywallMessage(),
            newState: STATES.MAIN_MENU
          };
        }
      }

      // Has payment access - check if user needs to be unlocked
      if (user.status === USER_STATUS.LOCKED) {
        return await this.handleUserUnlocking(user, accessCheck, normalizedPhone);
      }

      // PRIORITY 9: Enhanced help commands with compliance
      if (upperMessage === 'HELP' || upperMessage === 'MENU') {
        return this.getComplianceEnhancedHelp();
      }

      // User has payment access and is compliant - use original state machine
      return await this.originalStateMachine.processMessage(user, message);
      
    } catch (error) {
      logger.error('Compliant state machine error', { 
        userId: user.id, 
        state: user.current_state, 
        error: error.message 
      });
      
      await this.auditUserActivity(user.id, 'error', { error: error.message });

      return {
        message: `Sorry, something went wrong. Please try again.

*Your data rights:*
• "EXPORT MY DATA" - Download your data  
• "DELETE MY DATA" - Delete account
• "STOP" - Opt out of messages
• "HELP" - Get support`,
        newState: user.current_state
      };
    }
  }

  /**
   * Handle data deletion request (GDPR Article 17)
   */
  async handleDataDeletion(user) {
    await this.auditUserActivity(user.id, 'data_deletion_requested');
    return await userRightsService.handleDeleteRequest(user);
  }

  /**
   * Handle data export request (GDPR Articles 15 & 20)
   */
  async handleDataExport(user) {
    await this.auditUserActivity(user.id, 'data_export_requested');
    return await userRightsService.handleExportRequest(user);
  }

  /**
   * Handle opt-out request (TCPA/CAN-SPAM compliance)
   */
  async handleOptOut(user) {
    await this.auditUserActivity(user.id, 'opt_out_requested');
    return await userRightsService.handleOptOutRequest(user);
  }

  /**
   * Handle opt-in request
   */
  async handleOptIn(user) {
    if (!user.opt_out_communications) {
      return {
        message: `You're already opted in to receive messages.

*Your data rights:*
• "EXPORT MY DATA" - Download your data
• "DELETE MY DATA" - Delete account  
• "STOP" - Opt out anytime

Reply "MENU" to continue.`,
        newState: user.current_state
      };
    }

    // Re-enable communications
    await pool.query(
      'UPDATE users SET opt_out_communications = FALSE, opt_out_date = NULL WHERE id = $1',
      [user.id]
    );

    await this.auditUserActivity(user.id, 'opt_in_requested');

    return {
      message: `✅ *OPTED BACK IN*

Welcome back! You'll now receive:
• Habit tracking messages
• Service notifications  
• Motivational content

*Your data rights remain:*
• "EXPORT MY DATA" - Download your data
• "DELETE MY DATA" - Delete account
• "STOP" - Opt out anytime

Reply "MENU" to continue using Lockin!`,
      newState: STATES.MAIN_MENU
    };
  }

  /**
   * Get privacy help information
   */
  getPrivacyHelp() {
    return {
      message: `🔒 *PRIVACY & DATA RIGHTS*

*Your Data Rights:*
• "EXPORT MY DATA" - Download all your data (GDPR Art. 15/20)
• "DELETE MY DATA" - Permanently delete account (GDPR Art. 17)
• "STOP" - Opt out of all messages (TCPA/CAN-SPAM)

*What We Collect:*
• Phone number (WhatsApp requirement)
• Habits you create (core functionality)
• Daily completion logs (progress tracking)
• Payment info (subscription management)

*What We DON'T Do:*
❌ Sell your data to third parties
❌ Share with marketers
❌ Excessive tracking or profiling

*Data Security:*
✅ Encrypted transmission (HTTPS/TLS)
✅ Secure servers and access controls
✅ Regular security monitoring
✅ Compliance audit trails

🔒 Full Privacy Policy: https://lockintracker.com/privacy

Questions? Reply "HELP" for support.`,
      newState: null
    };
  }

  /**
   * Get data rights help
   */
  getDataRightsHelp() {
    return {
      message: `⚖️ *YOUR DATA RIGHTS*

Under GDPR, CCPA, and other privacy laws, you have these rights:

*Right to Access (GDPR Art. 15):*
• "EXPORT MY DATA" - Get copy of all your data

*Right to Erasure (GDPR Art. 17):*
• "DELETE MY DATA" - Permanent account deletion

*Right to Portability (GDPR Art. 20):*
• Data export in machine-readable format (JSON)

*Right to Object (GDPR Art. 21):*
• "STOP" - Opt out of processing/communications

*Right to Rectification (GDPR Art. 16):*
• Update habits and data anytime in the app

*How to Exercise Rights:*
Simply send the command via WhatsApp. We respond within 30 days as required by law.

*No Fees:*
All data rights requests are free of charge.

🔒 Privacy Policy: https://lockintracker.com/privacy
📋 Terms of Service: https://lockintracker.com/terms`,
      newState: null
    };
  }

  /**
   * Get compliance-enhanced help menu
   */
  getComplianceEnhancedHelp() {
    return {
      message: `🎯 *LOCKIN HABIT TRACKER - MAIN MENU*

*Habit Tracking:*
• "HABITS" - Set up your daily habits (up to 5)
• "LOG" - Log today's habit completion
• "PROGRESS" - View your progress stats

*Your Data Rights:*
• "EXPORT MY DATA" - Download all your data
• "DELETE MY DATA" - Delete account permanently
• "STOP" - Opt out of all messages

*Account:*
• "ACCOUNT" - View subscription status
• "SETTINGS" - Update preferences

*Help & Support:*
• "PRIVACY" - Privacy information and rights
• "TERMS" - Terms of service help
• "SUPPORT" - Contact customer support

What would you like to do?`,
      newState: STATES.MAIN_MENU
    };
  }

  /**
   * Get compliant paywall message
   */
  getCompliantPaywallMessage() {
    return `🔒 *PREMIUM SUBSCRIPTION REQUIRED*

To continue using Lockin's habit tracking features, you need an active subscription.

*Subscription Options:*
💳 Yearly: $30.00/year (Best Value!)

*Features Included:*
✅ Track up to 5 daily habits
✅ Detailed progress analytics
✅ Motivational content & reminders
✅ Data export & backup
✅ Priority support

*Your Data Rights (Always Available):*
• "EXPORT MY DATA" - Download your data
• "DELETE MY DATA" - Delete account  
• "STOP" - Opt out of messages

*Subscribe Now:*
Visit: https://aeon.thrivecart.com/lock-in-whatsapp-habit-tracker-yearly/

After subscribing, send your access code here to activate!`;
  }

  /**
   * Get subscription expired message for existing users
   */
  getSubscriptionExpiredMessage(user) {
    const subscriptionService = require('./subscriptionService');
    const renewalInfo = subscriptionService.createRenewalMessage(user);
    
    const subscriptionTypeText = user.subscription_type.charAt(0).toUpperCase() + user.subscription_type.slice(1);
    const expiredDate = user.expires_at ? new Date(user.expires_at).toLocaleDateString() : 'recently';
    
    return `⚠️ *SUBSCRIPTION EXPIRED*

Your ${subscriptionTypeText} subscription expired on ${expiredDate}.

*To continue using Lockin:*
🔄 Renew your subscription at the link below
✅ Get instant access to all your habits
📊 Keep your progress history

*Subscription Options:*
💳 Weekly: $2/week
💳 Monthly: $5/month  
💳 Yearly: $30/year (Best Value!)

*Renew Now:*
${renewalInfo.renewalUrl}

*Your Data Rights (Always Available):*
• "EXPORT MY DATA" - Download your data
• "DELETE MY DATA" - Delete account  
• "STOP" - Opt out of messages

Once renewed, you'll receive a new access code to reactivate!`;
  }

  /**
   * Handle user unlocking with compliance
   */
  async handleUserUnlocking(user, accessCheck, normalizedPhone) {
    // Auto-unlock paid user
    await User.updateStatus(user.id, USER_STATUS.ONBOARDING);
    
    await this.auditUserActivity(user.id, 'account_unlocked', {
      paidUserId: accessCheck.paidUser?.id,
      unlockReason: 'payment_verified'
    });
    
    logger.info('Auto-unlocked paid user with compliance audit', { 
      userId: user.id, 
      phone: normalizedPhone.substring(0, 5) + '***',
      paidUserId: accessCheck.paidUser?.id
    });
    
    // Skip compliance onboarding - handled by store/website
    // Go directly to name collection if user doesn't have a name
    if (!user.display_name) {
      await User.updateState(user.id, STATES.AWAITING_NAME);
      return {
        message: `🎉 *WELCOME TO LOCKIN!*

Your subscription is active and your account is unlocked.

Let's set up your profile.
What should I call you? (Enter your first name)`,
        newState: STATES.AWAITING_NAME
      };
    }

    return {
      message: `🎉 Welcome back, ${user.display_name}!

Your subscription is verified and active.

*Your data rights:*
• "EXPORT MY DATA" - Download your data
• "DELETE MY DATA" - Delete account
• "STOP" - Opt out of messages

Reply "MENU" to continue tracking your habits!`,
      newState: STATES.MAIN_MENU
    };
  }

  /**
   * Handle access code activation with compliance
   */
  async handleAccessCodeActivation(user, message) {
    const accessCode = message.replace(/^START\s+/i, '').trim();
    
    await this.auditUserActivity(user.id, 'access_code_attempt', { 
      accessCode: accessCode.substring(0, 6) + '***' 
    });

    // Use original state machine logic but with compliance audit
    const result = await this.originalStateMachine.handleAccessCodeActivation(user, message);
    
    if (result.message.includes('successfully activated')) {
      await this.auditUserActivity(user.id, 'access_code_activated', { 
        accessCode: accessCode.substring(0, 6) + '***' 
      });
      
      // Add compliance notice to success message
      result.message += `

*Data Collection Notice:*
Now that you're activated, we collect:
• Habits you create (core functionality)
• Daily completion logs (progress tracking)
• Account preferences (service provision)

*Your Rights:*
• "EXPORT MY DATA" - Download anytime  
• "DELETE MY DATA" - Delete account anytime
• "STOP" - Opt out anytime`;
    }

    return result;
  }

  /**
   * Audit user activity for compliance
   */
  async auditUserActivity(userId, action, details = {}) {
    try {
      await pool.query(`
        INSERT INTO audit_log (user_id, action, resource_type, notes, timestamp)
        VALUES ($1, $2, 'user_activity', $3, NOW())
      `, [userId, action, JSON.stringify(details)]);
    } catch (error) {
      logger.error('Audit logging failed', { userId, action, error: error.message });
    }
  }
}

module.exports = new StateMachineCompliant();