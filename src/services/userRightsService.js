const pool = require('../db/connection');
const logger = require('../config/logger');
const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

class UserRightsService {
  
  /**
   * Handle DELETE MY DATA request (GDPR Article 17 - Right to Erasure)
   * @param {Object} user - User object
   * @returns {Object} Response with deletion status
   */
  async handleDeleteRequest(user) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Log the deletion request
      await this.logDataRequest(user.id, 'delete', 'whatsapp', client);
      
      // Generate verification code for security
      const verificationCode = crypto.randomBytes(3).toString('hex').toUpperCase();
      
      // Store deletion request with verification
      await client.query(
        `UPDATE users SET 
         account_deletion_requested = TRUE,
         deletion_request_date = NOW()
         WHERE id = $1`,
        [user.id]
      );
      
      await client.query(
        `INSERT INTO data_requests (user_id, request_type, request_method, verification_code, notes)
         VALUES ($1, 'delete', 'whatsapp', $2, 'User requested account deletion via WhatsApp')`,
        [user.id, verificationCode]
      );
      
      await client.query('COMMIT');
      
      // Immediate response
      const response = {
        message: `🗑️ *DELETION REQUEST RECEIVED*

Your request to delete all your data has been received and will be processed within 30 days as required by law.

*Verification Code: ${verificationCode}*
(Keep this code for your records)

*What happens next:*
• Your account will be deactivated immediately
• All data will be permanently deleted within 30 days
• You'll receive confirmation once complete
• This action cannot be undone

*Legal basis:* GDPR Article 17 (Right to Erasure)

If you change your mind, reply "CANCEL DELETION ${verificationCode}" within 24 hours.

🔒 Privacy Policy: https://lockintracker.com/privacy
📋 Terms: https://lockintracker.com/terms`,
        newState: 'DELETION_REQUESTED'
      };
      
      // Schedule actual deletion (in a real app, you'd use a job queue)
      setTimeout(() => this.processAccountDeletion(user.id), 24 * 60 * 60 * 1000); // 24 hours delay
      
      return response;
      
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Delete request failed', { 
        userId: user.id, 
        error: error.message 
      });
      
      return {
        message: "Sorry, there was an error processing your deletion request. Please try again or contact support.",
        newState: user.current_state
      };
    } finally {
      client.release();
    }
  }

  /**
   * Handle EXPORT MY DATA request (GDPR Article 15 - Right of Access & Article 20 - Data Portability)
   * @param {Object} user - User object
   * @returns {Object} Response with export instructions
   */
  async handleExportRequest(user) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Check if user has exceeded export limits (prevent abuse)
      const exportCount = await client.query(
        'SELECT data_export_requests FROM users WHERE id = $1',
        [user.id]
      );
      
      if (exportCount.rows[0]?.data_export_requests >= 5) {
        return {
          message: "You have reached the maximum number of data exports (5 per year). This helps prevent abuse while respecting your privacy rights. Contact support if you need assistance.",
          newState: user.current_state
        };
      }
      
      // Log the export request
      await this.logDataRequest(user.id, 'export', 'whatsapp', client);
      
      // Generate secure download token
      const downloadToken = crypto.randomBytes(16).toString('hex');
      
      // Get all user data
      const userData = await this.collectUserData(user.id, client);
      
      // Create export file
      const exportData = {
        export_info: {
          user_id: user.id,
          export_date: new Date().toISOString(),
          export_type: 'full_account_data',
          format: 'json',
          legal_basis: 'GDPR Article 15 (Right of Access) & Article 20 (Data Portability)'
        },
        personal_data: userData
      };
      
      // Save export file
      const fileName = `user_${user.id}_export_${Date.now()}.json`;
      const filePath = path.join('/tmp', fileName);
      await fs.writeFile(filePath, JSON.stringify(exportData, null, 2));
      
      // Calculate file hash for integrity
      const fileBuffer = await fs.readFile(filePath);
      const fileHash = crypto.createHash('sha256').update(fileBuffer).digest('hex');
      
      // Record export in database
      await client.query(
        `INSERT INTO data_exports (user_id, export_type, export_format, file_size, file_hash)
         VALUES ($1, 'full', 'json', $2, $3)`,
        [user.id, fileBuffer.length, fileHash]
      );
      
      // Update user export count
      await client.query(
        `UPDATE users SET 
         data_export_requests = data_export_requests + 1,
         last_export_date = NOW()
         WHERE id = $1`,
        [user.id]
      );
      
      await client.query('COMMIT');
      
      // Response with download instructions
      const response = {
        message: `📊 *DATA EXPORT READY*

Your complete data export has been prepared and is ready for download.

*Export Details:*
• File size: ${Math.round(fileBuffer.length / 1024)} KB
• Format: JSON (machine-readable)
• Export date: ${new Date().toISOString().split('T')[0]}
• Download expires: 7 days

*What's included:*
✅ Account information
✅ All habits and descriptions
✅ Complete logging history
✅ Subscription details
✅ Consent records

*How to download:*
Visit this secure link (expires in 7 days):
https://lockintracker.com/export/${downloadToken}

*Your rights under GDPR:*
This export fulfills your right to data portability (Article 20) and right of access (Article 15).

Remaining exports this year: ${4 - (exportCount.rows[0]?.data_export_requests || 0)}

🔒 Privacy Policy: https://lockintracker.com/privacy`,
        newState: user.current_state
      };
      
      return response;
      
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Export request failed', { 
        userId: user.id, 
        error: error.message 
      });
      
      return {
        message: "Sorry, there was an error preparing your data export. Please try again or contact support.",
        newState: user.current_state
      };
    } finally {
      client.release();
    }
  }

  /**
   * Handle OPT-OUT requests (GDPR Article 21 - Right to Object)
   * @param {Object} user - User object
   * @returns {Object} Response with opt-out confirmation
   */
  async handleOptOutRequest(user) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Log the opt-out request
      await this.logDataRequest(user.id, 'opt_out', 'whatsapp', client);
      
      // Update user preferences
      await client.query(
        `UPDATE users SET 
         opt_out_communications = TRUE,
         opt_out_date = NOW()
         WHERE id = $1`,
        [user.id]
      );
      
      // Record consent withdrawal
      await client.query(
        `INSERT INTO user_consents (user_id, consent_type, consent_given, consent_method, notes)
         VALUES ($1, 'marketing', FALSE, 'whatsapp', 'User opted out of all communications')`,
        [user.id]
      );
      
      await client.query('COMMIT');
      
      return {
        message: `✅ *OPT-OUT CONFIRMED*

You have been removed from all communications.

*What this means:*
• No more habit reminders
• No more motivational messages  
• No marketing communications
• Account remains active for logging habits

*To opt back in:* Reply "OPT IN"

*Your rights:* This fulfills your right to object under GDPR Article 21.

This is our final message unless you opt back in.`,
        newState: 'OPTED_OUT'
      };
      
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Opt-out request failed', { 
        userId: user.id, 
        error: error.message 
      });
      
      return {
        message: "Sorry, there was an error processing your opt-out request. Please try again.",
        newState: user.current_state
      };
    } finally {
      client.release();
    }
  }

  /**
   * Collect all user data for export
   * @param {number} userId - User ID
   * @param {Object} client - Database client
   * @returns {Object} Complete user data
   */
  async collectUserData(userId, client) {
    // Get user account data
    const userResult = await client.query(
      `SELECT id, phone, display_name, status, timezone, created_at, is_unlocked, 
              current_state, last_active, consent_given, consent_timestamp, 
              age_verified, terms_accepted, data_export_requests
       FROM users WHERE id = $1`,
      [userId]
    );
    
    // Get habits
    const habitsResult = await client.query(
      'SELECT habit_number, habit_name, created_at FROM habits WHERE user_id = $1',
      [userId]
    );
    
    // Get habit logs
    const logsResult = await client.query(
      `SELECT h.habit_name, hl.log_date, hl.completed, hl.logged_at 
       FROM habit_logs hl 
       JOIN habits h ON hl.habit_id = h.id 
       WHERE hl.user_id = $1 
       ORDER BY hl.log_date DESC`,
      [userId]
    );
    
    // Get consents
    const consentsResult = await client.query(
      `SELECT consent_type, consent_given, consent_method, consent_timestamp, 
              withdrawn_date, notes 
       FROM user_consents WHERE user_id = $1`,
      [userId]
    );
    
    // Get data requests history
    const requestsResult = await client.query(
      `SELECT request_type, request_method, status, request_date, completion_date 
       FROM data_requests WHERE user_id = $1`,
      [userId]
    );
    
    return {
      account: userResult.rows[0],
      habits: habitsResult.rows,
      habit_logs: logsResult.rows,
      consent_history: consentsResult.rows,
      data_requests: requestsResult.rows,
      export_summary: {
        total_habits: habitsResult.rows.length,
        total_logs: logsResult.rows.length,
        account_age_days: Math.floor((new Date() - new Date(userResult.rows[0].created_at)) / (1000 * 60 * 60 * 24))
      }
    };
  }

  /**
   * Process actual account deletion (after grace period)
   * @param {number} userId - User ID to delete
   */
  async processAccountDeletion(userId) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      logger.info('Processing account deletion', { userId });
      
      // Check if user canceled deletion
      const user = await client.query(
        'SELECT account_deletion_requested FROM users WHERE id = $1',
        [userId]
      );
      
      if (!user.rows[0]?.account_deletion_requested) {
        logger.info('Deletion canceled by user', { userId });
        return;
      }
      
      // Delete in correct order (foreign key constraints)
      await client.query('DELETE FROM habit_logs WHERE user_id = $1', [userId]);
      await client.query('DELETE FROM habits WHERE user_id = $1', [userId]);
      await client.query('DELETE FROM user_consents WHERE user_id = $1', [userId]);
      await client.query('DELETE FROM data_requests WHERE user_id = $1', [userId]);
      await client.query('DELETE FROM data_exports WHERE user_id = $1', [userId]);
      
      // Finally delete user
      await client.query('DELETE FROM users WHERE id = $1', [userId]);
      
      // Log successful deletion
      await client.query(
        `INSERT INTO audit_log (user_id, action, resource_type, notes) 
         VALUES ($1, 'account_deleted', 'user', 'Account permanently deleted per user request')`,
        [userId]
      );
      
      await client.query('COMMIT');
      
      logger.info('Account deletion completed', { userId });
      
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Account deletion failed', { userId, error: error.message });
    } finally {
      client.release();
    }
  }

  /**
   * Log data rights request
   * @param {number} userId - User ID
   * @param {string} requestType - Type of request
   * @param {string} method - Request method
   * @param {Object} client - Database client
   */
  async logDataRequest(userId, requestType, method, client) {
    await client.query(
      `INSERT INTO audit_log (user_id, action, resource_type, notes) 
       VALUES ($1, $2, 'data_request', $3)`,
      [userId, `${requestType}_request`, `User requested ${requestType} via ${method}`]
    );
  }

  /**
   * Handle STOP keyword (TCPA compliance)
   * @param {Object} user - User object
   * @returns {Object} Response with stop confirmation
   */
  async handleStopRequest(user) {
    // This should completely opt out the user per TCPA requirements
    return await this.handleOptOutRequest(user);
  }
}

module.exports = new UserRightsService();