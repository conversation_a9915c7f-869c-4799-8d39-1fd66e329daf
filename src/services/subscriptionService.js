const pool = require('../db/connection');
const logger = require('../config/logger');

class SubscriptionService {
  /**
   * Check if a user's subscription is active and not expired
   */
  async checkSubscriptionStatus(email) {
    try {
      const result = await pool.query(
        `SELECT id, email, subscription_type, status, expires_at, next_billing_date, 
                access_code, is_affiliate
         FROM paid_users 
         WHERE email = $1`,
        [email]
      );

      if (result.rows.length === 0) {
        return {
          hasAccess: false,
          status: 'not_found',
          message: 'User not found'
        };
      }

      const user = result.rows[0];

      // If no expiration date, assume active (legacy users or lifetime)
      if (!user.expires_at) {
        return {
          hasAccess: true,
          status: 'active',
          user: user,
          message: 'Active subscription (no expiration)'
        };
      }

      const now = new Date();
      const expiresAt = new Date(user.expires_at);

      // Check if subscription has expired
      if (now > expiresAt) {
        // Mark as expired if not already
        if (user.status !== 'expired') {
          await this.markSubscriptionExpired(user.id);
        }

        return {
          hasAccess: false,
          status: 'expired',
          user: user,
          expiresAt: expiresAt,
          message: `Subscription expired on ${expiresAt.toDateString()}`
        };
      }

      // Check if user status is active
      if (user.status !== 'active') {
        return {
          hasAccess: false,
          status: user.status,
          user: user,
          expiresAt: expiresAt,
          message: `Subscription status: ${user.status}`
        };
      }

      // Subscription is active and not expired
      return {
        hasAccess: true,
        status: 'active',
        user: user,
        expiresAt: expiresAt,
        message: `Active until ${expiresAt.toDateString()}`
      };

    } catch (error) {
      logger.error('Error checking subscription status', { error: error.message, email });
      return {
        hasAccess: false,
        status: 'error',
        message: 'Error checking subscription status'
      };
    }
  }

  /**
   * Check subscription status by access code
   */
  async checkSubscriptionByAccessCode(accessCode) {
    try {
      const result = await pool.query(
        `SELECT pu.id, pu.email, pu.subscription_type, pu.status, pu.expires_at, 
                pu.next_billing_date, pu.access_code, pu.is_affiliate
         FROM paid_users pu
         INNER JOIN access_codes ac ON pu.id = ac.paid_user_id
         WHERE ac.code = $1 AND ac.is_active = true`,
        [accessCode]
      );

      if (result.rows.length === 0) {
        return {
          hasAccess: false,
          status: 'invalid_code',
          message: 'Access code not found or inactive'
        };
      }

      const user = result.rows[0];
      return await this.checkSubscriptionStatus(user.email);

    } catch (error) {
      logger.error('Error checking subscription by access code', { error: error.message, accessCode });
      return {
        hasAccess: false,
        status: 'error',
        message: 'Error checking access code'
      };
    }
  }

  /**
   * Mark a subscription as expired
   */
  async markSubscriptionExpired(userId) {
    try {
      await pool.query(
        `UPDATE paid_users 
         SET status = 'expired', updated_at = NOW()
         WHERE id = $1`,
        [userId]
      );

      // Optionally deactivate access codes
      await pool.query(
        `UPDATE access_codes 
         SET is_active = false 
         WHERE paid_user_id = $1`,
        [userId]
      );

      logger.info('Marked subscription as expired', { userId });
    } catch (error) {
      logger.error('Error marking subscription as expired', { error: error.message, userId });
      throw error;
    }
  }

  /**
   * Get all expired subscriptions that need to be marked as expired
   */
  async findExpiredSubscriptions() {
    try {
      const result = await pool.query(
        `SELECT id, email, subscription_type, expires_at, status
         FROM paid_users 
         WHERE expires_at < NOW() 
           AND status = 'active'
           AND subscription_type != 'lifetime'
         ORDER BY expires_at ASC`
      );

      return result.rows;
    } catch (error) {
      logger.error('Error finding expired subscriptions', { error: error.message });
      throw error;
    }
  }

  /**
   * Process expired subscriptions (mark them as expired)
   */
  async processExpiredSubscriptions() {
    try {
      const expiredUsers = await this.findExpiredSubscriptions();
      
      if (expiredUsers.length === 0) {
        logger.info('No expired subscriptions found');
        return { processed: 0, expired: [] };
      }

      logger.info(`Found ${expiredUsers.length} expired subscriptions to process`);

      for (const user of expiredUsers) {
        await this.markSubscriptionExpired(user.id);
      }

      return { 
        processed: expiredUsers.length, 
        expired: expiredUsers.map(u => ({ 
          email: u.email, 
          expiresAt: u.expires_at,
          subscriptionType: u.subscription_type 
        }))
      };

    } catch (error) {
      logger.error('Error processing expired subscriptions', { error: error.message });
      throw error;
    }
  }

  /**
   * Get subscriptions expiring within the next N days
   */
  async getExpiringSubscriptions(daysAhead = 7) {
    try {
      const result = await pool.query(
        `SELECT id, email, subscription_type, expires_at, next_billing_date, status
         FROM paid_users 
         WHERE expires_at BETWEEN NOW() AND NOW() + INTERVAL '${daysAhead} days'
           AND status = 'active'
           AND subscription_type != 'lifetime'
         ORDER BY expires_at ASC`
      );

      return result.rows;
    } catch (error) {
      logger.error('Error getting expiring subscriptions', { error: error.message });
      throw error;
    }
  }

  /**
   * Extend subscription by billing period (for renewals)
   */
  async extendSubscription(email, billingFrequencyDays = null) {
    try {
      const user = await pool.query(
        'SELECT * FROM paid_users WHERE email = $1',
        [email]
      );

      if (user.rows.length === 0) {
        throw new Error('User not found');
      }

      const userData = user.rows[0];
      
      // Lifetime users don't need extensions
      if (userData.subscription_type === 'lifetime') {
        logger.info('Attempted to extend lifetime subscription - no action needed', { email });
        return {
          success: true,
          message: 'Lifetime subscription - no extension needed'
        };
      }
      
      const frequencyDays = billingFrequencyDays || userData.billing_frequency_days || 30;
      
      // Calculate new expiration date
      const currentExpires = userData.expires_at ? new Date(userData.expires_at) : new Date();
      const newExpiresAt = new Date(currentExpires.getTime() + (frequencyDays * 24 * 60 * 60 * 1000));
      
      // Calculate new next billing date
      const newNextBilling = new Date(newExpiresAt.getTime() + (frequencyDays * 24 * 60 * 60 * 1000));

      await pool.query(
        `UPDATE paid_users 
         SET status = 'active',
             expires_at = $1,
             next_billing_date = $2,
             updated_at = NOW()
         WHERE email = $3`,
        [newExpiresAt, newNextBilling, email]
      );

      logger.info('Extended subscription', { 
        email, 
        newExpiresAt, 
        frequencyDays 
      });

      return {
        success: true,
        newExpiresAt,
        newNextBilling
      };

    } catch (error) {
      logger.error('Error extending subscription', { error: error.message, email });
      throw error;
    }
  }

  /**
   * Get renewal URL for expired/expiring users
   */
  getRenewalUrl(subscriptionType, affiliateCode = null) {
    const baseUrls = {
      weekly: process.env.THRIVECART_WEEKLY_URL || 'https://aeon.thrivecart.com/weekly-subscription',
      monthly: process.env.THRIVECART_MONTHLY_URL || 'https://aeon.thrivecart.com/monthly-subscription', 
      yearly: process.env.THRIVECART_YEARLY_URL || 'https://aeon.thrivecart.com/annual-subscription'
    };

    const baseUrl = baseUrls[subscriptionType] || baseUrls.monthly;
    
    if (affiliateCode) {
      return `${baseUrl}?affiliate=${affiliateCode}`;
    }
    
    return baseUrl;
  }

  /**
   * Create renewal message for users
   */
  createRenewalMessage(user) {
    // Lifetime users don't need renewal messages
    if (user.subscription_type === 'lifetime') {
      return {
        message: "You have lifetime access! No renewal needed.",
        renewalUrl: null
      };
    }
    
    const renewalUrl = this.getRenewalUrl(user.subscription_type);
    const subscriptionTypeText = user.subscription_type.charAt(0).toUpperCase() + user.subscription_type.slice(1);
    
    return {
      message: `⚠️ Your ${subscriptionTypeText} subscription has expired. To continue using the habit tracker, please renew your subscription:\n\n${renewalUrl}\n\nOnce renewed, you'll receive a new access code.`,
      renewalUrl,
      subscriptionType: user.subscription_type
    };
  }
}

module.exports = new SubscriptionService();