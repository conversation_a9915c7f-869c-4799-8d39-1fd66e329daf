const pool = require('../db/connection');
const logger = require('../config/logger');
const { validateAccessCode } = require('../utils/codeGenerator');
const subscriptionService = require('./subscriptionService');

class PaymentService {
  constructor() {
    this.testMode = process.env.PAYMENT_TEST_MODE === 'true';
  }

  /**
   * Check if user has valid payment/access
   */
  async checkUserAccess(phone) {
    try {
      // First check if user is linked to a paid account by phone
      const paidUserResult = await pool.query(
        'SELECT email FROM paid_users WHERE phone = $1',
        [phone]
      );

      if (paidUserResult.rows.length === 0) {
        // No payment found - deny access
        logger.info('User has no linked subscription', { phone, testMode: this.testMode });
        return { hasAccess: false, testMode: this.testMode };
      }

      const email = paidUserResult.rows[0].email;
      
      // Use subscription service for comprehensive status check
      const subscriptionStatus = await subscriptionService.checkSubscriptionStatus(email);
      
      if (subscriptionStatus.hasAccess) {
        // User has active subscription
        logger.info('User has active subscription', { 
          phone: phone.substring(0, 5) + '***',
          email: email.substring(0, 3) + '***',
          status: subscriptionStatus.status,
          expiresAt: subscriptionStatus.expiresAt
        });
        
        return { 
          hasAccess: true, 
          paidUser: subscriptionStatus.user,
          testMode: this.testMode,
          expiresAt: subscriptionStatus.expiresAt,
          status: subscriptionStatus.status,
          message: subscriptionStatus.message
        };
      } else {
        // User subscription is expired/inactive
        logger.info('User subscription access denied', { 
          phone: phone.substring(0, 5) + '***',
          email: email.substring(0, 3) + '***',
          status: subscriptionStatus.status,
          reason: subscriptionStatus.message
        });
        
        return { 
          hasAccess: false,
          isExpired: subscriptionStatus.status === 'expired',
          user: subscriptionStatus.user,
          status: subscriptionStatus.status,
          message: subscriptionStatus.message,
          expiresAt: subscriptionStatus.expiresAt,
          testMode: this.testMode
        };
      }
      
    } catch (error) {
      logger.error('Error checking user access', { error: error.message });
      // In case of error, deny access
      return { hasAccess: false, error: true };
    }
  }

  /**
   * Activate access code and link to phone number
   */
  async activateAccessCode(phone, code) {
    const client = await pool.connect();
    
    try {
      // Validate code format
      if (!validateAccessCode(code)) {
        return {
          success: false,
          message: 'Invalid access code format. Please check and try again.'
        };
      }

      await client.query('BEGIN');

      // Find the access code and associated paid user
      const codeResult = await client.query(
        `SELECT ac.*, pu.* 
         FROM access_codes ac
         JOIN paid_users pu ON ac.paid_user_id = pu.id
         WHERE UPPER(ac.code) = UPPER($1) AND ac.is_active = true`,
        [code]
      );

      if (codeResult.rows.length === 0) {
        await client.query('ROLLBACK');
        return {
          success: false,
          message: 'Access code not found or already used. Please check your code.'
        };
      }

      const accessCode = codeResult.rows[0];

      // Check if code is already used
      if (accessCode.used_by_phone) {
        await client.query('ROLLBACK');
        return {
          success: false,
          message: 'This access code has already been activated.'
        };
      }

      // Update access code as used
      await client.query(
        `UPDATE access_codes 
         SET used_by_phone = $1, used_at = NOW()
         WHERE id = $2`,
        [phone, accessCode.id]
      );

      // Link phone to paid user
      await client.query(
        `UPDATE paid_users 
         SET phone = $1, updated_at = NOW()
         WHERE id = $2`,
        [phone, accessCode.paid_user_id]
      );

      // Update user status to unlocked
      await client.query(
        `UPDATE users 
         SET status = 'ONBOARDING',
             updated_at = NOW()
         WHERE phone = $1`,
        [phone]
      );

      await client.query('COMMIT');

      logger.info('Access code activated successfully', {
        phone,
        code,
        subscriptionType: accessCode.subscription_type
      });

      return {
        success: true,
        message: `Success! Your ${accessCode.subscription_type} subscription is now active. Let's set up your habits!`,
        subscriptionType: accessCode.subscription_type,
        isAffiliate: accessCode.is_affiliate,
        affiliateCode: accessCode.affiliate_code
      };
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error activating access code', { error: error.message });
      return {
        success: false,
        message: 'An error occurred while activating your code. Please try again.'
      };
    } finally {
      client.release();
    }
  }

  /**
   * Get paywall message for locked users
   */
  getPaywallMessage(userStatus = null) {
    // Support both ThriveCart and FastSpring URLs
    const checkoutUrl = process.env.THRIVECART_CHECKOUT_URL || 
                       process.env.FASTSPRING_CHECKOUT_URL || 
                       'https://aeon.thrivecart.com/lock-in-whatsapp-habit-tracker-yearly/';
    
    // Different message for expired vs new users
    if (userStatus?.isExpired) {
      return `⏰ SUBSCRIPTION EXPIRED

Your ${userStatus.subscriptionType} subscription expired on ${new Date(userStatus.expiredAt).toLocaleDateString()}.

🔄 **RENEW NOW TO RESTORE ACCESS:**
• All your habit data is safely stored
• Resume tracking immediately after renewal
• Continue your progress streak

💰 **RENEWAL OPTIONS:**
• Weekly: $2/week  
• Monthly: $5/month
• Yearly: $30/year (Best Value - Save 50%!)

🔐 **RENEW ACCESS:**
1. Purchase renewal: ${checkoutUrl}
2. Check your email for new access code  
3. Text: START HABIT-XXXXXX

❓ Questions? <EMAIL>

⚠️ Renew today to keep your habit streak!`;
    }
    
    return `🚫 ACCESS REQUIRED

Habit Tracker is a premium service that helps you build lasting habits with daily tracking and progress insights.

💰 **PRICING:**
• Weekly: $2/week
• Monthly: $5/month  
• Yearly: $30/year (Best Value - Save 50%!)

🎉 **FEATURES INCLUDED:** 
• Track up to 5 daily habits
• Detailed progress analytics
• Motivational content & reminders
• Data export & backup
• Priority support

🔐 **GET ACCESS:**
1. Purchase subscription: ${checkoutUrl}
2. Check your email for access code
3. Text: START HABIT-XXXXXX

❓ Questions? <EMAIL>

⚠️ All bot functions require active subscription`;
  }

  /**
   * Process affiliate referral
   */
  async processAffiliateReferral(affiliateCode, newUserId, orderAmount) {
    try {
      // Find affiliate by code
      const affiliateResult = await pool.query(
        'SELECT id, commission_rate FROM paid_users WHERE UPPER(affiliate_code) = UPPER($1) AND is_affiliate = true',
        [affiliateCode]
      );

      if (affiliateResult.rows.length === 0) {
        logger.warn('Affiliate code not found', { affiliateCode });
        return false;
      }

      const affiliate = affiliateResult.rows[0];
      const commissionAmount = (orderAmount * affiliate.commission_rate) / 100;

      // Create referral record
      await pool.query(
        `INSERT INTO affiliate_referrals (
          affiliate_id, referred_user_id, commission_amount, 
          referral_amount, commission_status
        ) VALUES ($1, $2, $3, $4, 'pending')`,
        [affiliate.id, newUserId, commissionAmount, orderAmount]
      );

      logger.info('Affiliate referral processed', {
        affiliateId: affiliate.id,
        commissionAmount,
        orderAmount
      });

      return true;
    } catch (error) {
      logger.error('Error processing affiliate referral', { error: error.message });
      return false;
    }
  }

  /**
   * Get user subscription details
   */
  async getSubscriptionDetails(phone) {
    try {
      const result = await pool.query(
        `SELECT 
          subscription_type,
          status,
          paid_at,
          expires_at,
          is_affiliate,
          affiliate_code
         FROM paid_users
         WHERE phone = $1 AND status = 'active'`,
        [phone]
      );

      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error getting subscription details', { error: error.message });
      return null;
    }
  }

  /**
   * Check and handle expired subscriptions using subscription service
   */
  async checkExpiredSubscriptions() {
    try {
      logger.info('Starting scheduled subscription expiration check');
      
      // Use subscription service for comprehensive processing
      const result = await subscriptionService.processExpiredSubscriptions();
      
      if (result.processed > 0) {
        logger.info('Processed expired subscriptions', { 
          count: result.processed,
          expired: result.expired 
        });
        
        // Queue expiration emails with renewal links
        for (const expiredUser of result.expired) {
          const renewalUrl = subscriptionService.getRenewalUrl(expiredUser.subscriptionType);
          
          const templateData = {
            subscriptionType: expiredUser.subscriptionType,
            expiresAt: expiredUser.expiresAt,
            renewalUrl: renewalUrl
          };

          await pool.query(
            `INSERT INTO email_queue (to_email, subject, template, template_data, priority)
             VALUES ($1, $2, $3, $4, $5)`,
            [
              expiredUser.email, 
              'Subscription Expired - Renew Now', 
              'subscription_expired_renewal',
              JSON.stringify(templateData),
              2 // High priority
            ]
          );
        }
      }

      // Also check for subscriptions expiring in the next 3 days for warning emails
      const expiringSoon = await subscriptionService.getExpiringSubscriptions(3);
      
      if (expiringSoon.length > 0) {
        logger.info('Found subscriptions expiring soon', { count: expiringSoon.length });
        
        for (const user of expiringSoon) {
          const renewalUrl = subscriptionService.getRenewalUrl(user.subscription_type);
          const daysLeft = Math.ceil((new Date(user.expires_at) - new Date()) / (1000 * 60 * 60 * 24));
          
          const templateData = {
            subscriptionType: user.subscription_type,
            expiresAt: user.expires_at,
            daysLeft: daysLeft,
            renewalUrl: renewalUrl
          };

          await pool.query(
            `INSERT INTO email_queue (to_email, subject, template, template_data, priority)
             VALUES ($1, $2, $3, $4, $5)`,
            [
              user.email, 
              `Subscription Expires in ${daysLeft} Day${daysLeft > 1 ? 's' : ''}`, 
              'subscription_expiring_warning',
              JSON.stringify(templateData),
              1 // Normal priority
            ]
          );
        }
      }

      return result;
      
    } catch (error) {
      logger.error('Error in scheduled subscription check', { error: error.message });
      return { processed: 0, expired: [], error: error.message };
    }
  }

  /**
   * Start subscription checker (runs every 6 hours for better responsiveness)
   */
  startSubscriptionChecker() {
    logger.info('Starting subscription checker service');
    
    // Check on startup (with delay to let services initialize)
    setTimeout(() => {
      this.checkExpiredSubscriptions();
    }, 30000); // 30 second delay
    
    // Check every 6 hours for better responsiveness
    const checkInterval = 6 * 60 * 60 * 1000; // 6 hours in milliseconds
    setInterval(() => {
      this.checkExpiredSubscriptions();
    }, checkInterval);
    
    logger.info('Subscription checker scheduled to run every 6 hours');
  }

  /**
   * Manual subscription check (for testing or admin use)
   */
  async manualSubscriptionCheck() {
    logger.info('Manual subscription check triggered');
    return await this.checkExpiredSubscriptions();
  }
}

module.exports = new PaymentService();