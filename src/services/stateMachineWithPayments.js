const User = require('../models/User');
const Habit = require('../models/Habit');
const AuditLog = require('../models/AuditLog');
const paymentService = require('./paymentService');
const { STATES, USER_STATUS, AUDIT_EVENTS, HABITS } = require('../config/constants');
const logger = require('../config/logger');
const moment = require('moment-timezone');
const motivationalQuotes = require('../utils/motivationalQuotes');

class StateMachineWithPayments {
  constructor() {
    // Copy the original state machine transitions
    this.originalStateMachine = require('./stateMachine');
    this.transitions = this.originalStateMachine.transitions;
  }

  async processMessage(user, message) {
    try {
      // Update last active
      await User.updateLastActive(user.id);

      // Check for START HABIT-XXXXX command
      const upperMessage = message.trim().toUpperCase();
      if (upperMessage.startsWith('START HABIT-')) {
        return await this.handleAccessCodeActivation(user, message);
      }

      // DEVELOPER TEST COMMAND: Reset user to clean slate
      if (upperMessage === 'RESET_TEST') {
        return await this.originalStateMachine.handleResetTest(user);
      }

      // Check payment access for locked users
      if (user.status === USER_STATUS.LOCKED) {
        const normalizedPhone = User.normalizePhone(user.phone);
        const accessCheck = await paymentService.checkUserAccess(normalizedPhone);
        
        if (!accessCheck.hasAccess) {
          // No access - show paywall
          return {
            message: paymentService.getPaywallMessage(accessCheck),
            newState: STATES.MAIN_MENU
          };
        }
        
        // Has access - unlock user and continue
        await User.updateStatus(user.id, USER_STATUS.ONBOARDING);
        logger.info('Auto-unlocked paid user', { userId: user.id, phone: normalizedPhone });
      }

      // Use original state machine for everything else
      return await this.originalStateMachine.processMessage(user, message);
    } catch (error) {
      logger.error('State machine error', { 
        userId: user.id, 
        state: user.current_state, 
        error: error.message 
      });
      
      await AuditLog.log(user.id, AUDIT_EVENTS.ERROR, {
        state: user.current_state,
        error: error.message
      });

      return {
        message: "Sorry, something went wrong. Please try again or type 'menu' to return to the main menu.",
        newState: user.current_state
      };
    }
  }

  /**
   * Handle START HABIT-XXXXX command for access code activation
   */
  async handleAccessCodeActivation(user, message) {
    try {
      // Extract access code from message
      const match = message.trim().toUpperCase().match(/^START\s+(HABIT-[A-Z0-9]{6})$/);
      
      if (!match) {
        return {
          message: `Invalid format. Please use exactly:
START HABIT-XXXXXX

Example: START HABIT-ABC123

Make sure to include the space between START and your access code.`,
          newState: user.current_state
        };
      }

      const accessCode = match[1];
      const normalizedPhone = User.normalizePhone(user.phone);
      
      // Activate the access code
      const result = await paymentService.activateAccessCode(normalizedPhone, accessCode);
      
      if (!result.success) {
        return {
          message: result.message,
          newState: user.current_state
        };
      }

      // Success - update user status and start onboarding
      await User.updateStatus(user.id, USER_STATUS.ONBOARDING);
      await User.updateState(user.id, STATES.AWAITING_NAME);
      
      let welcomeMessage = `🎉 ${result.message}

Welcome to Habit Tracker! Let's get you set up.

What should I call you? (Enter your first name)`;

      // Add affiliate info for yearly subscribers
      if (result.isAffiliate && result.affiliateCode) {
        welcomeMessage = `🎉 ${result.message}

🎊 BONUS: As a yearly subscriber, you're automatically enrolled in our affiliate program!
Your affiliate code: ${result.affiliateCode}
Share it to earn 30% commission on referrals!

Now let's set up your habit tracking.

What should I call you? (Enter your first name)`;
      }

      await AuditLog.log(user.id, 'ACCESS_CODE_ACTIVATED', {
        accessCode,
        subscriptionType: result.subscriptionType,
        isAffiliate: result.isAffiliate
      });

      return {
        message: welcomeMessage,
        newState: STATES.AWAITING_NAME
      };
    } catch (error) {
      logger.error('Error handling access code activation', { 
        error: error.message,
        userId: user.id 
      });
      
      return {
        message: 'Sorry, there was an error activating your access code. Please try again or contact support.',
        newState: user.current_state
      };
    }
  }
}

module.exports = new StateMachineWithPayments();