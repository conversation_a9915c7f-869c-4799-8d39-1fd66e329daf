const User = require('../models/User');
const Habit = require('../models/Habit');
const AuditLog = require('../models/AuditLog');
const paymentService = require('./paymentService');
const pool = require('../db/connection');
const { STATES, USER_STATUS, AUDIT_EVENTS, HABITS } = require('../config/constants');
const logger = require('../config/logger');
const moment = require('moment-timezone');
const motivationalQuotes = require('../utils/motivationalQuotes');

class StateMachinePaymentEnforced {
  constructor() {
    // Copy the original state machine transitions
    this.originalStateMachine = require('./stateMachine');
    this.transitions = this.originalStateMachine.transitions;
  }

  async processMessage(user, message) {
    try {
      // Update last active
      await User.updateLastActive(user.id);

      const upperMessage = message.trim().toUpperCase();
      const normalizedPhone = User.normalizePhone(user.phone);

      // PRIORITY 1: Handle START HABIT-XXXXX command (always allowed)
      if (upperMessage.startsWith('START HABIT-')) {
        return await this.handleAccessCodeActivation(user, message);
      }

      // PRIORITY 2: Handle RESET_PAYMENT_TEST command (test mode only)
      if (upperMessage === 'RESET_PAYMENT_TEST' && process.env.PAYMENT_TEST_MODE === 'true') {
        return await this.handlePaymentTestReset(user);
      }

      // PRIORITY 3: Handle RESET_TEST command (developer test)
      if (upperMessage === 'RESET_TEST') {
        return await this.originalStateMachine.handleResetTest(user);
      }

      // PRIORITY 4: CHECK PAYMENT STATUS FOR ALL OTHER COMMANDS
      const accessCheck = await paymentService.checkUserAccess(normalizedPhone);
      
      if (!accessCheck.hasAccess) {
        // NO ACCESS - Show paywall for ANY command
        logger.info('Payment required - showing paywall', { 
          userId: user.id, 
          phone: normalizedPhone,
          attemptedMessage: message.substring(0, 20)
        });
        
        return {
          message: paymentService.getPaywallMessage(accessCheck),
          newState: STATES.MAIN_MENU
        };
      }

      // Has payment access - check if user needs to be unlocked
      if (user.status === USER_STATUS.LOCKED) {
        // Auto-unlock paid user
        await User.updateStatus(user.id, USER_STATUS.ONBOARDING);
        logger.info('Auto-unlocked paid user', { 
          userId: user.id, 
          phone: normalizedPhone,
          paidUserId: accessCheck.paidUser?.id
        });
        
        // Check if they have a name set
        if (!user.display_name) {
          await User.updateState(user.id, STATES.AWAITING_NAME);
          return {
            message: `Welcome back to Habit Tracker! 🎯

I see you have an active subscription. Let's get you set up.

What should I call you? (Enter your first name)`,
            newState: STATES.AWAITING_NAME
          };
        }
      }

      // User has payment access - use original state machine
      return await this.originalStateMachine.processMessage(user, message);
      
    } catch (error) {
      logger.error('State machine error', { 
        userId: user.id, 
        state: user.current_state, 
        error: error.message 
      });
      
      await AuditLog.log(user.id, AUDIT_EVENTS.ERROR, {
        state: user.current_state,
        error: error.message
      });

      return {
        message: "Sorry, something went wrong. Please try again or type 'menu' to return to the main menu.",
        newState: user.current_state
      };
    }
  }

  /**
   * Handle START HABIT-XXXXX command for access code activation
   */
  async handleAccessCodeActivation(user, message) {
    try {
      // Extract access code from message
      const match = message.trim().toUpperCase().match(/^START\s+(HABIT-[A-Z0-9]{6})$/);
      
      if (!match) {
        return {
          message: `Invalid format. Please use exactly:
START HABIT-XXXXXX

Example: START HABIT-ABC123

Make sure to include the space between START and your access code.`,
          newState: user.current_state
        };
      }

      const accessCode = match[1];
      const normalizedPhone = User.normalizePhone(user.phone);
      
      // Check if user already has access
      const existingAccess = await paymentService.checkUserAccess(normalizedPhone);
      if (existingAccess.hasAccess) {
        return {
          message: `You already have an active subscription! 

Type 'menu' to see your options or start tracking your habits.`,
          newState: user.current_state
        };
      }
      
      // Activate the access code
      const result = await paymentService.activateAccessCode(normalizedPhone, accessCode);
      
      if (!result.success) {
        return {
          message: result.message,
          newState: user.current_state
        };
      }

      // Success - update user status and skip compliance, go directly to name collection
      await User.updateStatus(user.id, USER_STATUS.ONBOARDING);
      await User.updateState(user.id, STATES.AWAITING_NAME);
      
      let welcomeMessage = `🎉 ${result.message}

Welcome to Habit Tracker! 🎯

Let's set up your profile.
What should I call you? (Enter your first name)`;

      // Add affiliate info for yearly subscribers
      if (result.isAffiliate && result.affiliateCode) {
        welcomeMessage = `🎉 ${result.message}

🎊 BONUS: As a yearly subscriber, you're automatically enrolled in our affiliate program!
Your affiliate code: ${result.affiliateCode}
Share it to earn 30% commission on referrals!

Now let's set up your habit tracking.

What should I call you? (Enter your first name)`;
      }

      await AuditLog.log(user.id, 'ACCESS_CODE_ACTIVATED', {
        accessCode,
        subscriptionType: result.subscriptionType,
        isAffiliate: result.isAffiliate
      });

      return {
        message: welcomeMessage,
        newState: STATES.AWAITING_NAME
      };
    } catch (error) {
      logger.error('Error handling access code activation', { 
        error: error.message,
        userId: user.id 
      });
      
      return {
        message: 'Sorry, there was an error activating your access code. Please try again or contact support.',
        newState: user.current_state
      };
    }
  }

  /**
   * Handle RESET_PAYMENT_TEST command for development testing
   */
  async handlePaymentTestReset(user) {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      const normalizedPhone = User.normalizePhone(user.phone);
      
      // Find and remove payment records
      const paidUserResult = await client.query(
        'SELECT id FROM paid_users WHERE phone = $1',
        [normalizedPhone]
      );
      
      if (paidUserResult.rows.length > 0) {
        const paidUserId = paidUserResult.rows[0].id;
        
        // Clear related records
        await client.query('DELETE FROM affiliate_referrals WHERE referred_user_id = $1', [paidUserId]);
        await client.query('DELETE FROM payment_transactions WHERE paid_user_id = $1', [paidUserId]);
        await client.query('DELETE FROM access_codes WHERE paid_user_id = $1', [paidUserId]);
        await client.query('DELETE FROM paid_users WHERE id = $1', [paidUserId]);
        
        logger.info('Payment test reset - removed payment records', {
          userId: user.id,
          paidUserId,
          phone: normalizedPhone
        });
      }
      
      // Reset user to locked state
      await client.query(
        `UPDATE users 
         SET status = 'LOCKED',
             current_state = 'MAIN_MENU',
             session_context = '{}',
             display_name = NULL,
             updated_at = NOW()
         WHERE id = $1`,
        [user.id]
      );
      
      // Clear user's habits and logs
      await client.query('DELETE FROM habit_logs WHERE user_id = $1', [user.id]);
      await client.query('DELETE FROM habits WHERE user_id = $1', [user.id]);
      
      await client.query('COMMIT');
      
      await AuditLog.log(user.id, 'PAYMENT_TEST_RESET', {
        phone: normalizedPhone,
        testMode: true
      });
      
      return {
        message: `🔄 PAYMENT TEST RESET COMPLETE

Your payment status has been cleared. You are now an unpaid user.

✅ Removed:
- Payment records
- Access codes
- User profile
- All habits

You can now test the full payment flow:
1. Send any message to see the paywall
2. Use test endpoint to create a payment
3. Activate with START HABIT-XXXXX

Ready to test the paywall experience!`,
        newState: STATES.MAIN_MENU
      };
      
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error resetting payment test', {
        error: error.message,
        userId: user.id
      });
      
      return {
        message: 'Error resetting payment test. Please try again.',
        newState: user.current_state
      };
    } finally {
      client.release();
    }
  }
}

module.exports = new StateMachinePaymentEnforced();