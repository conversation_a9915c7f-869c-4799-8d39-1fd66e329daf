#!/usr/bin/env node
require('dotenv').config();

const stateMachine = require('./src/services/stateMachinePaymentEnforced');
const User = require('./src/models/User');

async function testFreshCodes() {
  console.log('🧪 Testing Fresh Access Codes\n');
  
  const codes = ['HABIT-C98D31', 'HABIT-3E5D57']; // Fresh codes from database
  
  for (let i = 0; i < codes.length; i++) {
    const code = codes[i];
    const testPhone = `+155512345${i}`; // Different phone for each test
    
    console.log(`📱 Testing code: ${code} with phone: ${testPhone}`);
    console.log('=' .repeat(50));
    
    try {
      // Create user
      const user = await User.findOrCreate(testPhone);
      console.log('👤 Created user:', user.status);
      
      // Test paywall first
      const paywallResponse = await stateMachine.processMessage(user, 'test');
      console.log('🚫 Paywall test:', paywallResponse.message.includes('ACCESS REQUIRED') ? 'SHOWN ✅' : 'NOT SHOWN ❌');
      
      // Test START command
      console.log(`🔑 Testing: START ${code}`);
      const startResponse = await stateMachine.processMessage(user, `START ${code}`);
      
      if (startResponse.message.includes('Success') || startResponse.message.includes('🎉')) {
        console.log('✅ Code activation: SUCCESS');
        console.log('🎯 Response preview:', startResponse.message.substring(0, 100) + '...');
        
        // Test post-activation access
        const updatedUser = await User.findByPhone(testPhone);
        const postResponse = await stateMachine.processMessage(updatedUser, 'hello');
        console.log('🎯 Post-activation access:', !postResponse.message.includes('ACCESS REQUIRED') ? 'WORKING ✅' : 'FAILED ❌');
      } else {
        console.log('❌ Code activation: FAILED');
        console.log('📝 Response:', startResponse.message.substring(0, 200));
      }
      
    } catch (error) {
      console.log('❌ Error:', error.message);
    }
    
    console.log();
  }
  
  console.log('✨ Fresh code test completed!');
  console.log('\n📋 SUMMARY:');
  console.log('- HABIT-C98D31 (monthly): Should work');
  console.log('- HABIT-3E5D57 (yearly): Should work');
  console.log('\n💡 If codes still don\'t work, check:');
  console.log('1. Exact format: "START HABIT-XXXXXX"');
  console.log('2. Code exists in database');
  console.log('3. Code not already used');
  console.log('4. No typos in code');
}

testFreshCodes().catch(console.error);