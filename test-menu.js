#!/usr/bin/env node

/**
 * Quick test script to see the main menu with a clean slate
 * Run: node test-menu.js
 */

require('dotenv').config();
const pool = require('./src/db/connection');
const stateMachine = require('./src/services/stateMachine');

async function testMenu() {
  let client;
  try {
    // Test phone number
    const testPhone = '+10000000000';
    
    client = await pool.connect();
    
    // Clear today's logs for clean slate and reset user state
    console.log('🧹 Clearing today\'s logs and resetting user state...\n');
    await client.query(
      'DELETE FROM habit_logs WHERE user_id = (SELECT id FROM users WHERE phone = $1) AND log_date = CURRENT_DATE',
      [testPhone]
    );
    
    // Reset user state to MAIN_MENU
    await client.query(
      'UPDATE users SET current_state = $1, session_context = $2 WHERE phone = $3',
      ['MAIN_MENU', '{}', testPhone]
    );
    
    // Get user
    const userResult = await client.query(
      'SELECT * FROM users WHERE phone = $1',
      [testPhone]
    );
    
    if (userResult.rows.length === 0) {
      console.log('❌ Test user not found. Creating one...');
      // Create test user if doesn't exist
      await client.query(
        'INSERT INTO users (phone, display_name, status, timezone) VALUES ($1, $2, $3, $4)',
        [testPhone, 'Rizz', 'ACTIVE', 'America/New_York']
      );
    }
    
    const user = userResult.rows[0];
    
    // Ensure user has 5 habits
    const habits = [
      'Subliminals',
      'Salt Water', 
      'Gym',
      '10k Steps',
      'Journal'
    ];
    
    for (let i = 0; i < habits.length; i++) {
      await client.query(
        'INSERT INTO habits (user_id, habit_number, habit_name) VALUES ($1, $2, $3) ON CONFLICT (user_id, habit_number) DO UPDATE SET habit_name = $3',
        [user.id, i + 1, habits[i]]
      );
    }
    
    // Test the main menu - need to get fresh user data after habits created
    const freshUserResult = await client.query('SELECT * FROM users WHERE phone = $1', [testPhone]);
    const freshUser = freshUserResult.rows[0];
    
    console.log('📱 BLANK SLATE TEST (no habits logged):\n');
    console.log('=' .repeat(50));
    const response1 = await stateMachine.handleMainMenu(freshUser, '');
    console.log(response1.message);
    console.log('=' .repeat(50));
    
    // Add some partial logs
    console.log('\n📱 PARTIALLY LOGGED TEST:\n');
    console.log('=' .repeat(50));
    
    // Log habits 2 and 3 as completed
    await client.query(
      `INSERT INTO habit_logs (user_id, habit_id, log_date, completed) 
       SELECT $1, id, CURRENT_DATE, true 
       FROM habits 
       WHERE user_id = $1 AND habit_number IN (2, 3)`,
      [user.id]
    );
    
    const response2 = await stateMachine.handleMainMenu(freshUser, '');
    console.log(response2.message);
    console.log('=' .repeat(50));
    
    // Log all habits
    console.log('\n📱 FULLY LOGGED TEST:\n');
    console.log('=' .repeat(50));
    
    // Log remaining habits (1 as false, 4 as false, 5 as true)
    await client.query(
      `INSERT INTO habit_logs (user_id, habit_id, log_date, completed) 
       SELECT $1, id, CURRENT_DATE, 
         CASE habit_number 
           WHEN 1 THEN false
           WHEN 4 THEN false  
           WHEN 5 THEN true
         END
       FROM habits 
       WHERE user_id = $1 AND habit_number IN (1, 4, 5)`,
      [user.id]
    );
    
    const response3 = await stateMachine.handleMainMenu(freshUser, '');
    console.log(response3.message);
    console.log('=' .repeat(50));
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    if (client) client.release();
    await pool.end();
  }
}

// Run the test
testMenu().catch(console.error);