#!/usr/bin/env node

require('dotenv').config();
const nodemailer = require('nodemailer');
const emailService = require('./src/services/emailService');
const pool = require('./src/db/connection');

async function debugSMTPSending() {
  try {
    console.log('🔍 DEBUG SMTP SENDING DIFFERENCE...');
    
    // Get the most recent production email data
    const result = await pool.query("SELECT template_data FROM email_queue WHERE to_email = '<EMAIL>' ORDER BY id DESC LIMIT 1");
    const templateData = result.rows[0].template_data;
    
    const { subject, html, text } = emailService.getWelcomeDynamicTemplate(templateData);
    
    // Create transporter exactly like production
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_PORT === '465',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
    
    console.log('📧 Sending email with PRODUCTION EMAIL QUEUE METHOD...');
    
    // Use the EXACT same sendMail call as the production email queue
    const mailOptions = {
      from: `"Lockin Habit Tracker" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: '<EMAIL>',
      subject: subject,
      html,
      text,
      replyTo: process.env.EMAIL_REPLY_TO || '<EMAIL>'
    };
    
    console.log('📊 Email options:');
    console.log('  From:', mailOptions.from);
    console.log('  Subject:', mailOptions.subject);
    console.log('  HTML length:', mailOptions.html.length);
    console.log('  Text length:', mailOptions.text.length);
    console.log('  ReplyTo:', mailOptions.replyTo);
    
    const result2 = await transporter.sendMail(mailOptions);
    
    console.log('✅ PRODUCTION-STYLE EMAIL SENT!');
    console.log('Message ID:', result2.messageId);
    console.log('Response:', result2.response);
    
    console.log('\n🎯 CHECK YOUR EMAIL:');
    console.log('Subject: "🚀 Your LOCK IN Access Code Is Here – Time to Build Unstoppable Habits"');
    console.log('This uses the EXACT same method as production email queue');
    console.log('If this email is complete but production emails are truncated,');
    console.log('then there is an issue in the email queue processing logic');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    if (error.response) {
      console.error('SMTP Response:', error.response);
    }
  }
  
  await pool.end();
}

debugSMTPSending().catch(console.error);