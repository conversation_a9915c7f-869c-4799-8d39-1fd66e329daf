--
-- PostgreSQL database dump
--

-- Dumped from database version 14.18 (Ubuntu 14.18-0ubuntu0.22.04.1)
-- Dumped by pg_dump version 14.18 (Ubuntu 14.18-0ubuntu0.22.04.1)

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Data for Name: paid_users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.paid_users (id, email, phone, access_code, subscription_type, subscription_id, customer_id, status, amount_paid, currency, is_affiliate, affiliate_code, commission_rate, paid_at, expires_at, created_at, updated_at, fastspring_order_id, fastspring_subscription_id, test_mode) FROM stdin;
43	<EMAIL>	+27646921984	HABIT-43F99E	monthly	36833828	429583657881185517	active	29.99	USD	f	\N	30.00	2025-08-22 23:47:53.193+00	\N	2025-08-22 23:47:53.174952+00	2025-08-23 00:02:31.994764+00	36833828	\N	t
45	<EMAIL>	\N	HABIT-3C27D6	monthly	36834009	429583657881185517	active	29.99	USD	f	\N	30.00	2025-08-23 00:07:12.198+00	\N	2025-08-23 00:07:12.177584+00	2025-08-23 00:07:12.177584+00	36834009	\N	t
\.


--
-- Data for Name: access_codes; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.access_codes (id, code, paid_user_id, used_by_phone, used_at, is_active, expires_at, created_at, notes) FROM stdin;
37	HABIT-43F99E	43	+27646921984	2025-08-23 00:02:31.994764+00	t	\N	2025-08-22 23:47:53.174952+00	\N
39	HABIT-3C27D6	45	\N	\N	t	\N	2025-08-23 00:07:12.177584+00	\N
\.


--
-- Data for Name: payment_transactions; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.payment_transactions (id, paid_user_id, transaction_id, type, amount, currency, fastspring_order_id, fastspring_reference, status, transaction_date, created_at) FROM stdin;
51	43	36833828	payment	29.99	USD	36833828	\N	completed	2025-08-22 23:47:53.197+00	2025-08-22 23:47:53.174952+00
52	45	36834009	payment	29.99	USD	36834009	\N	completed	2025-08-23 00:07:12.205+00	2025-08-23 00:07:12.177584+00
\.


--
-- Name: access_codes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.access_codes_id_seq', 39, true);


--
-- Name: paid_users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.paid_users_id_seq', 45, true);


--
-- Name: payment_transactions_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.payment_transactions_id_seq', 52, true);


--
-- PostgreSQL database dump complete
--

