#!/usr/bin/env node

require('dotenv').config();
const nodemailer = require('nodemailer');

async function testBrevoTruncation() {
  try {
    console.log('🧪 TESTING BREVO EMAIL TRUNCATION');
    console.log('='.repeat(60));
    
    // Create the exact same transporter as the app
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_PORT === '465',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
    
    // Generate test email with exact same content structure
    const emailService = require('./src/services/emailService');
    
    const templateData = {
      "botPhone": "+19035155547",
      "accessCode": "TEST-EMAIL",
      "subscriptionType": "yearly",
      "subscription_type": "Annual",
      "subscription_price": "$39.99/year",
      "primary_access_code": "TEST-EMAIL",
      "isAffiliate": true,
      "affiliateCode": "TEST-AFF"
    };
    
    const { subject, html, text } = emailService.getWelcomeDynamicTemplate(templateData);
    
    console.log('📊 Email Content Stats:');
    console.log(`   Subject: ${subject}`);
    console.log(`   HTML Length: ${html.length} characters`);
    console.log(`   Text Length: ${text.length} characters`);
    
    // Find the step 5 position
    const step5Index = html.indexOf('Start building unstoppable habits!');
    const afterStep5 = html.substring(step5Index);
    console.log(`   Characters after step 5: ${afterStep5.length}`);
    
    console.log('\n📧 Sending test email through Brevo...');
    
    const testEmailResult = await transporter.sendMail({
      from: `"Test Lockin" <${process.env.EMAIL_FROM}>`,
      to: '<EMAIL>', // Same test email as customer
      subject: 'TEST - Email Truncation Debug',
      html: html,
      text: text,
      replyTo: process.env.EMAIL_REPLY_TO
    });
    
    console.log('✅ Email sent successfully!');
    console.log('Message ID:', testEmailResult.messageId);
    console.log('Response:', testEmailResult.response);
    
    console.log('\n🎯 CHECK YOUR EMAIL INBOX!');
    console.log('   If email is truncated after step 5, the issue is with Brevo');
    console.log('   If email is complete, the issue is elsewhere in the app');
    
  } catch (error) {
    console.error('❌ Error sending test email:', error.message);
    if (error.responseCode) {
      console.error('SMTP Response Code:', error.responseCode);
    }
    if (error.response) {
      console.error('SMTP Response:', error.response);
    }
  }
}

testBrevoTruncation().catch(console.error);