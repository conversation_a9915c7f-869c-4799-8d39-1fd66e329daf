#!/usr/bin/env node

const fs = require('fs');
const html = fs.readFileSync('/var/www/lockin/debug-customer-email.html', 'utf8');

console.log('🔍 DETAILED HTML VALIDATION...');

// Find all style attributes and validate them
const styleRegex = /style="([^"]*)"/g;
let match;
let issues = [];

while ((match = styleRegex.exec(html)) !== null) {
  const styleContent = match[1];
  
  // Check for common CSS issues
  if (styleContent.endsWith(';') === false && !styleContent.endsWith('}')) {
    // Check if it's a complete property (not truncated)
    const lastProp = styleContent.split(';').pop().trim();
    if (lastProp.includes(':') && !lastProp.includes('://')) {
      const [prop, value] = lastProp.split(':');
      if (value && value.trim() && !value.includes('}')) {
        issues.push({
          type: 'Missing semicolon',
          context: match[0],
          line: html.substring(0, match.index).split('\n').length
        });
      }
    }
  }
  
  // Check for unmatched quotes within CSS values
  const quotes = (styleContent.match(/'/g) || []).length;
  if (quotes % 2 !== 0) {
    issues.push({
      type: 'Unmatched quotes',
      context: match[0],
      line: html.substring(0, match.index).split('\n').length
    });
  }
}

if (issues.length > 0) {
  console.log('❌ FOUND HTML ISSUES:');
  issues.forEach((issue, i) => {
    console.log(`\n${i+1}. ${issue.type} (Line ~${issue.line}):`);
    console.log(`   ${issue.context}`);
  });
} else {
  console.log('✅ No obvious HTML syntax issues found');
}

// Check for broken tags
const unclosedTags = html.match(/<[a-zA-Z][^>]*[^/>]$/gm);
if (unclosedTags) {
  console.log('\n❌ UNCLOSED TAGS:');
  unclosedTags.forEach(tag => console.log(`   ${tag}`));
}

// Look for specific truncation indicators around step 5
const step5Index = html.indexOf('Start building unstoppable habits!');
if (step5Index !== -1) {
  const beforeStep5 = html.substring(step5Index - 500, step5Index);
  const afterStep5 = html.substring(step5Index, step5Index + 1000);
  
  console.log('\n🔍 HTML AROUND STEP 5:');
  console.log('Before step 5 (last 100 chars):');
  console.log(beforeStep5.slice(-100));
  console.log('\nAfter step 5 (first 200 chars):');
  console.log(afterStep5.substring(0, 200));
}

console.log(`\n📊 HTML Length: ${html.length} characters`);
console.log(`📊 Style attributes: ${(html.match(/style="/g) || []).length}`);

// Check if HTML ends properly
const htmlEnding = html.slice(-200);
console.log('\nLast 200 characters:');
console.log(htmlEnding);