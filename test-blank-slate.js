#!/usr/bin/env node

require('dotenv').config();
const stateMachine = require('./src/services/stateMachine');
const User = require('./src/models/User');
const pool = require('./src/db/connection');

async function testBlankSlate() {
  try {
    const testPhone = '+27646921984';
    
    console.log('📱 TESTING BLANK SLATE MENU FOR RIZZ');
    console.log('='.repeat(50));
    
    // Get user
    const user = await User.findByPhone(testPhone);
    console.log(`👤 User: ${user.display_name} (${user.phone})`);
    console.log(`📊 State: ${user.current_state}, Status: ${user.status}`);
    
    // Show the menu
    const response = await stateMachine.handleMainMenu(user, '');
    
    console.log('\n🤖 MENU OUTPUT:');
    console.log('─'.repeat(50));
    console.log(response.message);
    console.log('─'.repeat(50));
    
    // Verify database state
    console.log('\n🗄️  DATABASE VERIFICATION:');
    const client = await pool.connect();
    const result = await client.query(`
      SELECT COUNT(*) as total_logs
      FROM habit_logs 
      WHERE user_id = (SELECT id FROM users WHERE phone = $1) 
      AND log_date = CURRENT_DATE
    `, [testPhone]);
    
    console.log(`   Today's logs in database: ${result.rows[0].total_logs}`);
    
    const habitsResult = await client.query(`
      SELECT h.habit_number, h.habit_name, hl.completed
      FROM habits h
      LEFT JOIN habit_logs hl ON h.id = hl.habit_id AND hl.log_date = CURRENT_DATE
      WHERE h.user_id = (SELECT id FROM users WHERE phone = $1)
      ORDER BY h.habit_number
    `, [testPhone]);
    
    console.log('\n   Habit statuses:');
    habitsResult.rows.forEach(row => {
      const status = row.completed === null ? 'NULL (should show ⚠️)' : 
                     row.completed === true ? 'TRUE (should show ✅)' : 
                     'FALSE (should show ❌)';
      console.log(`     ${row.habit_number}. ${row.habit_name}: ${status}`);
    });
    
    client.release();
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await pool.end();
  }
}

testBlankSlate().catch(console.error);