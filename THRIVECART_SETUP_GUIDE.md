# ThriveCart Setup & Configuration Guide

## Quick Start

### Production Webhook URL
```
https://*************:3001/webhook/thrivecart
```

### Development/Testing URL
```
http://localhost:3001/webhook/thrivecart
```

---

## Step 1: ThriveCart Dashboard Configuration

### 1.1 Create Products
1. Log into your ThriveCart dashboard
2. Create two products:
   - **Monthly Subscription**: `Habit Tracker Monthly` - $5/month
   - **Yearly Subscription**: `Habit Tracker Yearly` - $30/year

### 1.2 Configure Webhook Settings

1. Navigate to **Settings → Webhooks** in ThriveCart
2. Click **"Add New Webhook"**
3. Enter the following details:

**Webhook Configuration:**
```
Webhook URL: https://*************:3001/webhook/thrivecart
Events to Send: All Events (or select specific ones below)
```

**Required Events (minimum):**
- ✅ Order Success
- ✅ Subscription Payment
- ✅ Rebill Success
- ✅ Refund
- ✅ Subscription Cancelled
- ✅ Subscription Paused
- ✅ Subscription Resumed

### 1.3 Get Webhook Secret (Optional but Recommended)
1. In webhook settings, click **"Show Secret"**
2. Copy the webhook secret
3. Add to your `.env` file:
```bash
THRIVECART_WEBHOOK_SECRET=your_actual_webhook_secret_here
```

---

## Step 2: Environment Configuration

### 2.1 Update .env File
```bash
# ThriveCart Configuration
THRIVECART_CHECKOUT_URL=https://habittracker.thrivecart.com/habit-tracker-monthly/
THRIVECART_WEBHOOK_SECRET=your_webhook_secret_from_thrivecart
THRIVECART_MONTHLY_PRODUCT=habit-tracker-monthly
THRIVECART_YEARLY_PRODUCT=habit-tracker-yearly

# Payment Test Mode (set to false in production)
PAYMENT_TEST_MODE=false
```

### 2.2 Restart Application
```bash
pm2 restart lockin --update-env
```

---

## Step 3: Checkout Page Setup

### 3.1 Monthly Product Checkout URL
```
https://habittracker.thrivecart.com/habit-tracker-monthly/
```

### 3.2 Yearly Product Checkout URL
```
https://habittracker.thrivecart.com/habit-tracker-yearly/
```

### 3.3 Custom Checkout Page Elements
Add these to your ThriveCart checkout page:

**Customer Fields (Required):**
- Email Address
- Full Name

**Optional Fields:**
- Phone Number (for SMS notifications)
- Country/Region

**Compliance Links:**
- Terms of Service: `/legal/terms-of-service.html`
- Privacy Policy: `/legal/privacy-policy.html`
- Support Email: `<EMAIL>`

---

## Step 4: Testing the Integration

### 4.1 Enable Test Mode
```bash
# In .env file
PAYMENT_TEST_MODE=true
```

### 4.2 Test Individual Webhooks
```bash
# Test all webhook types
node test-thrivecart-webhooks.js

# Test specific webhook
node test-thrivecart-webhooks.js monthlyPurchase

# Test custom purchase
node test-thrivecart-webhooks.<NAME_EMAIL> 5 monthly
```

### 4.3 Test Complete Flow
```bash
node test-thrivecart-integration.js
```

### 4.4 Monitor Webhook Processing
```bash
# Watch logs in real-time
pm2 logs lockin

# Check webhook events in database
PGPASSWORD=postgres psql -U postgres -h localhost -d lockin -c \
  "SELECT event_type, payload->>'event', created_at 
   FROM webhook_events 
   WHERE source = 'thrivecart' 
   ORDER BY created_at DESC LIMIT 10;"
```

---

## Step 5: Verify Integration

### 5.1 Check Database Records
```sql
-- Check recent paid users
SELECT email, access_code, subscription_type, status, created_at 
FROM paid_users 
ORDER BY created_at DESC 
LIMIT 5;

-- Check access codes
SELECT code, is_active, used_by_phone, created_at 
FROM access_codes 
ORDER BY created_at DESC 
LIMIT 5;

-- Check payment transactions
SELECT transaction_id, type, amount, transaction_date 
FROM payment_transactions 
ORDER BY transaction_date DESC 
LIMIT 5;
```

### 5.2 Test Bot Access
1. Make a test purchase through ThriveCart
2. Check email for access code
3. Send WhatsApp message to bot: `START HABIT-XXXXX`
4. Verify bot access is granted

---

## Step 6: Production Deployment

### 6.1 Pre-Deployment Checklist
- [ ] Webhook secret configured
- [ ] PAYMENT_TEST_MODE set to false
- [ ] SSL certificate valid for webhook URL
- [ ] Database backed up
- [ ] Email service configured and tested

### 6.2 Deploy Changes
```bash
# Pull latest code
git pull origin main

# Install dependencies
npm install

# Restart application
pm2 restart lockin --update-env

# Save PM2 configuration
pm2 save
```

### 6.3 Monitor Initial Transactions
```bash
# Watch logs for first 24 hours
pm2 logs lockin --lines 100

# Check for errors
pm2 logs lockin --err --lines 50
```

---

## Troubleshooting

### Common Issues and Solutions

#### Webhook Not Receiving Data
```bash
# Test connectivity
curl -X POST https://*************:3001/webhook/thrivecart \
  -H "Content-Type: application/json" \
  -d '{"event":"test"}'

# Check firewall
sudo ufw status
sudo ufw allow 3001/tcp
```

#### Invalid Signature Errors
```bash
# Verify webhook secret matches
echo $THRIVECART_WEBHOOK_SECRET

# Test without signature verification (development only)
THRIVECART_WEBHOOK_SECRET="" pm2 restart lockin
```

#### Access Codes Not Working
```sql
-- Check if code exists
SELECT * FROM access_codes WHERE code = 'HABIT-XXXXX';

-- Check if user exists
SELECT * FROM paid_users WHERE email = '<EMAIL>';
```

#### Email Not Sending
```bash
# Check email queue
PGPASSWORD=postgres psql -U postgres -h localhost -d lockin -c \
  "SELECT * FROM email_queue WHERE status = 'pending';"

# Test email configuration
node -e "require('./src/services/emailService').processQueue()"
```

---

## Webhook Event Reference

### Supported Events

| Event | Description | Action Taken |
|-------|-------------|--------------|
| `order.success` | New purchase completed | Create user, generate access code, send email |
| `order.rebill_success` | Subscription renewed | Update user status to active |
| `order.refund` | Refund processed | Deactivate user and access code |
| `order.subscription_cancelled` | Subscription cancelled | Mark user as cancelled |
| `order.subscription_paused` | Subscription paused | Mark user as paused |
| `order.subscription_resumed` | Subscription resumed | Reactivate user |
| `order.rebill_failed` | Payment failed | Mark user as expired |

---

## Support & Monitoring

### Log Locations
- Application logs: `/root/.pm2/logs/lockin-out.log`
- Error logs: `/root/.pm2/logs/lockin-error.log`
- Webhook events: Database table `webhook_events`

### Health Check
```bash
curl http://localhost:3001/health
```

### Database Monitoring
```sql
-- Daily stats
SELECT 
  COUNT(*) as total_users,
  COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
  COUNT(CASE WHEN subscription_type = 'yearly' THEN 1 END) as yearly_users,
  COUNT(CASE WHEN subscription_type = 'monthly' THEN 1 END) as monthly_users
FROM paid_users;
```

---

## Contact & Resources

- **Support Email**: <EMAIL>
- **ThriveCart Documentation**: https://thrivecart.com/docs
- **Application Repository**: [Your GitHub Repo]
- **Server Access**: SSH to *************

---

Last Updated: August 2025