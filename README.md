# WhatsApp Habit Tracker Bot

Production-ready WhatsApp habit tracking bot built with Node.js, Express, PostgreSQL, and Twilio.

## Features

- ✅ **User Management**: LOCKED → ONBOARDING → ACTIVE state progression
- ✅ **Habit Tracking**: Track up to 5 daily habits with progress visualization
- ✅ **Session Management**: 30-minute timeout with automatic cleanup
- ✅ **Security**: Input sanitization, rate limiting, and SQL injection prevention
- ✅ **Compliance**: WhatsApp Business API compliant with STOP keyword handling
- ✅ **GDPR Compliant**: Privacy-focused audit logging without PII
- ✅ **Production Ready**: Docker support, health checks, graceful shutdown

## Prerequisites

- Node.js 18+ or Docker
- PostgreSQL 15+
- Twilio account with WhatsApp Business API access
- DigitalOcean account (for deployment)

## Quick Start

### 1. Clone and Install

```bash
git clone <repository-url>
cd whatsapp-habit-tracker
npm install
```

### 2. Configure Environment

```bash
cp .env.example .env
# Edit .env with your configuration
```

Required environment variables:
- `DATABASE_URL`: PostgreSQL connection string
- `TWILIO_ACCOUNT_SID`: Your Twilio Account SID
- `TWILIO_AUTH_TOKEN`: Your Twilio Auth Token
- `TWILIO_PHONE_NUMBER`: Your Twilio WhatsApp number

### 3. Setup Database

```bash
# Run migrations
npm run migrate

# To rollback (if needed)
npm run migrate:rollback
```

### 4. Start Application

```bash
# Development
npm run dev

# Production
npm start
```

## Docker Deployment

### Using Docker Compose

```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f app

# Stop services
docker-compose down
```

### Production Docker Build

```bash
# Build production image
docker build -t whatsapp-habit-tracker .

# Run container
docker run -d \
  --name habit-tracker \
  -p 3000:3000 \
  --env-file .env \
  whatsapp-habit-tracker
```

## DigitalOcean Deployment

### 1. Create Droplet

- Ubuntu 22.04 LTS
- 2GB RAM minimum
- Enable monitoring and backups

### 2. Initial Server Setup

```bash
# SSH into your droplet
ssh root@your-droplet-ip

# Update system
apt update && apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Install Docker Compose
apt install docker-compose -y

# Create app directory
mkdir -p /var/www/habit-tracker
cd /var/www/habit-tracker
```

### 3. Deploy Application

```bash
# Copy your code to the server
# Option 1: Use git
git clone <your-repo-url> .

# Option 2: Use scp from local machine
scp -r ./* root@your-droplet-ip:/var/www/habit-tracker/

# Create .env file
nano .env
# Add your production environment variables

# Start with Docker Compose
docker-compose up -d

# Check status
docker-compose ps
docker-compose logs -f app
```

### 4. Configure Nginx (Optional)

```bash
# Install Nginx
apt install nginx -y

# Configure reverse proxy
nano /etc/nginx/sites-available/habit-tracker

# Add configuration:
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}

# Enable site
ln -s /etc/nginx/sites-available/habit-tracker /etc/nginx/sites-enabled/
nginx -t
systemctl restart nginx
```

### 5. SSL with Let's Encrypt (Recommended)

```bash
# Install Certbot
apt install certbot python3-certbot-nginx -y

# Get SSL certificate
certbot --nginx -d your-domain.com

# Auto-renewal is configured automatically
```

## Twilio Webhook Configuration

1. Log into Twilio Console
2. Navigate to WhatsApp Sandbox or Business API
3. Set webhook URL to: `https://your-domain.com/webhook/whatsapp`
4. Method: POST
5. Save configuration

## API Endpoints

- `POST /webhook/whatsapp` - Twilio webhook endpoint
- `GET /health` - Health check endpoint

## Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm test -- --coverage

# Watch mode
npm run test:watch
```

## Project Structure

```
├── src/
│   ├── config/         # Configuration files
│   ├── controllers/    # Request handlers
│   ├── db/            # Database connection and migrations
│   ├── middleware/    # Express middleware
│   ├── models/        # Data models
│   ├── services/      # Business logic
│   └── server.js      # Main application
├── tests/             # Test files
├── docker-compose.yml # Docker Compose configuration
├── Dockerfile        # Docker image definition
└── package.json      # Node.js dependencies
```

## User Flow

1. **Initial Contact**: User sends any message
2. **Lock State**: Prompted to enter access code
3. **Onboarding**: Set name, timezone, and habits
4. **Active State**: Daily habit tracking and progress viewing

## Commands Users Can Send

- `STOP` - Opt out of messages
- `START` - Resubscribe after opt-out
- `menu` - Return to main menu
- `1-5` - Select menu options or habits

## Security Features

- E.164 phone number validation
- SQL injection prevention
- XSS protection
- Rate limiting (100 requests/15 minutes)
- Twilio signature verification
- Non-root Docker user
- No PII in logs

## Monitoring

- Health endpoint: `GET /health`
- Structured logging with Winston
- Docker health checks
- Session timeout monitoring
- Rate limit tracking

## Maintenance

### Database Backup

```bash
# Backup
docker-compose exec postgres pg_dump -U habituser habittracker > backup.sql

# Restore
docker-compose exec -T postgres psql -U habituser habittracker < backup.sql
```

### View Logs

```bash
# Application logs
docker-compose logs -f app

# Database logs
docker-compose logs -f postgres
```

### Update Application

```bash
# Pull latest changes
git pull

# Rebuild and restart
docker-compose down
docker-compose up -d --build
```

## Troubleshooting

### Database Connection Issues
- Check `DATABASE_URL` format
- Ensure PostgreSQL is running
- Verify network connectivity

### Twilio Webhook Not Working
- Verify webhook URL is publicly accessible
- Check Twilio signature validation
- Review Twilio console for errors

### High Memory Usage
- Check for memory leaks with `docker stats`
- Review session cleanup logs
- Consider increasing droplet size

## License

MIT

## Support

For issues or questions, please create an issue in the repository.