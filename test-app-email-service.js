#!/usr/bin/env node

require('dotenv').config();
const pool = require('./src/db/connection');
const emailService = require('./src/services/emailService');

async function testAppEmailService() {
  console.log('🔍 TESTING APPLICATION EMAIL SERVICE');
  console.log('=' .repeat(40));
  
  const testEmail = '<EMAIL>';
  const testAccessCode = 'HABIT-TEST123';
  const botPhone = '+19035155547';
  
  try {
    console.log('\n1️⃣ Queuing email through application service...');
    
    // Insert email into queue (same way the app does it)
    await pool.query(`
      INSERT INTO email_queue (to_email, subject, template, template_data, priority)
      VALUES ($1, $2, $3, $4, $5)
    `, [
      testEmail,
      'Your Lockin Habit Tracker Access Code',
      'welcome_monthly',
      JSON.stringify({
        accessCode: testAccessCode,
        botPhone: botPhone
      }),
      1
    ]);
    
    console.log(`   ✅ Email queued for: ${testEmail}`);
    console.log(`   📝 Template: welcome_monthly`);
    console.log(`   🔑 Access Code: ${testAccessCode}`);
    
    console.log('\n2️⃣ Processing email queue...');
    
    // Process the queue (same way the app does it)
    await emailService.processQueue();
    
    console.log('   ✅ Email queue processed');
    
    console.log('\n3️⃣ Checking email status...');
    
    const emailStatus = await pool.query(`
      SELECT id, to_email, status, sent_at, retry_count, error_message, created_at
      FROM email_queue 
      WHERE to_email = $1 
      ORDER BY created_at DESC 
      LIMIT 1
    `, [testEmail]);
    
    if (emailStatus.rows.length > 0) {
      const email = emailStatus.rows[0];
      console.log(`   📧 Email ID: ${email.id}`);
      console.log(`   📬 Status: ${email.status}`);
      console.log(`   🕐 Created: ${email.created_at}`);
      console.log(`   ✉️ Sent at: ${email.sent_at || 'Not sent'}`);
      console.log(`   🔄 Retries: ${email.retry_count}`);
      
      if (email.error_message) {
        console.log(`   ❌ Error: ${email.error_message}`);
      }
      
      if (email.status === 'sent') {
        console.log('\n4️⃣ Delivery Check:');
        console.log(`   🌐 Check inbox: https://www.mailinator.com/v4/public/inboxes.jsp?to=app-test`);
        console.log('   📮 Subject: "Your Lockin Habit Tracker Access Code"');
        console.log(`   🔍 Look for access code: ${testAccessCode}`);
      }
      
      // Clean up test data
      await pool.query('DELETE FROM email_queue WHERE id = $1', [email.id]);
      console.log('\n🧹 Test data cleaned');
      
    } else {
      console.log('   ❌ Email not found in queue');
    }
    
  } catch (error) {
    console.log(`   ❌ Test failed: ${error.message}`);
    console.log('   Full error:', error);
  } finally {
    await pool.end();
  }
}

testAppEmailService().catch(console.error);