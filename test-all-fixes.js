#!/usr/bin/env node

require('dotenv').config();
const stateMachine = require('./src/services/stateMachine');
const User = require('./src/models/User');
const pool = require('./src/db/connection');

async function testWhatsApp(phone, message, description) {
  console.log(`\n📱 ${description}`);
  console.log(`USER SENDS: "${message}"`);
  console.log('─'.repeat(60));
  
  const user = await User.findByPhone(phone);
  const response = await stateMachine.processMessage(user, message);
  
  console.log('🤖 BOT RESPONDS:');
  console.log(response.message);
  console.log('─'.repeat(60));
  
  return response;
}

async function runAllTests() {
  try {
    const testPhone = '+27646921984';
    
    // Reset user completely
    console.log('🔄 RESETTING USER TO CLEAN SLATE...');
    const client = await pool.connect();
    await client.query(
      'DELETE FROM habit_logs WHERE user_id = (SELECT id FROM users WHERE phone = $1) AND log_date = CURRENT_DATE',
      [testPhone]
    );
    await client.query(
      'UPDATE users SET current_state = $1, session_context = $2 WHERE phone = $3',
      ['MAIN_MENU', '{}', testPhone]
    );
    client.release();
    
    console.log('='.repeat(80));
    console.log('TESTING ALL THREE FIXES FOR RIZZ');
    console.log('='.repeat(80));
    
    // TEST 1: Main menu with ⚠️ icons
    await testWhatsApp(testPhone, 'menu', 'TEST 1: Main menu should show ⚠️ icons');
    
    // TEST 2: Logging submenu with ⚠️ icons (not ⬜)
    await testWhatsApp(testPhone, '1', 'TEST 2: Logging submenu should show ⚠️ icons (not ⬜)');
    
    // TEST 3: New "1y 2n 3y" format
    await testWhatsApp(testPhone, '1y 2n 3y 4n 5y', 'TEST 3A: Using new "1y 2n 3y 4n 5y" format');
    
    // Back to main menu to see status
    await testWhatsApp(testPhone, 'menu', 'TEST 3B: Main menu after using new format');
    
    // TEST 4: Old format still works
    console.log('\n🔄 Resetting for old format test...');
    const client2 = await pool.connect();
    await client2.query(
      'DELETE FROM habit_logs WHERE user_id = (SELECT id FROM users WHERE phone = $1) AND log_date = CURRENT_DATE',
      [testPhone]
    );
    client2.release();
    
    await testWhatsApp(testPhone, '1', 'TEST 4A: Go to logging submenu');
    await testWhatsApp(testPhone, '2,4', 'TEST 4B: Using old "2,4" format');
    await testWhatsApp(testPhone, 'menu', 'TEST 4C: Main menu after using old format');
    
    console.log('\n✅ ALL TESTS COMPLETED!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await pool.end();
  }
}

runAllTests().catch(console.error);