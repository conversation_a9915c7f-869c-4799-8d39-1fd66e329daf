# LOCK IN HABIT TRACKER - <PERSON><PERSON>UDE CODE TASKS

## PROJECT CONTEXT
- **Server**: root@*************
- **Backend**: Node.js on port 3001, PM2 process "howling-backend"
- **Database**: PostgreSQL (postgresql://postgres:postgres@localhost:5432/lockin)
- **Status**: Live production system with paying customers

## CRITICAL TASK - IMMEDIATE

### EMAIL TEMPLATE FOOTER BUG
**Priority**: P0 - Blocking customer experience

**Problem**: 
- welcome_multi_code template has footer (bump orders work)
- welcome_dynamic template missing footer (single purchases broken)

**Impact**:
- Monthly ($5.99) subscriptions = no footer
- Weekly ($2.99) subscriptions = no footer  
- Lifetime ($99.99) subscriptions = no footer
- Basic Annual ($39.99) subscriptions = no footer

**Required Fix**:
```
Update welcome_dynamic template in /var/www/lockin/src/services/emailService.js
Add identical footer content from welcome_multi_code template:
- Personal outro from Rich
- Social links (x: @richvieren, substack: @vieren)
- Affiliate program section (25% commission)
- Website and social handles
```

**Verification**: Test Monthly purchase after fix to confirm footer appears

---

## IMMEDIATE TESTING TASKS

### COMPREHENSIVE EMAIL VERIFICATION
After footer fix, verify all subscription types:

1. **Monthly ($5.99)** - welcome_dynamic template
2. **Weekly ($2.99)** - welcome_dynamic template
3. **Lifetime ($99.99)** - welcome_dynamic template
4. **Annual ($39.99)** - welcome_dynamic template
5. **Annual + Friend ($59.99)** - welcome_multi_code template
6. **Annual + Tribe ($99.99)** - welcome_multi_code template

**Success Criteria**: All emails show consistent footer with Rich's outro and branding

---

## MEDIUM PRIORITY TASKS

### SYSTEM STABILITY
- Enhance error handling in webhook processing
- Add logging for email template selection logic
- Implement webhook reliability monitoring
- Database performance optimization review

### ANALYTICS FOUNDATION
- Set up data collection for completion rates
- Implement streak trend analysis
- Create user engagement tracking
- Design email analytics reporting system

---

## FUTURE CLAUDE CODE TASKS

### CLAUDE FLOW INTEGRATION
- Install and configure Claude Flow for automation
- Implement customer success campaign workflows
- Set up automated analytics email generation
- Create personalized progress report system

### SOCIAL FEATURES BACKEND
- Design database schema for group functionality
- Implement challenge competition system
- Create accountability partner matching logic
- Build leaderboard and progress comparison APIs

### WEB DASHBOARD API
- Create REST API endpoints for habit data
- Implement authentication system for web access
- Build analytics data aggregation services
- Design real-time progress tracking system

---

## TECHNICAL SPECIFICATIONS

### Current System Architecture
```
ThriveCart → Webhook → Database → Email Service → Customer
WhatsApp Bot ← Access Codes ← Database ← Webhook Handler
```

### Email Template Logic
```javascript
// Line 252 in thrivecartController.js
const template = codes.length > 1 ? 'welcome_multi_code' : 'welcome_dynamic';
```

### Database Tables
- users (habit tracking data)
- paid_users (subscription info)
- access_codes (authentication)
- email_queue (delivery tracking)

---

## DEPLOYMENT NOTES

### Always Required
1. Backup before major changes
2. Test in isolated environment when possible
3. Restart PM2 process after template changes
4. Verify webhook endpoints remain functional
5. Monitor system logs for errors

### Rollback Strategy
- Keep backups in /root/backups/ with timestamps
- Document all file changes made
- Have restoration procedure ready
- Test rollback process before major deployments

---

## DEBUGGING FRAMEWORK

### DEBUGGING AGENT INTEGRATION
For complex debugging scenarios, consider using:
- **Claude Code debugging mode** - Enhanced logging and step-by-step analysis
- **Agents.md debugging workflows** - Systematic issue isolation
- **Multi-agent debugging** - Separate agents for different system components

### SYSTEMATIC DEBUG PROCESS

#### 1. ISSUE IDENTIFICATION
```bash
# Always gather these first:
pm2 logs howling-backend --lines 50
tail -f /var/log/postgresql/postgresql-*.log
curl -X POST https://yourdomain.com/webhook/thrivecart -d "test"
```

#### 2. COMPONENT ISOLATION
```bash
# Test webhook endpoint separately
node -e "require('./src/controllers/thrivecartController.js').testWebhook()"

# Test email service separately  
node -e "require('./src/services/emailService.js').testTemplate('welcome_dynamic')"

# Test database connection
psql -d lockin -c "SELECT COUNT(*) FROM users;"
```

#### 3. TEMPLATE DEBUGGING
```bash
# Check which template is being used
grep -n "welcome_" /var/www/lockin/src/services/emailService.js

# Verify template content
node -e "console.log(require('./src/services/emailService.js').getWelcomeDynamicTemplate())"

# Test email generation without sending
node -e "const email = require('./src/services/emailService.js'); console.log(email.generateTestEmail('welcome_dynamic'))"
```

#### 4. WEBHOOK DEBUGGING
```bash
# Add debug logging to webhook handler
console.log('Webhook received:', JSON.stringify(req.body, null, 2));
console.log('Template selected:', template);
console.log('Email data:', emailData);

# Monitor webhook calls in real-time
tail -f /var/www/lockin/logs/webhook.log
```

### DEBUGGING CHECKLIST

#### Email Issues
- [ ] Check template file exists and has correct content
- [ ] Verify PM2 process restarted after template changes
- [ ] Confirm Brevo SMTP credentials working
- [ ] Test email generation without actual sending
- [ ] Check email queue table for failed deliveries

#### Webhook Issues  
- [ ] Verify ThriveCart webhook URL is correct
- [ ] Check webhook secret matches ThriveCart settings
- [ ] Confirm webhook endpoint returns 200 status
- [ ] Monitor webhook payload structure changes
- [ ] Test with sample payload data

#### Database Issues
- [ ] Verify PostgreSQL connection string
- [ ] Check database table schemas match expectations
- [ ] Monitor for SQL query errors in logs
- [ ] Test database queries independently
- [ ] Verify user permissions on database

#### System Issues
- [ ] Check PM2 process status and memory usage
- [ ] Monitor system resources (CPU, memory, disk)
- [ ] Verify all environment variables set correctly
- [ ] Check file permissions on application files
- [ ] Test external API connections (Twilio, Brevo)

### COMMON DEBUG SCENARIOS

#### "Email template not found"
```bash
# Check file structure
find /var/www/lockin -name "*.js" | grep -E "(email|template)"

# Verify template mapping
grep -n "getWelcome" /var/www/lockin/src/services/emailService.js
```

#### "Webhook not receiving data"
```bash
# Test webhook endpoint
curl -X POST localhost:3001/webhook/thrivecart \
  -H "Content-Type: application/json" \
  -d '{"test": "data"}'

# Check webhook logs
grep -i "webhook" /var/www/lockin/logs/*.log
```

#### "Database connection failed"
```bash
# Test database connectivity
pg_isready -h localhost -p 5432 -d lockin

# Check connection pool
node -e "require('./src/config/database.js').testConnection()"
```

### LOGGING STANDARDS

#### Required Logging for All Changes
```javascript
// Before any major operation
console.log(`[${new Date().toISOString()}] STARTING: ${operationName}`);
console.log('Input data:', JSON.stringify(inputData, null, 2));

// After operation completion  
console.log(`[${new Date().toISOString()}] COMPLETED: ${operationName}`);
console.log('Result:', JSON.stringify(result, null, 2));

// On errors
console.error(`[${new Date().toISOString()}] ERROR in ${operationName}:`, error);
console.error('Stack trace:', error.stack);
```

#### Log File Organization
```bash
# Separate logs by component
/var/www/lockin/logs/webhook.log
/var/www/lockin/logs/email.log  
/var/www/lockin/logs/database.log
/var/www/lockin/logs/whatsapp.log
```

## AGENTS.MD COORDINATION PROTOCOL

### TASK EXECUTION WORKFLOW

#### MANDATORY PAUSE POINTS
**After EVERY task completion, Claude Code must:**

1. **Stop and request manual verification**
2. **Provide proof of changes made**
3. **Wait for user confirmation before proceeding**

#### VERIFICATION REQUIREMENTS

**For Every Completed Task:**
```bash
# Claude Code MUST provide:
git log --oneline -3                    # Show recent commits
git diff HEAD~1 HEAD                    # Show actual file changes
ls -la [modified-files]                 # Prove files were touched
pm2 list                               # Show process restart occurred
curl -I localhost:3001/health          # Verify system still running
```

**User Manual Verification:**
```bash
# User will manually check:
git status                             # Verify clean state
pm2 logs howling-backend --lines 10   # Check for errors
# Manual functional testing (purchases, emails, etc.)
```

#### TASK APPROVAL PROCESS

**Phase 1: Task Execution**
- Claude Code executes ONE task only
- Creates git commit with detailed message
- Provides verification evidence
- **STOPS AND WAITS**

**Phase 2: Manual Verification** 
- User reviews file changes manually
- User tests functionality independently  
- User approves or rejects the task completion

**Phase 3: Proceed or Fix**
- If approved: Move to next task
- If rejected: Claude Code must debug and fix
- No proceeding without explicit approval

### ANTI-BULLSHIT MEASURES

#### CLAUDE CODE ACCOUNTABILITY
**Required Evidence for Every Task:**
- Actual file content showing changes
- Git commit hash proving changes were made
- PM2 process ID change showing restart
- Functional test results (not just claims)

**Forbidden Responses:**
- "The changes are working correctly" (without proof)
- "The system has been updated" (without verification)
- "Testing shows success" (without showing actual test commands)
- Any claim of completion without evidence

#### PROOF STANDARDS
```bash
# For file changes:
cat /path/to/modified/file | grep -A 5 -B 5 "modified section"

# For system changes:
systemctl status [service] | head -5
pm2 show howling-backend | grep -E "(pid|uptime|restarts)"

# For functional changes:
curl -X POST localhost:3001/webhook/thrivecart -d '{"test":"data"}' -v
```

#### VERIFICATION COMMANDS

**Template Changes:**
```bash
# Must show actual template content
node -e "console.log(require('./src/services/emailService.js').getWelcomeDynamicTemplate().substr(0,500))"

# Must prove PM2 restart
pm2 logs howling-backend | tail -1

# Must show git evidence
git show --name-only HEAD
```

**Database Changes:**
```bash
# Must show actual query results
psql -d lockin -c "SELECT column_name FROM table_name LIMIT 3;"

# Must show connection test
node -e "require('./src/config/database.js').testConnection().then(console.log).catch(console.error)"
```

**Email System Changes:**
```bash
# Must demonstrate email generation
node -e "const e = require('./src/services/emailService.js'); console.log(e.testEmailGeneration('welcome_dynamic'))"

# Must show SMTP connection test
node -e "require('./src/services/emailService.js').testSMTPConnection().then(console.log)"
```

### DEBUGGING ESCALATION

#### WHEN CLAUDE CODE FAILS
If Claude Code provides false success claims or broken implementations:

1. **Stop all work immediately**
2. **Demand specific debugging evidence**
3. **Require rollback to last working state**
4. **Implement systematic debugging protocol**

#### DEBUG EVIDENCE REQUIRED
```bash
# System state
ps aux | grep node
netstat -tlnp | grep 3001
df -h
free -m

# Application state  
pm2 monit
tail -f /var/log/postgresql/*.log
ls -la /var/www/lockin/logs/

# Git state
git status
git log --graph --oneline -10
git stash list
```

#### ROLLBACK PROTOCOL
```bash
# Immediate rollback if issues found
git reset --hard HEAD~1
pm2 restart howling-backend
# Verify system restoration
curl -I localhost:3001/health
```

### TASK BREAKDOWN RULES

#### ONE TASK AT A TIME
- Each task must be atomic and independently testable
- No combining multiple changes in single commits
- Each task gets separate verification cycle
- No proceeding without approval

#### TASK SIZE LIMITS
- Maximum 30 minutes of work per task
- Single file modifications when possible
- Clear before/after states
- Specific success criteria defined

### COMMUNICATION PROTOCOL

#### REQUIRED STATUS UPDATES
```
TASK: [specific task name]
STATUS: [starting/in-progress/completed/failed]
FILES_MODIFIED: [exact file paths]
COMMITS: [git commit hashes]
VERIFICATION_COMMANDS: [commands to verify success]
NEXT_ACTION: [wait for approval/debugging/rollback]
```

#### MANDATORY PAUSES
- After every git commit
- Before any PM2 restart
- After any database changes
- Before claiming task completion

This protocol ensures you maintain control and can verify every change before allowing progression to the next task.
