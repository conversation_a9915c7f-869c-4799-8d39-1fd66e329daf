#!/usr/bin/env node

require('dotenv').config();
const pool = require('./src/db/connection');
const User = require('./src/models/User');

async function testUserIsolation() {
  console.log('Testing User Isolation\n');
  console.log('='.repeat(50));

  try {
    // Test with different phone numbers
    const testPhones = [
      'whatsapp:+12025551234',
      'whatsapp:+13105556789',
      'whatsapp:+14155559876'
    ];

    for (const phone of testPhones) {
      console.log(`\nTesting with phone: ${phone}`);
      
      // Use the actual findOrCreate method
      const user = await User.findOrCreate(phone);
      console.log(`  User ID: ${user.id}`);
      console.log(`  Phone: ${user.phone}`);
      console.log(`  Display Name: ${user.display_name || 'Not set'}`);
      console.log(`  Status: ${user.status}`);
      console.log(`  State: ${user.current_state}`);
      
      // Check if user has any habits
      const habitsResult = await pool.query(
        'SELECT COUNT(*) as count FROM habits WHERE user_id = $1',
        [user.id]
      );
      console.log(`  Habits count: ${habitsResult.rows[0].count}`);
    }

    // Now check all users in database
    console.log('\n' + '='.repeat(50));
    console.log('All users in database:');
    const allUsers = await pool.query(
      'SELECT id, phone, display_name, status FROM users ORDER BY id'
    );
    
    allUsers.rows.forEach(user => {
      console.log(`  ID: ${user.id}, Phone: ${user.phone}, Name: ${user.display_name || 'Not set'}, Status: ${user.status}`);
    });

  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await pool.end();
  }
}

testUserIsolation();