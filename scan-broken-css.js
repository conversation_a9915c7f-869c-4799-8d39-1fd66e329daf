#!/usr/bin/env node

require('dotenv').config();
const emailService = require('./src/services/emailService');

const templateData = {
  'botPhone': '+19035155547',
  'accessCode': 'SCAN-CSS',
  'subscriptionType': 'yearly',
  'subscription_type': 'Annual', 
  'subscription_price': '$39.99/year',
  'primary_access_code': 'SCAN-CSS',
  'isAffiliate': true,
  'affiliateCode': 'SCAN-AFF'
};

const { html } = emailService.getWelcomeDynamicTemplate(templateData);

console.log('🔍 SCANNING FOR BROKEN CSS IN GENERATED HTML...');
console.log('='.repeat(60));

// Find all style attributes
const styleMatches = html.match(/style="[^"]*"/g);
let brokenStyles = [];

if (styleMatches) {
  styleMatches.forEach((style, index) => {
    const content = style.match(/style="([^"]*)"/)[1];
    
    // Check for common issues
    const issues = [];
    
    // Check for truncated properties (missing values)
    if (content.includes('align-items: cent') && !content.includes('align-items: center')) {
      issues.push('TRUNCATED: align-items incomplete');
    }
    
    // Check for properties without semicolons at the end
    const props = content.split(';');
    props.forEach((prop, i) => {
      prop = prop.trim();
      if (prop && i < props.length - 1) { // Not the last property
        if (!prop.includes(':')) {
          issues.push(`MALFORMED PROPERTY: "${prop}"`);
        }
      }
    });
    
    // Check for unclosed quotes or values
    if (content.includes("'") && (content.match(/'/g) || []).length % 2 !== 0) {
      issues.push('UNMATCHED QUOTES');
    }
    
    if (issues.length > 0) {
      brokenStyles.push({
        index: index + 1,
        style: style,
        issues: issues
      });
    }
  });
}

if (brokenStyles.length > 0) {
  console.log('❌ FOUND BROKEN STYLES:');
  brokenStyles.forEach(broken => {
    console.log(`\n${broken.index}. ${broken.style}`);
    broken.issues.forEach(issue => console.log(`   🚨 ${issue}`));
  });
} else {
  console.log('✅ No obviously broken styles found');
}

// Specifically search for the "align-items: cent" issue you mentioned
const centMatch = html.match(/align-items:\s*cent[^e]/g);
if (centMatch) {
  console.log('\n❌ FOUND "align-items: cent" TRUNCATION:');
  centMatch.forEach((match, i) => console.log(`${i+1}: ${match}`));
  
  // Find the context
  const index = html.indexOf(centMatch[0]);
  const context = html.substring(index - 100, index + 200);
  console.log('\nCONTEXT:');
  console.log(context);
} else {
  console.log('\n✅ No "align-items: cent" truncation found');
}

// Save for manual inspection
require('fs').writeFileSync('/var/www/lockin/css-scan-email.html', html);
console.log('\n💾 Generated email saved to: css-scan-email.html');
console.log(`📊 Total style attributes: ${styleMatches ? styleMatches.length : 0}`);