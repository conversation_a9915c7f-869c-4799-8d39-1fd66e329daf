#!/usr/bin/env node

const axios = require('axios');
const querystring = require('querystring');

async function testWebhook() {
  try {
    // Try flat structure that matches what the code expects
    const webhookData = {
      event: 'order.success',
      thrivecart_secret: 'FUQ2A97V0Q8A',
      // Try both nested and flat formats
      customer_email: '<EMAIL>',
      email: '<EMAIL>',
      customer_id: 'CUST-FLAT-001',
      customer_name: 'Flat Test Customer',
      order_id: 'ORD-FLAT-001',
      order_total: '5.00',
      total: '500', // Also try cents format
      currency: 'USD',
      product_name: 'Habit Tracker Monthly'
    };

    // Convert to URL-encoded format
    const formData = querystring.stringify(webhookData);

    console.log('🚀 Testing ThriveCart webhook with flat structure...\n');
    console.log('📤 Sending webhook data...');
    console.log('Data being sent:', webhookData);
    
    const response = await axios.post(
      'http://localhost:3001/webhook/thrivecart',
      formData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    console.log('✅ Webhook processed successfully!');
    console.log('Response:', response.data);
    
  } catch (error) {
    console.error('❌ Webhook failed:', error.response?.data || error.message);
  }
}

testWebhook();