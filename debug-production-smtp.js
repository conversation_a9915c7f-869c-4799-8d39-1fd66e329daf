#!/usr/bin/env node

require('dotenv').config();
const nodemailer = require('nodemailer');
const pool = require('./src/db/connection');

async function debugProductionSMTP() {
  try {
    console.log('🔍 DEBUGGING PRODUCTION SMTP EXACTLY AS QUEUE DOES...');
    
    // Get the most recent customer purchase
    const result = await pool.query(`
      SELECT * FROM email_queue 
      WHERE template = 'welcome_dynamic' 
      AND to_email NOT LIKE 'test%@%'
      ORDER BY id DESC LIMIT 1
    `);
    
    if (!result.rows.length) {
      console.log('No customer emails found');
      return;
    }
    
    const emailRecord = result.rows[0];
    console.log('Using customer email ID:', emailRecord.id);
    console.log('Email:', emailRecord.to_email);
    console.log('Created:', emailRecord.created_at);
    
    // EXACT production SMTP setup
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_PORT === '465',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });
    
    // EXACT production template generation
    const emailService = require('./src/services/emailService');
    const templateFunc = emailService.templates[emailRecord.template];
    
    const templateData = typeof emailRecord.template_data === 'string' 
      ? JSON.parse(emailRecord.template_data) 
      : emailRecord.template_data;
    
    console.log('\n📊 TEMPLATE DATA:');
    console.log(JSON.stringify(templateData, null, 2));
    
    const { subject, html, text } = templateFunc(templateData);
    
    console.log('\n📈 GENERATED CONTENT:');
    console.log('HTML length:', html.length);
    console.log('Text length:', text.length);
    
    // Check footer content
    const step5Index = html.indexOf('Start building unstoppable habits!');
    if (step5Index !== -1) {
      const afterStep5 = html.substring(step5Index + 'Start building unstoppable habits!'.length);
      console.log('Characters after step 5:', afterStep5.length);
      
      const hasSignature = afterStep5.includes("Let's Lock In") && afterStep5.includes('Rich');
      const hasAffiliate = afterStep5.includes('💰 Earn with Lock In');
      const hasSocial = afterStep5.includes('@richvieren');
      const hasCopyright = afterStep5.includes('© 2025 Lock In');
      
      console.log('\n🔍 FOOTER CHECK:');
      console.log('  ✅ Rich signature:', hasSignature);
      console.log('  ✅ Affiliate section:', hasAffiliate);
      console.log('  ✅ Social links:', hasSocial);
      console.log('  ✅ Copyright:', hasCopyright);
    }
    
    // EXACT production mail options
    const mailOptions = {
      from: `"Lockin Habit Tracker" <${process.env.EMAIL_FROM || '<EMAIL>'}>`,
      to: '<EMAIL>', // Safe test email
      subject: emailRecord.subject || subject,
      html,
      text,
      replyTo: process.env.EMAIL_REPLY_TO || '<EMAIL>'
    };
    
    console.log('\n📧 SENDING EXACT PRODUCTION EMAIL...');
    console.log('From:', mailOptions.from);
    console.log('Subject:', mailOptions.subject);
    
    const info = await transporter.sendMail(mailOptions);
    console.log('✅ Email sent:', info.messageId);
    
    // Save the exact content sent
    require('fs').writeFileSync('/var/www/lockin/production-smtp-debug.html', html);
    console.log('💾 Saved to: production-smtp-debug.html');
    
    console.log('\n🎯 CHECK YOUR EMAIL:');
    console.log('If this email is complete but real customer emails are truncated,');
    console.log('then the issue is in the SMTP provider or email client rendering.');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
  
  await pool.end();
}

debugProductionSMTP().catch(console.error);