#!/usr/bin/env node
require('dotenv').config();

/**
 * Simple Payment Test - Tests core functionality
 */
const paymentService = require('./src/services/paymentService');

async function testSimple() {
  console.log('🚀 Testing Payment Service\n');
  
  const testPhone = '+15551234567';
  
  // Test 1: Check unpaid user access
  console.log('1️⃣ Testing unpaid user...');
  const accessCheck1 = await paymentService.checkUserAccess(testPhone);
  console.log('Has access:', accessCheck1.hasAccess);
  console.log('Test mode:', accessCheck1.testMode);
  
  if (!accessCheck1.hasAccess) {
    console.log('✅ Unpaid user correctly denied access');
  } else {
    console.log('⚠️  Unpaid user has access (test mode?)');
  }
  
  // Test 2: Get paywall message
  console.log('\n2️⃣ Testing paywall message...');
  const paywallMessage = paymentService.getPaywallMessage();
  console.log('Paywall message preview:');
  console.log(paywallMessage.substring(0, 150) + '...');
  
  if (paywallMessage.includes('ACCESS REQUIRED')) {
    console.log('✅ Paywall message looks correct');
  }
  
  // Test 3: Try to activate non-existent access code
  console.log('\n3️⃣ Testing invalid access code...');
  const invalidResult = await paymentService.activateAccessCode(testPhone, 'HABIT-INVALID');
  console.log('Invalid code result:', invalidResult.success);
  console.log('Message:', invalidResult.message);
  
  if (!invalidResult.success) {
    console.log('✅ Invalid access code correctly rejected');
  }
  
  console.log('\n✨ Basic payment service test completed!');
}

testSimple().catch(console.error);