const axios = require('axios');

// Test webhook with proper ThriveCart payload structure
async function testWebhookFix() {
  const webhookUrl = 'http://localhost:3001/webhook/thrivecart';
  
  // Simulate actual ThriveCart payload structure
  const payload = {
    mode: 'test',
    event: 'order.success',
    thrivecart_secret: process.env.THRIVECART_WEBHOOK_SECRET || 'FUQ2A97V0Q8A',
    customer: {
      id: 'TEST-CUSTOMER-' + Date.now(),
      email: 'webhook-test-' + Date.now() + '@example.com',
      name: 'Test Customer',
      first_name: 'Test',
      last_name: 'Customer'
    },
    order: {
      id: 'TEST-ORDER-' + Date.now(),
      total: '2999', // In cents
      total_str: '29.99', // As string with decimal
      currency: 'USD'
    },
    currency: 'USD',
    base_product_name: 'Lock In - Whatsapp Habit Tracker - Yearly'
  };

  try {
    console.log('Sending test webhook to:', webhookUrl);
    console.log('Customer email:', payload.customer.email);
    
    const response = await axios.post(webhookUrl, payload, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Webhook processed successfully');
    console.log('Response:', response.data);
    
    // Check if email was queued
    const { Client } = require('pg');
    const client = new Client({
      host: 'localhost',
      database: 'lockin',
      user: 'postgres',
      password: 'postgres'
    });
    
    await client.connect();
    
    // Check if user was created
    const userResult = await client.query(
      "SELECT id, email, access_code, subscription_type FROM paid_users WHERE email = $1",
      [payload.customer.email]
    );
    
    if (userResult.rows.length > 0) {
      console.log('✅ User created:', userResult.rows[0]);
      
      // Check email queue
      const emailResult = await client.query(
        "SELECT id, to_email, subject, status, error_message FROM email_queue WHERE to_email = $1 ORDER BY created_at DESC LIMIT 1",
        [payload.customer.email]
      );
      
      if (emailResult.rows.length > 0) {
        console.log('📧 Email status:', emailResult.rows[0]);
      }
    } else {
      console.log('❌ User not created');
    }
    
    await client.end();
    
  } catch (error) {
    console.error('❌ Error:', error.response?.data || error.message);
  }
}

testWebhookFix();