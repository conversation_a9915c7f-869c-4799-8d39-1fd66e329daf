#!/usr/bin/env node

require('dotenv').config();
const axios = require('axios');
const pool = require('./src/db/connection');

class ThriveCartWebhookDebugger {
  constructor() {
    this.webhookUrl = process.env.BOT_WEBHOOK_URL?.replace('https://', 'http://') || 'http://localhost:3001';
    this.webhookSecret = process.env.THRIVECART_WEBHOOK_SECRET;
  }

  async runDiagnostics() {
    console.log('🔍 ThriveCart Webhook Diagnostics');
    console.log('=' .repeat(50));
    
    await this.checkConfiguration();
    await this.checkEndpointAvailability();
    await this.checkRecentActivity();
    await this.testWebhookProcessing();
    await this.identifyIssues();
    
    await pool.end();
  }

  async checkConfiguration() {
    console.log('\n1️⃣ Configuration Check');
    console.log('-' .repeat(25));
    
    console.log(`📍 Webhook URL: ${this.webhookUrl}/webhook/thrivecart`);
    console.log(`🔑 Webhook Secret: ${this.webhookSecret ? '✅ Set' : '❌ Missing'}`);
    console.log(`🧪 Test Mode: ${process.env.PAYMENT_TEST_MODE === 'true' ? '✅ Enabled' : '❌ Disabled'}`);
    
    // Check if URL uses HTTPS vs HTTP mismatch
    if (this.webhookUrl.includes('https://') && process.env.BOT_WEBHOOK_URL?.includes('https://')) {
      console.log('⚠️  WARNING: Webhook URL uses HTTPS but server might be HTTP only');
    }
  }

  async checkEndpointAvailability() {
    console.log('\n2️⃣ Endpoint Availability');
    console.log('-' .repeat(25));
    
    try {
      // Test HEAD request (ThriveCart sends this first)
      console.log('Testing HEAD request...');
      const headResponse = await axios.head(`${this.webhookUrl}/webhook/thrivecart`, {
        timeout: 5000
      });
      console.log(`✅ HEAD request: ${headResponse.status}`);
    } catch (error) {
      console.log(`❌ HEAD request failed: ${error.message}`);
    }

    try {
      // Test basic POST request
      console.log('Testing POST request...');
      const postResponse = await axios.post(`${this.webhookUrl}/webhook/thrivecart`, 
        new URLSearchParams({ test: 'ping' }), {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        timeout: 5000
      });
      console.log(`✅ POST request: ${postResponse.status}`);
    } catch (error) {
      console.log(`❌ POST request failed: ${error.message}`);
    }
  }

  async checkRecentActivity() {
    console.log('\n3️⃣ Recent Activity Analysis');
    console.log('-' .repeat(25));

    try {
      // Check recent webhook events
      const webhookEvents = await pool.query(`
        SELECT event_type, source, processed, error_message, created_at,
               payload->>'customer_email' as customer_email
        FROM webhook_events 
        WHERE created_at >= NOW() - INTERVAL '24 hours' 
        AND source = 'thrivecart'
        ORDER BY created_at DESC 
        LIMIT 5
      `);

      console.log(`📊 Recent webhook events (last 24h): ${webhookEvents.rows.length}`);
      for (const event of webhookEvents.rows) {
        const status = event.processed ? '✅ Processed' : 
                      event.error_message ? '❌ Failed' : '⏳ Pending';
        console.log(`   ${event.created_at.toISOString()}: ${event.customer_email} - ${status}`);
        if (event.error_message) {
          console.log(`     Error: ${event.error_message}`);
        }
      }

      // Check recent user creation
      const recentUsers = await pool.query(`
        SELECT pu.email, pu.subscription_type, pu.created_at,
               ac.code as access_code,
               eq.status as email_status, eq.sent_at
        FROM paid_users pu
        JOIN access_codes ac ON pu.access_code = ac.code
        LEFT JOIN email_queue eq ON pu.email = eq.to_email
        WHERE pu.created_at >= NOW() - INTERVAL '24 hours'
        ORDER BY pu.created_at DESC
        LIMIT 5
      `);

      console.log(`\n👥 Recent user creations (last 24h): ${recentUsers.rows.length}`);
      for (const user of recentUsers.rows) {
        const emailStatus = user.email_status === 'sent' ? 
          `✅ Sent (${user.sent_at?.toISOString()})` : 
          user.email_status === 'pending' ? '⏳ Pending' :
          user.email_status === 'failed' ? '❌ Failed' :
          '❌ No Email';
        
        console.log(`   ${user.created_at.toISOString()}: ${user.email}`);
        console.log(`     Plan: ${user.subscription_type} | Code: ${user.access_code}`);
        console.log(`     Email: ${emailStatus}`);
      }

    } catch (error) {
      console.log(`❌ Database query failed: ${error.message}`);
    }
  }

  async testWebhookProcessing() {
    console.log('\n4️⃣ Live Webhook Test');
    console.log('-' .repeat(25));

    const testEmail = `debug-test-${Date.now()}@example.com`;
    
    try {
      console.log(`📧 Testing webhook with: ${testEmail}`);
      
      const payload = {
        event: 'order.success',
        thrivecart_secret: this.webhookSecret,
        customer_email: testEmail,
        customer_first_name: 'Debug',
        customer_last_name: 'Test',
        order_id: `DEBUG-${Date.now()}`,
        order_total: '5.00',
        order_currency: 'USD',
        product_name: 'Habit Tracker Monthly',
        item_name: 'Habit Tracker Monthly'
      };

      const response = await axios.post(`${this.webhookUrl}/webhook/thrivecart`, 
        new URLSearchParams(payload), {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        timeout: 10000
      });

      console.log(`✅ Webhook response: ${response.status}`);

      // Wait a moment for processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Check if user was created
      const userCheck = await pool.query(`
        SELECT pu.email, ac.code as access_code, eq.status as email_status
        FROM paid_users pu
        JOIN access_codes ac ON pu.access_code = ac.code
        LEFT JOIN email_queue eq ON pu.email = eq.to_email
        WHERE pu.email = $1
      `, [testEmail]);

      if (userCheck.rows.length > 0) {
        const user = userCheck.rows[0];
        console.log(`✅ User created: ${user.access_code}`);
        console.log(`📧 Email status: ${user.email_status || 'Not queued'}`);
        
        // Cleanup test data
        await pool.query('DELETE FROM email_queue WHERE to_email = $1', [testEmail]);
        await pool.query('DELETE FROM access_codes WHERE code = $1', [user.access_code]);
        await pool.query('DELETE FROM paid_users WHERE email = $1', [testEmail]);
        console.log('🧹 Test data cleaned');
      } else {
        console.log('❌ User was not created');
      }

    } catch (error) {
      console.log(`❌ Webhook test failed: ${error.message}`);
      if (error.response) {
        console.log(`   Response: ${error.response.status} - ${error.response.statusText}`);
      }
    }
  }

  async identifyIssues() {
    console.log('\n5️⃣ Issue Analysis');
    console.log('-' .repeat(25));

    const issues = [];
    const recommendations = [];

    // Check for common issues
    try {
      // Check for failed webhook events
      const failedWebhooks = await pool.query(`
        SELECT COUNT(*) as count
        FROM webhook_events 
        WHERE created_at >= NOW() - INTERVAL '7 days'
        AND source = 'thrivecart'
        AND error_message IS NOT NULL
      `);

      if (failedWebhooks.rows[0].count > 0) {
        issues.push(`❌ ${failedWebhooks.rows[0].count} failed webhook(s) in last 7 days`);
      }

      // Check for users without emails
      const usersWithoutEmails = await pool.query(`
        SELECT COUNT(*) as count
        FROM paid_users pu
        LEFT JOIN email_queue eq ON pu.email = eq.to_email
        WHERE pu.created_at >= NOW() - INTERVAL '7 days'
        AND eq.to_email IS NULL
      `);

      if (usersWithoutEmails.rows[0].count > 0) {
        issues.push(`❌ ${usersWithoutEmails.rows[0].count} user(s) without queued emails`);
      }

      // Check for pending emails
      const pendingEmails = await pool.query(`
        SELECT COUNT(*) as count
        FROM email_queue
        WHERE status = 'pending'
        AND created_at >= NOW() - INTERVAL '1 hour'
      `);

      if (pendingEmails.rows[0].count > 0) {
        issues.push(`⚠️  ${pendingEmails.rows[0].count} email(s) pending for >1 hour`);
      }

      // URL mismatch check
      if (process.env.BOT_WEBHOOK_URL?.includes('https://') && !this.webhookUrl.includes('https://')) {
        issues.push('⚠️  Webhook URL configured as HTTPS but server running HTTP');
        recommendations.push('• Update ThriveCart webhook URL to use HTTP instead of HTTPS');
      }

      // Output results
      if (issues.length === 0) {
        console.log('✅ No issues detected');
        console.log('\n🔍 System Status:');
        console.log('   • Webhook endpoint: ✅ Accessible');
        console.log('   • Recent processing: ✅ Working');
        console.log('   • Email delivery: ✅ Operational');
        console.log('   • Database: ✅ Connected');
      } else {
        console.log('🚨 Issues Detected:');
        issues.forEach(issue => console.log(`   ${issue}`));
      }

      if (recommendations.length > 0) {
        console.log('\n💡 Recommendations:');
        recommendations.forEach(rec => console.log(`   ${rec}`));
      }

    } catch (error) {
      console.log(`❌ Issue analysis failed: ${error.message}`);
    }
  }
}

async function main() {
  const webhookDebugger = new ThriveCartWebhookDebugger();
  await webhookDebugger.runDiagnostics();
}

main().catch(console.error);