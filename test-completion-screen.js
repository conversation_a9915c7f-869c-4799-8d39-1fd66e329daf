#!/usr/bin/env node

require('dotenv').config();
const webhookController = require('./src/controllers/webhookController');
const pool = require('./src/db/connection');

async function testCompletionScreen() {
  try {
    const testPhone = '+27646921984';
    
    console.log('🧪 TESTING COMPLETION SCREEN');
    console.log('='.repeat(60));
    
    // Step 1: Reset user to clean slate
    console.log('\n🔄 STEP 1: Resetting to clean slate...');
    const resetReq = {
      body: {
        From: `whatsapp:${testPhone}`,
        Body: 'RESET_TEST'
      }
    };
    
    let resetResponse = '';
    const resetRes = {
      type: () => resetRes,
      send: (data) => {
        const match = data.match(/<Message>(.*?)<\/Message>/s);
        resetResponse = match ? match[1] : data;
      }
    };
    
    await webhookController.handleIncomingMessage(resetReq, resetRes);
    console.log('   ✅ Reset complete');
    
    // Step 2: Log all 5 habits with the exact input that should trigger completion
    console.log('\n📝 STEP 2: Entering logging submenu...');
    const logMenuReq = {
      body: {
        From: `whatsapp:${testPhone}`,
        Body: '1'
      }
    };
    
    let logMenuResponse = '';
    const logMenuRes = {
      type: () => logMenuRes,
      send: (data) => {
        const match = data.match(/<Message>(.*?)<\/Message>/s);
        logMenuResponse = match ? match[1] : data;
      }
    };
    
    await webhookController.handleIncomingMessage(logMenuReq, logMenuRes);
    console.log('   📋 Logging submenu shown');
    
    // Step 3: Log all habits with "1y 2n 3y 4y 5y" - this should trigger completion
    console.log('\n✅ STEP 3: Logging all habits with "1y 2n 3y 4y 5y"...');
    const completeReq = {
      body: {
        From: `whatsapp:${testPhone}`,
        Body: '1y 2n 3y 4y 5y'
      }
    };
    
    let completionResponse = '';
    const completionRes = {
      type: () => completionRes,
      send: (data) => {
        // Capture the full TwiML with both messages
        completionResponse = data;
      }
    };
    
    await webhookController.handleIncomingMessage(completeReq, completionRes);
    
    // Step 4: Analyze the response
    console.log('\n🔍 STEP 4: Analyzing response...');
    console.log('─'.repeat(50));
    console.log('FULL RESPONSE:');
    console.log(completionResponse);
    console.log('─'.repeat(50));
    
    // Extract individual messages
    const messageMatches = completionResponse.match(/<Message>(.*?)<\/Message>/gs);
    console.log('\n📨 MESSAGES SENT:');
    
    if (messageMatches) {
      messageMatches.forEach((match, index) => {
        const content = match.replace(/<\/?Message>/g, '');
        console.log(`\nMESSAGE ${index + 1}:`);
        console.log('─'.repeat(30));
        console.log(content);
        console.log('─'.repeat(30));
      });
    }
    
    // Step 5: Verification
    console.log('\n✅ VERIFICATION:');
    
    if (completionResponse.includes('🎉 DAY COMPLETE!')) {
      console.log('   ✅ MESSAGE 1: Completion screen triggered correctly');
    } else {
      console.log('   ❌ MESSAGE 1: Completion screen NOT triggered');
    }
    
    if (completionResponse.includes('Click and hold the previous message')) {
      console.log('   ✅ MESSAGE 2: Follow-up instructions sent');
    } else {
      console.log('   ❌ MESSAGE 2: Follow-up instructions NOT sent');
    }
    
    const messageCount = messageMatches ? messageMatches.length : 0;
    console.log(`   📊 Total messages sent: ${messageCount}`);
    
    if (messageCount === 2) {
      console.log('   ✅ Correct: Two-message completion flow working');
    } else {
      console.log('   ❌ Incorrect: Should send exactly 2 messages');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  } finally {
    await pool.end();
  }
}

testCompletionScreen().catch(console.error);