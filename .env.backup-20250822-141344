# Application Environment
NODE_ENV=development
PORT=3001

# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/lockin

# For Docker Compose
POSTGRES_DB=lockin
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres

# JWT Secret for sessions
JWT_SECRET=67e23c626ee4455001611bf599cbfbc576879b5a7e0258cb276eebb780277f73

# Twilio Configuration (Production WhatsApp Business API)
TWILIO_ACCOUNT_SID=**********************************
TWILIO_AUTH_TOKEN=89be7afea564923bc17e7ef79f7567ce
TWILIO_PHONE_NUMBER=+***********

# Logging
LOG_LEVEL=debug
LOG_DIR=./logs

# Optional: pgAdmin (for Docker Compose debug profile)
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin123

# Payment Configuration
PAYMENT_TEST_MODE=true
LEMONSQUEEZY_WEBHOOK_SECRET=test_webhook_secret_change_in_production
LEMONSQUEEZY_API_KEY=your_lemonsqueezy_api_key
LEMONSQUEEZY_STORE_ID=your_store_id
LEMONSQUEEZY_MONTHLY_PRODUCT_ID=your_monthly_product_id
LEMONSQUEEZY_MONTHLY_VARIANT_ID=your_monthly_variant_id
LEMONSQUEEZY_YEARLY_PRODUCT_ID=your_yearly_product_id
LEMONSQUEEZY_YEARLY_VARIANT_ID=your_yearly_variant_id

# Email Configuration (Brevo SMTP)
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>
SMTP_HOST=smtp-relay.brevo.com
SMTP_PORT=2525
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=1R3L6WxrTqsbQKdf

# ThriveCart Configuration
THRIVECART_CHECKOUT_URL=https://habittracker.thrivecart.com/habit-tracker-monthly/
THRIVECART_WEBHOOK_SECRET=FUQ2A97V0Q8A
THRIVECART_MONTHLY_PRODUCT=habit-tracker-monthly
THRIVECART_YEARLY_PRODUCT=habit-tracker-yearly

# Bot Configuration
BOT_WEBHOOK_URL=https://*************:3001