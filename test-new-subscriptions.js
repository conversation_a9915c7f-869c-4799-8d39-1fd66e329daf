const axios = require('axios');

// Test configuration
const WEBHOOK_URL = process.env.BOT_WEBHOOK_URL || 'http://localhost:3001';
const WEBHOOK_SECRET = process.env.THRIVECART_WEBHOOK_SECRET || 'FUQ2A97V0Q8A';

// Generate test email with timestamp
const testEmail = `test-${Date.now()}@example.com`;

// Test payloads for different subscription types
const testPayloads = {
  // Weekly subscription - $2.99/week
  weekly: {
    event: 'order.success',
    thrivecart_secret: WEBHOOK_SECRET,
    customer: {
      id: `CUST-WEEKLY-${Date.now()}`,
      email: `weekly-${testEmail}`,
      name: 'Weekly Test User',
      first_name: 'Weekly',
      last_name: 'Test'
    },
    order: {
      id: `ORDER-WEEKLY-${Date.now()}`,
      total: 299, // $2.99 in cents
      total_str: '2.99',
      currency: 'USD',
      charges: [{
        payment_plan_id: 'plan_weekly_299',
        payment_plan_name: 'Weekly Subscription - $2.99/week'
      }],
      future_charges: [{
        frequency: 'week',
        frequency_days: 7,
        due: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      }]
    },
    product_name: 'Habit Tracker Weekly - $2.99/week'
  },

  // Lifetime Founder subscription
  lifetime: {
    event: 'order.success',
    thrivecart_secret: WEBHOOK_SECRET,
    customer: {
      id: `CUST-LIFETIME-${Date.now()}`,
      email: `lifetime-${testEmail}`,
      name: 'Lifetime Founder User',
      first_name: 'Lifetime',
      last_name: 'Founder'
    },
    order: {
      id: `ORDER-LIFETIME-${Date.now()}`,
      total: 9999, // $99.99 in cents (or whatever lifetime price)
      total_str: '99.99',
      currency: 'USD',
      charges: [{
        payment_plan_id: 'plan_lifetime_founder',
        payment_plan_name: 'Lifetime Founder Access'
      }],
      // No future charges for lifetime
      future_charges: []
    },
    product_name: 'Habit Tracker Lifetime Founder Access'
  },

  // Monthly subscription - $5.99/month (existing)
  monthly: {
    event: 'order.success',
    thrivecart_secret: WEBHOOK_SECRET,
    customer: {
      id: `CUST-MONTHLY-${Date.now()}`,
      email: `monthly-${testEmail}`,
      name: 'Monthly Test User',
      first_name: 'Monthly',
      last_name: 'Test'
    },
    order: {
      id: `ORDER-MONTHLY-${Date.now()}`,
      total: 599, // $5.99 in cents
      total_str: '5.99',
      currency: 'USD',
      charges: [{
        payment_plan_id: 'plan_monthly_599',
        payment_plan_name: 'Monthly Subscription - $5.99/month'
      }],
      future_charges: [{
        frequency: 'month',
        frequency_days: 30,
        due: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
      }]
    },
    product_name: 'Habit Tracker Monthly - $5.99/month'
  },

  // Yearly subscription - $39.99/year (existing)
  yearly: {
    event: 'order.success',
    thrivecart_secret: WEBHOOK_SECRET,
    customer: {
      id: `CUST-YEARLY-${Date.now()}`,
      email: `yearly-${testEmail}`,
      name: 'Yearly Test User',
      first_name: 'Yearly',
      last_name: 'Test'
    },
    order: {
      id: `ORDER-YEARLY-${Date.now()}`,
      total: 3999, // $39.99 in cents
      total_str: '39.99',
      currency: 'USD',
      charges: [{
        payment_plan_id: 'plan_yearly_3999',
        payment_plan_name: 'Annual Subscription - $39.99/year'
      }],
      future_charges: [{
        frequency: 'year',
        frequency_days: 365,
        due: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString()
      }]
    },
    product_name: 'Habit Tracker Annual - $39.99/year'
  }
};

async function testWebhook(type) {
  const payload = testPayloads[type];
  
  if (!payload) {
    console.error(`Invalid subscription type: ${type}`);
    console.log('Valid types: weekly, lifetime, monthly, yearly');
    return;
  }

  console.log(`\n=== Testing ${type.toUpperCase()} Subscription ===`);
  console.log(`Email: ${payload.customer.email}`);
  console.log(`Amount: ${payload.order.total_str}`);
  console.log(`Plan: ${payload.order.charges[0].payment_plan_name}`);
  
  if (payload.order.future_charges?.[0]) {
    console.log(`Next billing: ${payload.order.future_charges[0].due}`);
    console.log(`Frequency: Every ${payload.order.future_charges[0].frequency_days} days`);
  } else {
    console.log(`Next billing: Never (Lifetime access)`);
  }

  try {
    const response = await axios.post(
      `${WEBHOOK_URL}/webhook/thrivecart`,
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
          'X-TC-Hmac-SHA256': 'test-signature'
        }
      }
    );

    console.log('\nWebhook Response:', response.status, response.data);
    
    // Check database for the created user
    const { Pool } = require('pg');
    const pool = new Pool({
      connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/lockin'
    });
    
    const result = await pool.query(
      `SELECT email, subscription_type, status, expires_at, 
              next_billing_date, billing_frequency_days, 
              payment_plan_name, access_code
       FROM paid_users 
       WHERE email = $1`,
      [payload.customer.email]
    );
    
    if (result.rows[0]) {
      const user = result.rows[0];
      console.log('\n✅ User created in database:');
      console.log(`  Subscription Type: ${user.subscription_type}`);
      console.log(`  Status: ${user.status}`);
      console.log(`  Access Code: ${user.access_code}`);
      console.log(`  Expires At: ${user.expires_at || 'Never (Lifetime)'}`);
      console.log(`  Next Billing: ${user.next_billing_date || 'N/A'}`);
      console.log(`  Billing Frequency: ${user.billing_frequency_days ? `${user.billing_frequency_days} days` : 'N/A'}`);
      console.log(`  Plan Name: ${user.payment_plan_name}`);
    } else {
      console.log('\n❌ User not found in database');
    }
    
    await pool.end();
    
  } catch (error) {
    console.error('\n❌ Webhook Error:', error.response?.data || error.message);
  }
}

// Test subscription renewal for existing users
async function testRenewal(type) {
  const payload = testPayloads[type];
  
  if (!payload) {
    console.error(`Invalid subscription type: ${type}`);
    return;
  }

  // Modify event type for renewal
  payload.event = 'order.subscription_payment';
  
  console.log(`\n=== Testing ${type.toUpperCase()} Renewal ===`);
  console.log(`Email: ${payload.customer.email}`);
  console.log(`Renewal Amount: ${payload.order.total_str}`);

  try {
    const response = await axios.post(
      `${WEBHOOK_URL}/webhook/thrivecart`,
      payload,
      {
        headers: {
          'Content-Type': 'application/json',
          'X-TC-Hmac-SHA256': 'test-signature'
        }
      }
    );

    console.log('\nRenewal Response:', response.status, response.data);
    
  } catch (error) {
    console.error('\n❌ Renewal Error:', error.response?.data || error.message);
  }
}

// Main test function
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const type = args[1];

  if (!command) {
    console.log('Usage: node test-new-subscriptions.js <command> [type]');
    console.log('\nCommands:');
    console.log('  test <type>    - Test new subscription (weekly, lifetime, monthly, yearly)');
    console.log('  renew <type>   - Test subscription renewal');
    console.log('  all            - Test all subscription types');
    console.log('\nExamples:');
    console.log('  node test-new-subscriptions.js test weekly');
    console.log('  node test-new-subscriptions.js test lifetime');
    console.log('  node test-new-subscriptions.js renew monthly');
    console.log('  node test-new-subscriptions.js all');
    process.exit(1);
  }

  if (command === 'all') {
    // Test all subscription types
    for (const subType of ['weekly', 'lifetime', 'monthly', 'yearly']) {
      await testWebhook(subType);
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds between tests
    }
  } else if (command === 'test') {
    await testWebhook(type);
  } else if (command === 'renew') {
    await testRenewal(type);
  } else {
    console.error(`Unknown command: ${command}`);
    process.exit(1);
  }
}

// Run tests
main().catch(error => {
  console.error('Test failed:', error);
  process.exit(1);
});