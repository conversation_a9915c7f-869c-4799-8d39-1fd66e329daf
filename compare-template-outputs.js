#!/usr/bin/env node

require('dotenv').config();

async function compareTemplateOutputs() {
  try {
    console.log('🔍 COMPARING welcome_dynamic vs welcome_multi_code TEMPLATES...');
    
    const emailService = require('./src/services/emailService');
    
    // Same base data for both templates
    const baseData = {
      subscription_type: 'Annual',
      subscription_price: '$39.99/year',
      primary_access_code: 'HABIT-TEST123',
      botPhone: '+19035155547',
      accessCode: 'HABIT-TEST123',
      amountPaid: '39.99',
      isLifetime: false,
      accessCodes: [
        {
          code: 'HABIT-TEST123',
          type: 'primary',
          description: 'Main access code'
        }
      ],
      hasBumps: false,
      bumps: [],
      bonus_codes: null,
      isAffiliate: true,
      pricingText: '$39.99/year',
      primaryCode: 'HABIT-TEST123',
      affiliateCode: 'AFF-TEST123',
      subscriptionType: 'yearly',
      subscriptionTypeDisplay: 'Annual'
    };
    
    console.log('\n1️⃣ TESTING welcome_dynamic (SINGLE CODE - BROKEN)');
    console.log('='.repeat(60));
    
    const dynamicTemplate = emailService.getWelcomeDynamicTemplate(baseData);
    
    console.log('Subject:', dynamicTemplate.subject);
    console.log('HTML length:', dynamicTemplate.html.length);
    console.log('Text length:', dynamicTemplate.text.length);
    
    // Check footer in welcome_dynamic
    const step5IndexDynamic = dynamicTemplate.html.indexOf('Start building unstoppable habits!');
    if (step5IndexDynamic !== -1) {
      const afterStep5Dynamic = dynamicTemplate.html.substring(step5IndexDynamic + 'Start building unstoppable habits!'.length);
      console.log('Characters after step 5:', afterStep5Dynamic.length);
      
      const footerChecksDynamic = [
        { name: 'Rich signature', check: afterStep5Dynamic.includes("Let's Lock In") && afterStep5Dynamic.includes('Rich') },
        { name: 'Affiliate section', check: afterStep5Dynamic.includes('💰 Earn with Lock In') },
        { name: 'Social links', check: afterStep5Dynamic.includes('@richvieren') },
        { name: 'Copyright', check: afterStep5Dynamic.includes('© 2025 Lock In') }
      ];
      
      console.log('Footer check:');
      footerChecksDynamic.forEach(({ name, check }) => {
        console.log(`  ${check ? '✅' : '❌'} ${name}`);
      });
    }
    
    console.log('\n2️⃣ TESTING welcome_multi_code (BUMP OFFERS - WORKING)');
    console.log('='.repeat(60));
    
    // For multi-code, we need bonus codes
    const multiData = {
      ...baseData,
      bonus_codes: ['HABIT-BONUS1', 'HABIT-BONUS2']
    };
    
    const multiTemplate = emailService.getWelcomeMultiCodeTemplate(multiData);
    
    console.log('Subject:', multiTemplate.subject);
    console.log('HTML length:', multiTemplate.html.length);
    console.log('Text length:', multiTemplate.text.length);
    
    // Check footer in welcome_multi_code
    const step5IndexMulti = multiTemplate.html.indexOf('Start building unstoppable habits!');
    if (step5IndexMulti !== -1) {
      const afterStep5Multi = multiTemplate.html.substring(step5IndexMulti + 'Start building unstoppable habits!'.length);
      console.log('Characters after step 5:', afterStep5Multi.length);
      
      const footerChecksMulti = [
        { name: 'Rich signature', check: afterStep5Multi.includes("Let's Lock In") && afterStep5Multi.includes('Rich') },
        { name: 'Affiliate section', check: afterStep5Multi.includes('💰 Earn with Lock In') },
        { name: 'Social links', check: afterStep5Multi.includes('@richvieren') },
        { name: 'Copyright', check: afterStep5Multi.includes('© 2025 Lock In') }
      ];
      
      console.log('Footer check:');
      footerChecksMulti.forEach(({ name, check }) => {
        console.log(`  ${check ? '✅' : '❌'} ${name}`);
      });
    }
    
    console.log('\n📊 COMPARISON ANALYSIS:');
    console.log('='.repeat(60));
    console.log('Same subject?', dynamicTemplate.subject === multiTemplate.subject);
    console.log('Same HTML length?', dynamicTemplate.html.length === multiTemplate.html.length);
    console.log('Same HTML content?', dynamicTemplate.html === multiTemplate.html);
    
    if (dynamicTemplate.html !== multiTemplate.html) {
      console.log('\n🚨 TEMPLATES ARE DIFFERENT!');
      
      // Find the first difference
      let diffIndex = -1;
      for (let i = 0; i < Math.min(dynamicTemplate.html.length, multiTemplate.html.length); i++) {
        if (dynamicTemplate.html[i] !== multiTemplate.html[i]) {
          diffIndex = i;
          break;
        }
      }
      
      if (diffIndex !== -1) {
        console.log(`First difference at character ${diffIndex}:`);
        console.log('Dynamic:', dynamicTemplate.html.substring(diffIndex - 20, diffIndex + 50));
        console.log('Multi:   ', multiTemplate.html.substring(diffIndex - 20, diffIndex + 50));
      } else if (dynamicTemplate.html.length !== multiTemplate.html.length) {
        console.log('Templates have different lengths!');
        console.log('Dynamic length:', dynamicTemplate.html.length);
        console.log('Multi length:', multiTemplate.html.length);
        
        const shorter = dynamicTemplate.html.length < multiTemplate.html.length ? 'dynamic' : 'multi';
        const longer = shorter === 'dynamic' ? 'multi' : 'dynamic';
        
        console.log(`${shorter} template is shorter!`);
        
        if (shorter === 'dynamic') {
          const extraContent = multiTemplate.html.substring(dynamicTemplate.html.length);
          console.log('Extra content in multi template:');
          console.log('"' + extraContent.substring(0, 200) + '"...');
        }
      }
    } else {
      console.log('✅ Templates generate identical content!');
    }
    
    // Save both for detailed comparison
    require('fs').writeFileSync('/var/www/lockin/welcome-dynamic-test.html', dynamicTemplate.html);
    require('fs').writeFileSync('/var/www/lockin/welcome-multi-test.html', multiTemplate.html);
    
    console.log('\n💾 SAVED FILES FOR COMPARISON:');
    console.log('- welcome-dynamic-test.html (broken version)');
    console.log('- welcome-multi-test.html (working version)');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
}

compareTemplateOutputs().catch(console.error);