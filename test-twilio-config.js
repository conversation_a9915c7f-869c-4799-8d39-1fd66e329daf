require('dotenv').config();
const twilio = require('twilio');

console.log('Twilio Configuration Check:');
console.log('===========================');
console.log('Account SID:', process.env.TWILIO_ACCOUNT_SID ? 'Configured' : 'Missing');
console.log('Auth Token:', process.env.TWILIO_AUTH_TOKEN ? 'Configured' : 'Missing');
console.log('Phone Number in ENV:', process.env.TWILIO_PHONE_NUMBER);
console.log('Expected Production Number: +***********');
console.log('');

// Initialize Twilio client
if (process.env.TWILIO_ACCOUNT_SID && process.env.TWILIO_AUTH_TOKEN) {
  const client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
  
  // Check incoming phone numbers
  client.incomingPhoneNumbers
    .list({ limit: 10 })
    .then(numbers => {
      console.log('Configured Phone Numbers:');
      numbers.forEach(number => {
        console.log(`- ${number.phoneNumber} (${number.friendlyName})`);
        console.log(`  SMS URL: ${number.smsUrl}`);
        console.log(`  Voice URL: ${number.voiceUrl}`);
        console.log('');
      });
    })
    .catch(err => {
      console.error('Error fetching phone numbers:', err.message);
    });
}