#!/usr/bin/env node

require('dotenv').config();
const emailService = require('./src/services/emailService');

const templateData = {
  'botPhone': '+19035155547',
  'accessCode': 'TEST-HTML-FIX',
  'subscriptionType': 'yearly',
  'subscription_type': 'Annual', 
  'subscription_price': '$39.99/year',
  'primary_access_code': 'TEST-HTML-FIX',
  'isAffiliate': true,
  'affiliateCode': 'TEST-AFF'
};

const { html } = emailService.getWelcomeDynamicTemplate(templateData);

// Check affiliate links now
const affiliateLinks = html.match(/<a[^>]*thrivecart[^>]*>/g);
if (affiliateLinks) {
  console.log('✅ COMPLETE AFFILIATE LINKS:');
  affiliateLinks.forEach((link, i) => {
    console.log(`${i+1}: ${link}`);
  });
} else {
  console.log('❌ Still no complete affiliate links found');
}

// Run HTML validation
const styleRegex = /style="([^"]*)"/g;
let match;
let issues = [];

while ((match = styleRegex.exec(html)) !== null) {
  const styleContent = match[1];
  
  // Check for unmatched quotes within CSS values
  const quotes = (styleContent.match(/'/g) || []).length;
  if (quotes % 2 !== 0) {
    issues.push({
      type: 'Unmatched quotes',
      context: match[0],
      line: html.substring(0, match.index).split('\n').length
    });
  }
}

if (issues.length > 0) {
  console.log('❌ HTML ISSUES REMAINING:');
  issues.forEach(issue => console.log(`   ${issue.type}: ${issue.context}`));
} else {
  console.log('✅ No HTML syntax issues found');
}

console.log(`\n📊 HTML Length: ${html.length} characters`);

// Check step 5 content
const step5Index = html.indexOf('Start building unstoppable habits!');
const afterStep5 = html.substring(step5Index + 'Start building unstoppable habits!'.length);
console.log(`📊 Characters after step 5: ${afterStep5.length}`);

// Save for inspection
require('fs').writeFileSync('/var/www/lockin/fixed-email-test.html', html);
console.log('\n💾 Fixed email saved to: fixed-email-test.html');