#!/usr/bin/env node
require('dotenv').config();

const stateMachine = require('./src/services/stateMachinePaymentEnforced');
const User = require('./src/models/User');
const paymentService = require('./src/services/paymentService');

async function debugStartCommand() {
  console.log('🔍 DEBUG: START Command Processing\n');
  
  const testPhone = '+15551112222';
  const accessCode = 'HABIT-8E6163'; // From database
  
  try {
    // Create test user
    const user = await User.findOrCreate(testPhone);
    console.log('👤 Test user:', {
      id: user.id,
      phone: user.phone,
      status: user.status,
      current_state: user.current_state
    });
    
    // Check payment status BEFORE START command
    console.log('\n💳 Payment Status Check (BEFORE):');
    const accessCheckBefore = await paymentService.checkUserAccess(testPhone);
    console.log('Has access:', accessCheckBefore.hasAccess);
    console.log('Test mode:', accessCheckBefore.testMode);
    console.log('Error:', accessCheckBefore.error);
    
    // Test normal command first (should show paywall)
    console.log('\n🚫 Testing normal command (should show paywall):');
    const normalResponse = await stateMachine.processMessage(user, 'hello');
    console.log('Response preview:', normalResponse.message.substring(0, 80) + '...');
    console.log('Contains ACCESS REQUIRED:', normalResponse.message.includes('ACCESS REQUIRED'));
    
    // Test START command processing
    console.log('\n🔑 Testing START command processing:');
    console.log('Command:', `START ${accessCode}`);
    
    // Add debug logging
    console.log('\n📝 Debug logs will show in server logs...');
    
    const startResponse = await stateMachine.processMessage(user, `START ${accessCode}`);
    
    console.log('\n📤 START Command Response:');
    console.log('Success indicators:', {
      contains_success: startResponse.message.includes('Success'),
      contains_celebration: startResponse.message.includes('🎉'),
      contains_welcome: startResponse.message.includes('Welcome'),
      contains_paywall: startResponse.message.includes('ACCESS REQUIRED')
    });
    
    console.log('Full response preview:', startResponse.message.substring(0, 200) + '...');
    console.log('New state:', startResponse.newState);
    
    // Check user status AFTER START command
    console.log('\n👤 User Status After START:');
    const updatedUser = await User.findByPhone(testPhone);
    console.log('Updated user:', {
      id: updatedUser.id,
      status: updatedUser.status,
      current_state: updatedUser.current_state,
      display_name: updatedUser.display_name
    });
    
    // Check payment status AFTER START command
    console.log('\n💳 Payment Status Check (AFTER):');
    const accessCheckAfter = await paymentService.checkUserAccess(testPhone);
    console.log('Has access:', accessCheckAfter.hasAccess);
    console.log('Paid user info:', accessCheckAfter.paidUser ? {
      id: accessCheckAfter.paidUser.id,
      email: accessCheckAfter.paidUser.email,
      phone: accessCheckAfter.paidUser.phone
    } : 'None');
    
    // Check database for access code usage
    console.log('\n🗄️ Database Access Code Status:');
    const { Pool } = require('pg');
    const pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: false
    });
    
    const codeResult = await pool.query(
      'SELECT * FROM access_codes WHERE code = $1',
      [accessCode]
    );
    
    const paidUserResult = await pool.query(
      'SELECT * FROM paid_users WHERE access_code = $1',
      [accessCode]
    );
    
    console.log('Access code record:', codeResult.rows[0] || 'Not found');
    console.log('Paid user record:', {
      id: paidUserResult.rows[0]?.id,
      phone: paidUserResult.rows[0]?.phone,
      status: paidUserResult.rows[0]?.status
    });
    
    await pool.end();
    
    // Test another command after START
    console.log('\n🎯 Testing command AFTER START (should work if activated):');
    const finalUser = await User.findByPhone(testPhone);
    const afterResponse = await stateMachine.processMessage(finalUser, 'hello again');
    console.log('Response preview:', afterResponse.message.substring(0, 100) + '...');
    console.log('Still shows paywall:', afterResponse.message.includes('ACCESS REQUIRED'));
    
  } catch (error) {
    console.error('\n❌ Debug failed:', error.message);
    console.error(error.stack);
  }
}

debugStartCommand().catch(console.error);