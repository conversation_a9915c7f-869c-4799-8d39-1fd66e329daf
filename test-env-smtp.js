#!/usr/bin/env node

require('dotenv').config();

console.log('Testing environment variables...\n');
console.log('SMTP Configuration:');
console.log('SMTP_HOST:', process.env.SMTP_HOST);
console.log('SMTP_PORT:', process.env.SMTP_PORT);
console.log('SMTP_USER:', process.env.SMTP_USER);
console.log('SMTP_PASS:', process.env.SMTP_PASS ? '***HIDDEN***' : 'NOT SET');
console.log('EMAIL_FROM:', process.env.EMAIL_FROM);
console.log('EMAIL_REPLY_TO:', process.env.EMAIL_REPLY_TO);

console.log('\nChecking if config is valid for email service:');
const hasSmtpHost = !!process.env.SMTP_HOST;
const hasSmtpUser = !!process.env.SMTP_USER;
console.log('Has SMTP_HOST:', hasSmtpHost);
console.log('Has SMTP_USER:', hasSmtpUser);
console.log('Will use SMTP:', hasSmtpHost && hasSmtpUser);

// Now test email service initialization
const { createTransport } = require('nodemailer');

if (process.env.SMTP_HOST && process.env.SMTP_USER) {
  console.log('\nCreating transporter...');
  const transporter = createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_PORT === '465',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    }
  });

  console.log('Verifying transporter...');
  transporter.verify((error) => {
    if (error) {
      console.error('❌ Email transporter verification failed:', error.message);
    } else {
      console.log('✅ Email service ready!');
    }
    process.exit(0);
  });
} else {
  console.log('\n❌ SMTP not configured - emails will be logged only');
  process.exit(1);
}