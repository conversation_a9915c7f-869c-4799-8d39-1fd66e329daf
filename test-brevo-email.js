#!/usr/bin/env node

const nodemailer = require('nodemailer');
require('dotenv').config();

async function testBrevoEmail() {
  console.log('Testing Brevo SMTP email delivery...\n');
  
  console.log('SMTP Configuration:');
  console.log('- Host:', process.env.SMTP_HOST);
  console.log('- Port:', process.env.SMTP_PORT);
  console.log('- User:', process.env.SMTP_USER);
  console.log('- From:', process.env.EMAIL_FROM);
  console.log('- Reply-To:', process.env.EMAIL_REPLY_TO);
  console.log();

  // Create transporter
  const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: false,
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    }
  });

  try {
    // Verify transporter
    console.log('Verifying SMTP connection...');
    await transporter.verify();
    console.log('✓ SMTP connection verified successfully\n');

    // Send test email
    console.log('Sending test email...');
    const testAccessCode = 'HABIT-TEST123';
    const botPhone = '+19035155547';
    
    const info = await transporter.sendMail({
      from: `"Lockin Habit Tracker" <${process.env.EMAIL_FROM}>`,
      to: '<EMAIL>', // This will be visible in logs even if not delivered
      replyTo: process.env.EMAIL_REPLY_TO,
      subject: 'Your Lockin Habit Tracker Access Code',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #4CAF50; color: white; padding: 20px; text-align: center; border-radius: 5px; }
            .code-box { background: #f4f4f4; border: 2px dashed #4CAF50; padding: 15px; margin: 20px 0; text-align: center; }
            .code { font-size: 24px; font-weight: bold; color: #4CAF50; letter-spacing: 2px; }
            .steps { background: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0; }
            .step { margin: 10px 0; padding-left: 20px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Welcome to Habit Tracker!</h1>
              <p>Your Monthly Subscription is Active</p>
            </div>
            
            <p>Hi there!</p>
            
            <p>Thank you for subscribing to Habit Tracker! Your monthly subscription ($5/month) is now active.</p>
            
            <div class="code-box">
              <p>Your Access Code:</p>
              <div class="code">${testAccessCode}</div>
            </div>
            
            <div class="steps">
              <h3>How to Get Started:</h3>
              <div class="step">1. Open WhatsApp on your phone</div>
              <div class="step">2. Send a message to: <strong>${botPhone}</strong></div>
              <div class="step">3. Type exactly: <strong>START ${testAccessCode}</strong></div>
              <div class="step">4. Follow the setup instructions to add your habits</div>
              <div class="step">5. Start tracking your daily progress!</div>
            </div>
            
            <p><strong>Important:</strong> Keep this access code safe. You'll need it to activate your account.</p>
          </div>
        </body>
        </html>
      `,
      text: `
Welcome to Habit Tracker!

Thank you for subscribing! Your monthly subscription ($5/month) is now active.

YOUR ACCESS CODE: ${testAccessCode}

How to Get Started:
1. Open WhatsApp on your phone
2. Send a message to: ${botPhone}
3. Type exactly: START ${testAccessCode}
4. Follow the setup instructions to add your habits
5. Start tracking your daily progress!

Important: Keep this access code safe. You'll need it to activate your account.
      `
    });

    console.log('✓ Test email sent successfully!');
    console.log('Message ID:', info.messageId);
    console.log('Response:', info.response);
    console.log('\n✅ Brevo SMTP is configured and working correctly!');
    
  } catch (error) {
    console.error('❌ Error testing Brevo SMTP:', error.message);
    console.error('Full error:', error);
    process.exit(1);
  }
}

testBrevoEmail();