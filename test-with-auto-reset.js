#!/usr/bin/env node

require('dotenv').config();
const webhookController = require('./src/controllers/webhookController');
const pool = require('./src/db/connection');

async function autoReset(phone = '+27646921984') {
  console.log(`🧹 AUTO-RESET: Clearing all logs for ${phone}...`);
  const client = await pool.connect();
  
  // Delete ALL logs for this user (not just today)
  const result = await client.query(
    'DELETE FROM habit_logs WHERE user_id = (SELECT id FROM users WHERE phone = $1) RETURNING *',
    [phone]
  );
  
  // Reset user state
  await client.query(
    'UPDATE users SET current_state = $1, session_context = $2 WHERE phone = $3',
    ['MAIN_MENU', '{}', phone]
  );
  
  console.log(`✅ Deleted ${result.rowCount} logs and reset user state`);
  client.release();
}

async function testWebhook(phone, message, description) {
  console.log(`\n📱 ${description}`);
  console.log(`USER SENDS: "${message}"`);
  console.log('─'.repeat(60));
  
  const req = {
    body: {
      From: `whatsapp:${phone}`,
      Body: message
    }
  };
  
  let responseMessage = '';
  const res = {
    type: () => res,
    send: (data) => {
      // Extract message from TwiML
      const match = data.match(/<Message>(.*?)<\/Message>/s);
      responseMessage = match ? match[1] : data;
    }
  };
  
  await webhookController.handleIncomingMessage(req, res);
  
  console.log('🤖 BOT RESPONDS:');
  console.log(responseMessage);
  console.log('─'.repeat(60));
  
  return responseMessage;
}

async function runCriticalTest() {
  try {
    const testPhone = '+27646921984';
    
    console.log('🚨 CRITICAL TEST: FIXING POST-LOG SCREEN');
    console.log('='.repeat(80));
    
    // Step 1: Auto-reset
    await autoReset(testPhone);
    
    // Step 2: Show blank slate main menu
    await testWebhook(testPhone, 'menu', 'STEP 1: Blank slate main menu');
    
    // Step 3: Enter logging mode
    await testWebhook(testPhone, '1', 'STEP 2: Enter logging mode');
    
    // Step 4: Log with "1y 2n 3y 4y 5y" - THIS IS THE CRITICAL TEST
    const response = await testWebhook(testPhone, '1y 2n 3y 4y 5y', 'STEP 3: Log "1y 2n 3y 4y 5y" - SHOULD SHOW MAIN MENU, NOT SUCCESS MESSAGE');
    
    // Step 5: Check if we got the main menu or the broken success message
    if (response.includes('Habits logged for today!')) {
      console.log('\n❌ CRITICAL FAILURE: Still showing success message instead of main menu!');
      console.log('This is the exact bug that needs to be fixed.');
    } else if (response.includes('📋 Your Habits (5/5):')) {
      console.log('\n✅ SUCCESS: Now showing main menu after logging!');
    } else {
      console.log('\n⚠️  UNEXPECTED: Unknown response format');
    }
    
    // Step 6: Verify database state
    console.log('\n📊 DATABASE VERIFICATION:');
    const client = await pool.connect();
    const result = await client.query(`
      SELECT h.habit_number, h.habit_name, hl.completed
      FROM habits h
      LEFT JOIN habit_logs hl ON h.id = hl.habit_id AND hl.log_date = CURRENT_DATE
      WHERE h.user_id = (SELECT id FROM users WHERE phone = $1)
      ORDER BY h.habit_number
    `, [testPhone]);
    
    result.rows.forEach(row => {
      const status = row.completed === null ? '⚠️ (NULL)' : 
                     row.completed === true ? '✅ (TRUE)' : '❌ (FALSE)';
      console.log(`   ${row.habit_number}. ${row.habit_name}: ${status}`);
    });
    
    client.release();
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await pool.end();
  }
}

runCriticalTest().catch(console.error);