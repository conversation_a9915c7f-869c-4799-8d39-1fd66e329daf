#!/usr/bin/env node

require('dotenv').config();
const stateMachine = require('./src/services/stateMachine');
const User = require('./src/models/User');

async function testNewUserFlow() {
  console.log('Testing New User Flow\n');
  console.log('='.repeat(50));

  try {
    // Create a brand new user
    const testPhone = 'whatsapp:+15555550001';
    
    console.log(`Creating new user with phone: ${testPhone}`);
    const user = await User.findOrCreate(testPhone);
    
    console.log('\nUser Details:');
    console.log(`  ID: ${user.id}`);
    console.log(`  Phone: ${user.phone}`);
    console.log(`  Status: ${user.status}`);
    console.log(`  State: ${user.current_state}`);
    console.log(`  Is Unlocked: ${user.is_unlocked}`);
    
    // Send "menu" as first message
    console.log('\n' + '='.repeat(50));
    console.log('Sending "menu" as first message:');
    console.log('='.repeat(50) + '\n');
    
    const response = await stateMachine.processMessage(user, 'menu');
    console.log(response.message);
    
    console.log('\n' + '='.repeat(50));
    console.log('Response state:', response.newState);
    
  } catch (error) {
    console.error('Error:', error.message);
    console.error(error.stack);
  } finally {
    const pool = require('./src/db/connection');
    await pool.end();
  }
}

testNewUserFlow();