# Update Twilio Production Credentials

## Issues Found:
1. ✅ Server is running correctly on port 3001
2. ✅ Webhook endpoint is accessible at: http://165.22.17.127:3001/webhook/whatsapp
3. ❌ Twilio credentials are still set to test/development values
4. ❌ Phone number in .env (+***********) doesn't match production number (+***********)

## Required Actions:

### 1. Update .env file with production credentials:

You need to update these values in `/var/www/lockin/.env`:

```bash
# Replace these test values with your production Twilio credentials
TWILIO_ACCOUNT_SID=YOUR_PRODUCTION_ACCOUNT_SID
TWILIO_AUTH_TOKEN=YOUR_PRODUCTION_AUTH_TOKEN
TWILIO_PHONE_NUMBER=+***********
```

### 2. Get your production credentials from Twilio Console:

1. Log into [Twilio Console](https://console.twilio.com)
2. Navigate to your WhatsApp Business Account
3. Get your:
   - Account SID (starts with AC...)
   - Auth Token (click to reveal)
   - Confirm your WhatsApp Business Number: +***********

### 3. Update Webhook URL in Twilio:

The correct webhook URL for your bot is:
```
http://165.22.17.127:3001/webhook/whatsapp
```

In Twilio Console:
1. Go to Messaging > Settings > WhatsApp sandbox (or WhatsApp Business if in production)
2. Set "When a message comes in" webhook to: `http://165.22.17.127:3001/webhook/whatsapp`
3. Method: HTTP POST
4. Save the configuration

### 4. Commands to update and restart:

```bash
# Edit the .env file
nano /var/www/lockin/.env

# Update the three values mentioned above, then save

# Restart the bot
pm2 restart lockin

# Check logs
pm2 logs lockin --lines 50
```

### 5. Test the connection:

After updating, send "menu" to +*********** via WhatsApp and you should get a response.

## Security Note:
- Never commit the .env file with real credentials to git
- Consider using environment variables directly in PM2 for production