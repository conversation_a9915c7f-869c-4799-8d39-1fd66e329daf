# ThriveCart Integration Guide

## Overview
The ThriveCart webhook integration has been successfully implemented to handle payment processing for the Habit Tracker bot. This replaces/supplements the FastSpring integration while maintaining all existing functionality.

## Implementation Summary

### 1. New Files Created
- `/src/controllers/thrivecartController.js` - Main webhook handler for ThriveCart events
- `/test-thrivecart-webhook.js` - Webhook testing script
- `/test-thrivecart-integration.js` - Comprehensive integration test

### 2. Modified Files
- `/src/server.js` - Added ThriveCart webhook endpoint at `/webhook/thrivecart`
- `/src/services/paymentService.js` - Updated paywall message to support ThriveCart checkout URL

## Configuration

### Environment Variables
Add these to your `.env` file:

```bash
# ThriveCart Configuration
THRIVECART_CHECKOUT_URL=https://your-product.thrivecart.com/checkout
THRIVECART_WEBHOOK_SECRET=your_webhook_secret_here  # Optional but recommended
```

## Webhook Endpoint

### Production URL
```
https://yourdomain.com/webhook/thrivecart
```

### Supported Events
- `order.success` / `order.purchase` - New purchase
- `order.subscription_payment` / `order.rebill_success` - Subscription renewal
- `order.refund` - Refund processing
- `order.subscription_cancelled` - Subscription cancellation
- `order.subscription_resumed` - Subscription reactivation

## Features

### Access Code Generation
- Automatically generates unique `HABIT-XXXXX` access codes for new purchases
- Codes are sent via email to customers

### Affiliate Program
- Yearly subscribers automatically receive affiliate codes (`AFF-XXXXXX`)
- 30% commission rate by default

### Email Notifications
- Welcome emails with access codes sent automatically
- Different templates for monthly vs yearly subscribers

### Payment Enforcement
- Bot access restricted to users with active subscriptions
- Access verified through `START HABIT-XXXXX` command

## Testing

### 1. Test Mode
Enable test mode in `.env`:
```bash
PAYMENT_TEST_MODE=true
```

### 2. Run Integration Test
```bash
node test-thrivecart-integration.js
```

### 3. Test Individual Webhooks
```bash
node test-thrivecart-webhook.js
```

### 4. Manual Test via cURL
```bash
curl -X POST http://localhost:3001/webhook/thrivecart \
  -H "Content-Type: application/json" \
  -d '{
    "event": "order.success",
    "customer": {
      "email": "<EMAIL>"
    },
    "order": {
      "order_id": "TEST-123",
      "total": 5.00
    },
    "product": {
      "product_name": "Habit Tracker Monthly"
    }
  }'
```

## ThriveCart Setup

### 1. Product Configuration
Set up your products in ThriveCart:
- Monthly subscription: $5/month
- Yearly subscription: $30/year

### 2. Webhook Configuration
In ThriveCart product settings:
1. Go to Settings > Webhooks
2. Add webhook URL: `https://yourdomain.com/webhook/thrivecart`
3. Select events to send (recommended: all order events)
4. Save webhook secret if using signature verification

### 3. Checkout Page
Update your checkout page to include:
- Clear pricing information
- Terms of service link
- Privacy policy link
- Support email: <EMAIL>

## Database Schema

### Tables Used
- `paid_users` - Stores customer subscription data
- `access_codes` - Manages access codes for bot activation
- `payment_transactions` - Logs all payment events
- `webhook_events` - Stores raw webhook data for auditing
- `email_queue` - Manages email notifications

## Monitoring

### Check Webhook Logs
```sql
-- Recent ThriveCart webhooks
SELECT * FROM webhook_events 
WHERE source = 'thrivecart' 
ORDER BY created_at DESC 
LIMIT 10;

-- Check paid users
SELECT email, access_code, subscription_type, status 
FROM paid_users 
ORDER BY created_at DESC 
LIMIT 10;
```

### PM2 Logs
```bash
pm2 logs lockin --lines 50
```

## Troubleshooting

### Common Issues

1. **Webhook not receiving data**
   - Check ThriveCart webhook settings
   - Verify server is accessible from internet
   - Check firewall/security group settings

2. **Access codes not working**
   - Verify code format: `START HABIT-XXXXXX`
   - Check if code exists in database
   - Ensure user hasn't already activated code

3. **Emails not sending**
   - Check SMTP configuration in `.env`
   - Verify email queue processing
   - Check logs for email errors

## Maintenance

### Clean Test Data
```bash
# Remove test webhook events
PGPASSWORD=postgres psql -U postgres -h localhost -d lockin -c \
  "DELETE FROM webhook_events WHERE source = 'thrivecart' AND test_mode = true"

# Remove test users
PGPASSWORD=postgres psql -U postgres -h localhost -d lockin -c \
  "DELETE FROM paid_users WHERE email LIKE 'test%' AND test_mode = true"
```

### Backup Considerations
Always backup before major changes:
```bash
pg_dump -U postgres -h localhost lockin > backup_$(date +%Y%m%d).sql
```

## Support

For issues or questions:
- Email: <EMAIL>
- Check logs: `/root/.pm2/logs/lockin-*.log`
- Database issues: Check PostgreSQL logs

## Security Notes

1. **Always use HTTPS** for webhook endpoints in production
2. **Configure webhook secret** in ThriveCart and verify signatures
3. **Sanitize all input** from webhooks
4. **Log but don't expose** sensitive customer data
5. **Regular security audits** of payment flows

## Next Steps

1. Configure production ThriveCart account
2. Set up webhook secret for signature verification
3. Update checkout URL in environment variables
4. Test full payment flow in production
5. Monitor initial transactions closely