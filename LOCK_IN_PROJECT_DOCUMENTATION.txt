================================================================================
                    LOCK IN HABIT TRACKER - COMPLETE PROJECT DOCUMENTATION
================================================================================
                           WhatsApp Bot for Daily Habit Tracking
                                    Version 1.0.0
                              Last Updated: January 2025
================================================================================

TABLE OF CONTENTS
-----------------
1. PROJECT OVERVIEW
2. WHATSAPP COMMANDS & TESTING
3. ENVIRONMENT CONFIGURATION
4. DATABASE STRUCTURE
5. STATE MACHINE & USER FLOW
6. API ENDPOINTS & WEBHOOKS
7. PAYMENT & SUBSCRIPTION SYSTEM
8. TESTING PROCEDURES
9. DEPLOYMENT & MAINTENANCE
10. TROUBLESHOOTING GUIDE
11. DEVELOPER NOTES

================================================================================
1. PROJECT OVERVIEW
================================================================================

Lock In is a WhatsApp-based habit tracking bot that helps users track up to 5 daily habits.
Built with Node.js, Express, PostgreSQL, and integrated with Twilio for WhatsApp messaging.

Key Features:
- Track up to 5 daily habits via WhatsApp
- Visual progress tracking with emoji indicators
- 30-day and 100-day challenge tracking
- Automated daily check-ins
- Payment integration with FastSpring and ThriveCart
- GDPR compliant with data retention policies
- Session management with 30-minute timeout

Tech Stack:
- Backend: Node.js + Express
- Database: PostgreSQL
- Messaging: Twilio WhatsApp Business API
- Payments: FastSpring & ThriveCart webhooks
- Email: Brevo (SendinBlue) for transactional emails
- Deployment: DigitalOcean/Docker

================================================================================
2. WHATSAPP COMMANDS & TESTING
================================================================================

USER COMMANDS (Type in WhatsApp):
----------------------------------
RESET_TEST         - Reset user account to clean slate for testing
                     Clears all habit logs, resets to MAIN_MENU state
                     
START HABIT-XXXXX  - Activate account with access code
                     Links WhatsApp number to paid subscription

menu               - Return to main menu from any state
1, 2, 3, etc.     - Navigate menu options
yes/no            - Confirm or skip habits during daily check-in
STOP              - Unsubscribe from all messages (compliance)

PAYMENT TEST COMMANDS (Test Mode Only):
---------------------------------------
RESET_PAYMENT_TEST - Reset payment test state
                     Only works when PAYMENT_TEST_MODE=true

ADMIN/UTILITY COMMANDS (Server-side):
-------------------------------------
node src/utils/resetUser.js <phone>     - Reset specific user to onboarding
node src/utils/clearTodayLogs.js        - Clear today's habit logs for all users
npm run migrate                          - Run database migrations
npm run migrate:rollback                 - Rollback last migration

================================================================================
3. ENVIRONMENT CONFIGURATION
================================================================================

Required Environment Variables (.env file):
-------------------------------------------
# Application
NODE_ENV=development|production
PORT=3000

# Database
DATABASE_URL=postgresql://habituser:password@localhost:5432/habittracker
POSTGRES_DB=habittracker
POSTGRES_USER=habituser
POSTGRES_PASSWORD=yourpassword

# Twilio WhatsApp
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_PHONE_NUMBER=+**********

# Payment Processing
PAYMENT_TEST_MODE=true|false
FASTSPRING_WEBHOOK_SECRET=your_secret
THRIVECART_SECRET=your_secret

# Email Service (Brevo/SendinBlue)
BREVO_API_KEY=your_api_key
EMAIL_FROM=<EMAIL>
SMTP_HOST=smtp-relay.brevo.com
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password

# Logging
LOG_LEVEL=info|debug|error
LOG_DIR=/var/log

# Security
ENCRYPTION_KEY=your_32_char_encryption_key
JWT_SECRET=your_jwt_secret

# Optional
ADMIN_PASSWORD=your_admin_password
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin

================================================================================
4. DATABASE STRUCTURE
================================================================================

MAIN TABLES:
-----------
users
  - id (UUID, primary key)
  - phone (VARCHAR, unique, encrypted)
  - display_name (VARCHAR)
  - timezone (VARCHAR, default: America/New_York)
  - status (ENUM: LOCKED, ONBOARDING, ACTIVE, PAUSED)
  - current_state (VARCHAR)
  - state_data (JSONB)
  - created_at (TIMESTAMPTZ)
  - updated_at (TIMESTAMPTZ)
  - last_active (TIMESTAMPTZ)
  - consent_given (BOOLEAN)
  - terms_accepted (BOOLEAN)
  - age_verified (BOOLEAN)

habits
  - id (UUID, primary key)
  - user_id (UUID, foreign key)
  - name (VARCHAR)
  - created_at (TIMESTAMPTZ)
  - updated_at (TIMESTAMPTZ)

habit_logs
  - id (UUID, primary key)
  - habit_id (UUID, foreign key)
  - user_id (UUID, foreign key)
  - completed (BOOLEAN)
  - logged_date (DATE)
  - logged_at (TIMESTAMPTZ)

PAYMENT TABLES:
--------------
paid_users
  - id (SERIAL, primary key)
  - email (VARCHAR, unique)
  - phone (VARCHAR, unique)
  - access_code (VARCHAR, unique)
  - subscription_status (VARCHAR)
  - subscription_type (VARCHAR)
  - expires_at (TIMESTAMPTZ)
  - customer_id (VARCHAR)
  - created_at (TIMESTAMPTZ)
  - updated_at (TIMESTAMPTZ)

access_codes
  - id (SERIAL, primary key)
  - code (VARCHAR, unique)
  - email (VARCHAR)
  - phone (VARCHAR)
  - used (BOOLEAN)
  - created_at (TIMESTAMPTZ)
  - expires_at (TIMESTAMPTZ)

payment_transactions
  - id (SERIAL, primary key)
  - email (VARCHAR)
  - amount (DECIMAL)
  - currency (VARCHAR)
  - status (VARCHAR)
  - provider (VARCHAR)
  - transaction_id (VARCHAR)
  - metadata (JSONB)
  - created_at (TIMESTAMPTZ)

webhook_events
  - id (SERIAL, primary key)
  - provider (VARCHAR)
  - event_type (VARCHAR)
  - payload (JSONB)
  - processed (BOOLEAN)
  - created_at (TIMESTAMPTZ)

email_queue
  - id (SERIAL, primary key)
  - recipient_email (VARCHAR)
  - subject (VARCHAR)
  - template_name (VARCHAR)
  - template_data (JSONB)
  - status (VARCHAR)
  - attempts (INTEGER)
  - sent_at (TIMESTAMPTZ)
  - created_at (TIMESTAMPTZ)

COMPLIANCE TABLES:
-----------------
audit_log
  - id (UUID, primary key)
  - user_id (UUID)
  - event_type (VARCHAR)
  - event_data (JSONB)
  - created_at (TIMESTAMPTZ)

user_consents
  - id (UUID, primary key)
  - user_id (UUID)
  - consent_type (VARCHAR)
  - granted (BOOLEAN)
  - version (VARCHAR)
  - created_at (TIMESTAMPTZ)

================================================================================
5. STATE MACHINE & USER FLOW
================================================================================

USER STATUS PROGRESSION:
-----------------------
LOCKED → ONBOARDING → ACTIVE → PAUSED

STATE MACHINE STATES:
--------------------
MAIN_MENU           - Main navigation menu
AWAITING_NAME       - Collecting user's display name
AWAITING_TIMEZONE   - Setting user timezone
ONBOARDING_MENU     - Onboarding menu options
SETTING_HABIT       - Adding habits (1-5)
LOGGING_HABITS      - Daily habit check-in
VIEWING_PROGRESS    - Viewing habit progress
STATS_MENU          - Statistics menu
STATS_30_DAY        - 30-day challenge view
STATS_100_DAY       - 100-day challenge view
SETTINGS_MENU       - User settings
COMPLETION_SCREEN   - Daily completion celebration

USER JOURNEY:
------------
1. New user sends any message → LOCKED status
2. User enters access code (START HABIT-XXXXX) → Links to subscription
3. Welcome message → Name collection → Timezone setup
4. Habit setup (1-5 habits) → ONBOARDING status
5. Daily check-ins → ACTIVE status
6. Progress tracking, stats, settings → Ongoing engagement

MENU STRUCTURE:
--------------
Main Menu (ACTIVE users):
1. Log today's habits
2. View progress
3. Statistics
4. Settings
5. Help

Statistics Menu:
1. 30-day challenge
2. 100-day challenge
3. Back to menu

Settings Menu:
1-5. Edit habits
6. Change timezone

================================================================================
6. API ENDPOINTS & WEBHOOKS
================================================================================

PUBLIC ENDPOINTS:
----------------
GET  /health                    - Health check
GET  /privacy                   - Privacy policy redirect
GET  /terms                     - Terms of service redirect

WEBHOOK ENDPOINTS:
-----------------
POST /webhook/whatsapp          - Twilio WhatsApp messages
POST /webhook/fastspring        - FastSpring payment events
POST /webhook/thrivecart        - ThriveCart payment events

ADMIN ENDPOINTS (Production):
----------------------------
GET  /admin/compliance/stats    - Compliance statistics
GET  /admin/compliance/report   - Generate compliance report
POST /admin/compliance/cleanup  - Manual data retention cleanup
GET  /admin/compliance/check    - Check retention compliance

TEST ENDPOINTS (Development):
----------------------------
POST /test/webhook              - Simulate FastSpring webhook
POST /test/webhook/thrivecart   - Simulate ThriveCart webhook
GET  /test/status               - Check system status
POST /test/create-payment       - Create test payment
DELETE /test/clear-data         - Clear all test data

WEBHOOK PAYLOAD EXAMPLES:
------------------------
FastSpring Order Completed:
{
  "events": [{
    "type": "order.completed",
    "data": {
      "order": {
        "id": "ORD-123",
        "customer": {
          "email": "<EMAIL>"
        },
        "items": [{
          "product": "habit-tracker-monthly"
        }]
      }
    }
  }]
}

ThriveCart Purchase:
{
  "event": "order.success",
  "customer": {
    "email": "<EMAIL>"
  },
  "order": {
    "order_id": "123",
    "status": "SUCCESS"
  }
}

================================================================================
7. PAYMENT & SUBSCRIPTION SYSTEM
================================================================================

PAYMENT FLOW:
------------
1. User purchases on FastSpring/ThriveCart
2. Webhook received → Process payment
3. Generate unique access code (HABIT-XXXXX)
4. Send welcome email with access code
5. User enters code in WhatsApp
6. Phone number linked to subscription
7. Access granted, onboarding begins

SUBSCRIPTION TYPES:
------------------
- monthly: 30-day subscription ($9/month)
- annual: 365-day subscription ($97/year)
- lifetime: Permanent access ($297 once)

ACCESS CODE FORMAT:
------------------
HABIT-XXXXX (5 random alphanumeric characters)
Example: HABIT-A3K9Z

EMAIL TEMPLATES:
---------------
1. welcome_email - Sent after purchase with access code
2. subscription_expired - Sent when subscription expires
3. reminder_email - 3-day reminder if code unused

PAYMENT PROVIDERS:
-----------------
FastSpring:
- Webhook: /webhook/fastspring
- Events: order.completed, subscription.activated, subscription.deactivated

ThriveCart:
- Webhook: /webhook/thrivecart
- Events: order.success, order.refund

================================================================================
8. TESTING PROCEDURES
================================================================================

LOCAL TESTING SETUP:
-------------------
1. Set PAYMENT_TEST_MODE=true in .env
2. Use test phone numbers (not real WhatsApp)
3. Create test payments via /test/create-payment
4. Reset test user: Type "RESET_TEST" in WhatsApp

TESTING CHECKLIST:
-----------------
□ New user onboarding flow
□ Access code activation (START HABIT-XXXXX)
□ Habit creation (1-5 habits)
□ Daily check-in flow
□ Progress visualization
□ 30-day challenge tracking
□ 100-day challenge tracking
□ Settings modification
□ Payment webhook processing
□ Email delivery
□ Session timeout (30 minutes)
□ Rate limiting
□ STOP keyword compliance

AUTOMATED TESTS:
---------------
npm test                - Run all tests with coverage
npm run test:watch      - Run tests in watch mode

TEST SCRIPTS:
------------
node test-payment-direct.js         - Test payment flow
node test-completion-screen.js      - Test completion screen
node test-menu-states.js           - Test menu navigation
node test-with-auto-reset.js       - Test with auto-reset

WEBHOOK TESTING:
---------------
# Simulate FastSpring webhook
curl -X POST http://localhost:3000/test/webhook \
  -H "Content-Type: application/json" \
  -d '{"events":[{"type":"order.completed","data":{"order":{"customer":{"email":"<EMAIL>"}}}}]}'

# Simulate ThriveCart webhook
curl -X POST http://localhost:3000/test/webhook/thrivecart \
  -H "Content-Type: application/json" \
  -d '{"event":"order.success","customer":{"email":"<EMAIL>"}}'

================================================================================
9. DEPLOYMENT & MAINTENANCE
================================================================================

DOCKER DEPLOYMENT:
-----------------
# Build and deploy
docker-compose up -d

# View logs
docker-compose logs -f app

# Restart services
docker-compose restart

# Stop services
docker-compose down

PRODUCTION DEPLOYMENT:
---------------------
1. SSH to server: ssh root@your-server-ip
2. Navigate to project: cd /var/www/lockin
3. Pull latest code: git pull origin master
4. Install dependencies: npm install
5. Run migrations: npm run migrate
6. Restart service: pm2 restart habit-tracker

PM2 COMMANDS:
------------
pm2 start src/server.js --name habit-tracker
pm2 restart habit-tracker
pm2 stop habit-tracker
pm2 logs habit-tracker
pm2 monit

DATABASE MAINTENANCE:
--------------------
# Backup database
pg_dump habittracker > backup_$(date +%Y%m%d).sql

# Restore database
psql habittracker < backup_20250101.sql

# Connect to database
psql -U habituser -d habittracker

# Useful queries
SELECT COUNT(*) FROM users WHERE status = 'ACTIVE';
SELECT * FROM paid_users WHERE expires_at > NOW();
SELECT * FROM habit_logs WHERE logged_date = CURRENT_DATE;

MONITORING:
----------
# Check application health
curl http://localhost:3000/health

# View application logs
tail -f /var/log/habit-tracker-error.log
tail -f /var/log/habit-tracker-combined.log

# Monitor system resources
htop
df -h
free -m

SCHEDULED TASKS (Cron):
-----------------------
# Daily cleanup (4 AM)
0 4 * * * cd /var/www/lockin && node src/utils/clearTodayLogs.js

# Weekly backup (Sunday 2 AM)
0 2 * * 0 pg_dump habittracker > /backups/weekly_$(date +%Y%m%d).sql

# Monthly retention cleanup
0 3 1 * * curl -X POST http://localhost:3000/admin/compliance/cleanup

================================================================================
10. TROUBLESHOOTING GUIDE
================================================================================

COMMON ISSUES:
-------------

Issue: User stuck in wrong state
Solution: Type RESET_TEST in WhatsApp or run:
  node src/utils/resetUser.js +phonenumber

Issue: Payment webhook not processing
Check:
  - Webhook secrets in .env match provider settings
  - Check webhook_events table for raw payloads
  - Review logs: pm2 logs habit-tracker

Issue: Emails not sending
Check:
  - BREVO_API_KEY is valid
  - email_queue table for stuck emails
  - Run: node src/services/emailService.js processQueue

Issue: Database connection errors
Check:
  - PostgreSQL is running: systemctl status postgresql
  - Connection string in .env is correct
  - Database exists: psql -U postgres -c "\l"

Issue: WhatsApp messages not receiving
Check:
  - Twilio webhook URL is correct
  - Twilio credentials in .env
  - ngrok running for local testing

Issue: Session timeout too quick
Check:
  - SESSION_TIMEOUT_MS in constants.js (default: 30 min)
  - User's last_active timestamp

ERROR CODES:
-----------
RATE_LIMITED     - User sending too many messages
SESSION_TIMEOUT  - User session expired (30 min)
PAYMENT_REQUIRED - User needs valid subscription
INVALID_CODE     - Access code not found or expired
DB_ERROR         - Database connection issue

LOG LOCATIONS:
-------------
Application logs: /var/log/habit-tracker-*.log
PostgreSQL logs: /var/log/postgresql/
Nginx logs: /var/log/nginx/
Docker logs: docker-compose logs

================================================================================
11. DEVELOPER NOTES
================================================================================

CODE STRUCTURE:
--------------
/src
  /config         - Configuration files (logger, constants)
  /db             - Database connection and migrations
  /middleware     - Express middleware (auth, rate limiting)
  /models         - Data models (User, Habit, etc.)
  /routes         - API route handlers
  /services       - Business logic services
  /utils          - Utility functions
  server.js       - Main application entry

KEY FILES:
---------
src/services/stateMachine.js         - Core bot logic and state handling
src/services/stateMachineCompliant.js - Payment-enforced version
src/services/paymentService.js       - Payment processing
src/services/subscriptionService.js  - Subscription management
src/services/emailService.js         - Email queue and sending
src/services/twilioService.js        - WhatsApp messaging
src/models/User.js                   - User data model
src/config/constants.js              - System constants

DEVELOPMENT WORKFLOW:
--------------------
1. Create feature branch: git checkout -b feature/name
2. Make changes and test locally
3. Run tests: npm test
4. Commit changes: git commit -m "Description"
5. Push branch: git push origin feature/name
6. Create pull request for review
7. Merge to master after approval
8. Deploy to production

BEST PRACTICES:
--------------
- Always sanitize user input
- Use parameterized queries (no SQL injection)
- Log errors but not sensitive data (PII)
- Test webhook handlers with curl first
- Keep sessions under 30 minutes
- Encrypt sensitive data in database
- Follow WhatsApp Business API guidelines
- Maintain GDPR compliance
- Regular database backups
- Monitor rate limits

SECURITY NOTES:
--------------
- Phone numbers are encrypted in database
- No passwords stored (phone-based auth)
- Rate limiting on all endpoints
- Input validation on all user inputs
- XSS protection via helmet
- SQL injection prevention
- Session timeout enforcement
- Audit logging for compliance

PERFORMANCE TIPS:
----------------
- Use database indexes on frequently queried columns
- Batch process email queue
- Cache user data in Redis (future)
- Use connection pooling for PostgreSQL
- Implement pagination for large datasets
- Monitor slow queries
- Regular database maintenance (VACUUM)

FUTURE ENHANCEMENTS:
-------------------
- Push notifications
- Multi-language support
- Advanced analytics dashboard
- Habit streak gamification
- Social sharing features
- AI-powered habit recommendations
- Voice message support
- Rich media responses

================================================================================
                               END OF DOCUMENTATION
================================================================================

For questions or support:
- GitHub Issues: [repository-url]/issues
- Email: <EMAIL>
- Documentation updates: Please submit PRs

Last updated by: Claude Assistant
Date: January 2025
Version: 1.0.0