#!/usr/bin/env node

require('dotenv').config();
const nodemailer = require('nodemailer');

// Monkey patch nodemailer to intercept all emails
const originalCreateTransport = nodemailer.createTransport;

nodemailer.createTransport = function(options) {
  const transporter = originalCreateTransport.call(this, options);
  const originalSendMail = transporter.sendMail;
  
  transporter.sendMail = async function(mailOptions) {
    console.log('\n🚨 INTERCEPTED EMAIL BEING SENT:');
    console.log('To:', mailOptions.to);
    console.log('Subject:', mailOptions.subject);
    console.log('HTML length:', mailOptions.html?.length || 0);
    console.log('Text length:', mailOptions.text?.length || 0);
    
    // Check if HTML contains step 5
    if (mailOptions.html) {
      const step5Index = mailOptions.html.indexOf('Start building unstoppable habits!');
      if (step5Index !== -1) {
        const afterStep5 = mailOptions.html.substring(step5Index + 'Start building unstoppable habits!'.length);
        console.log('Characters after step 5:', afterStep5.length);
        
        // Check footer content in the actual email being sent
        const footerChecks = [
          { name: 'Rich signature', check: afterStep5.includes("Let's Lock In") && afterStep5.includes('Rich') },
          { name: 'Affiliate section', check: afterStep5.includes('💰 Earn with Lock In') },
          { name: 'Social links', check: afterStep5.includes('@richvieren') },
          { name: 'Copyright', check: afterStep5.includes('© 2025 Lock In') }
        ];
        
        console.log('🔍 FOOTER CHECK IN ACTUAL SMTP:');
        footerChecks.forEach(({ name, check }) => {
          console.log(`  ${check ? '✅' : '❌'} ${name}`);
        });
        
        const allFooterGood = footerChecks.every(({ check }) => check);
        console.log(`${allFooterGood ? '🎉 SMTP EMAIL HAS COMPLETE FOOTER!' : '🚨 SMTP EMAIL IS TRUNCATED!'}`);
        
        if (!allFooterGood) {
          console.log('\n🚨 FOUND THE BUG! Email is truncated at SMTP level!');
          
          // Show exactly where it's cut off
          const lastChars = mailOptions.html.substring(mailOptions.html.length - 200);
          console.log('Last 200 chars of HTML:');
          console.log('"' + lastChars + '"');
        }
      }
      
      // Save the actual SMTP content
      const timestamp = Date.now();
      require('fs').writeFileSync(`/var/www/lockin/intercepted-smtp-${timestamp}.html`, mailOptions.html);
      console.log(`💾 Saved intercepted content: intercepted-smtp-${timestamp}.html`);
    }
    
    console.log('📧 Proceeding with actual email send...');
    return originalSendMail.call(this, mailOptions);
  };
  
  return transporter;
};

async function testWithInterception() {
  try {
    console.log('🎯 STARTING EMAIL INTERCEPTION TEST...');
    
    // Now simulate a purchase that will trigger the real production flow
    const thrivecartController = require('./src/controllers/thrivecartController');
    
    const webhookPayload = {
      thrivecart_account: process.env.THRIVECART_ACCOUNT || 'richvieren',
      event: 'order.success',
      event_id: `INTERCEPT_${Date.now()}`,
      
      customer_email: '<EMAIL>',
      customer_name: 'Intercept Test',
      customer_first_name: 'Intercept',
      customer_last_name: 'Test',
      
      product_name: 'LOCK IN - Annual - Whatsapp Habit Tracker',
      order_total: '3999',
      payment_plan_name: 'Annual subscription (ongoing) ($39.99)',
      
      webhook_charges: [{
        name: 'LOCK IN - Annual - Whatsapp Habit Tracker',
        amount: '3999',
        payment_plan_name: 'Annual subscription (ongoing) ($39.99)'
      }],
      
      currency: 'USD',
      thrivecart_secret: 'FUQ2A97V0Q8A'
    };
    
    const req = { body: webhookPayload };
    const res = {
      status: (code) => ({
        json: (data) => {
          console.log(`Response ${code}:`, JSON.stringify(data, null, 2));
          return res;
        }
      })
    };
    
    console.log('⚡ PROCESSING WEBHOOK WITH SMTP INTERCEPTION...');
    
    // This will trigger the email service which is now patched to log content
    await thrivecartController.handleWebhook(req, res);
    
    console.log('\n✅ INTERCEPTION TEST COMPLETE!');
    console.log('Check the logs above to see the exact content being sent via SMTP.');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
  }
}

testWithInterception().catch(console.error);