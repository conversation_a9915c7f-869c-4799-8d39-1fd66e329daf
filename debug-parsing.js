#!/usr/bin/env node

// Test the parsing logic directly

function testParsing() {
  const input = '1y 2n 3y';
  console.log(`Testing input: "${input}"`);
  
  // Check for "1y 2n 3y" format first
  const ynMatches = input.match(/(\d+)([yn])/g);
  console.log('ynMatches:', ynMatches);
  
  if (ynMatches && ynMatches.length > 0) {
    console.log('Processing "1y 2n 3y" format');
    const logsToCreate = [];
    
    ynMatches.forEach(match => {
      console.log('Processing match:', match);
      const [, num, yn] = match.match(/(\d+)([yn])/);
      const habitNumber = parseInt(num);
      console.log(`  habitNumber: ${habitNumber}, yn: ${yn}, completed: ${yn === 'y'}`);
      
      if (habitNumber >= 1 && habitNumber <= 5) {
        logsToCreate.push({
          habitNumber,
          completed: yn === 'y'
        });
      }
    });
    
    console.log('Final logsToCreate:', logsToCreate);
  } else {
    console.log('No ynMatches found, would fall back to numbers-only format');
  }
}

testParsing();