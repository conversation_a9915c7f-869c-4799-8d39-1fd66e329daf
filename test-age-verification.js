const { Pool } = require('pg');
const complianceService = require('./src/services/complianceService');
const logger = require('./src/config/logger');

// Create pool with explicit credentials
const pool = new Pool({
  host: 'localhost',
  database: 'lockin',
  user: 'postgres',
  password: 'postgres',
  port: 5432
});

async function testAgeVerification() {
  const client = await pool.connect();
  
  try {
    console.log('\n=== TESTING AGE VERIFICATION FLOW ===\n');
    
    // Create a test user in AGE_VERIFICATION state
    const testPhone = '+15559999' + Math.floor(Math.random() * 1000);
    console.log(`Creating test user with phone: ${testPhone}`);
    
    const userResult = await client.query(
      `INSERT INTO users (phone, display_name, current_state, status, created_at)
       VALUES ($1, $2, 'AGE_VERIFICATION', 'ACTIVE', NOW())
       RETURNING *`,
      [testPhone, 'Test User']
    );
    
    const testUser = userResult.rows[0];
    console.log(`Created user ID: ${testUser.id}, State: ${testUser.current_state}\n`);
    
    // Test 1: Start age verification flow
    console.log('Test 1: Starting age verification flow...');
    const startResponse = await complianceService.startComplianceOnboarding(testUser);
    console.log('Initial message:', startResponse.message);
    console.log('State:', startResponse.newState);
    console.log('---\n');
    
    // Test 2: Invalid age input (non-numeric)
    console.log('Test 2: Testing invalid input (non-numeric)...');
    const invalidResponse = await complianceService.handleAgeVerification(testUser, 'twenty five');
    console.log('Response:', invalidResponse.message);
    console.log('State remains:', invalidResponse.newState);
    console.log('---\n');
    
    // Test 3: Under 18 age
    console.log('Test 3: Testing under 18 (age 16)...');
    const minorResponse = await complianceService.handleAgeVerification(testUser, '16');
    console.log('Response:', minorResponse.message);
    console.log('State:', minorResponse.newState);
    console.log('---\n');
    
    // Test 4: Valid age (25)
    console.log('Test 4: Testing valid age (25)...');
    const validResponse = await complianceService.handleAgeVerification(testUser, '25');
    console.log('Response:', validResponse.message);
    console.log('New state should be:', validResponse.newState);
    
    // Check if database was updated correctly
    const updatedUserResult = await client.query(
      'SELECT current_state, age_verified FROM users WHERE id = $1',
      [testUser.id]
    );
    const updatedUser = updatedUserResult.rows[0];
    console.log('\nDatabase check:');
    console.log('- Current state in DB:', updatedUser.current_state);
    console.log('- Age verified in DB:', updatedUser.age_verified);
    console.log('---\n');
    
    // Test 5: Verify state transition works
    console.log('Test 5: Testing that flow continues to privacy consent...');
    const privacyResponse = await complianceService.handlePrivacyConsent(updatedUser, 'yes');
    console.log('Privacy consent response received');
    console.log('Next state:', privacyResponse.newState);
    
    // Clean up test user
    await client.query('DELETE FROM users WHERE id = $1', [testUser.id]);
    console.log('\n✅ Test completed successfully! All flows work correctly.\n');
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

testAgeVerification();