const { Pool } = require('pg');
const subscriptionService = require('./src/services/subscriptionService');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/lockin'
});

async function testSubscriptionValidation() {
  console.log('=== SUBSCRIPTION VALIDATION TEST ===\n');
  
  try {
    // Check all test users
    const result = await pool.query(
      `SELECT email, subscription_type, expires_at, 
              billing_frequency_days, amount_paid, status
       FROM paid_users 
       WHERE email LIKE 'test-%' 
       ORDER BY subscription_type`
    );
    
    console.log('1. DATABASE VALIDATION:');
    console.log('------------------------');
    for (const user of result.rows) {
      const daysUntilExpiry = Math.floor((new Date(user.expires_at) - new Date()) / (1000 * 60 * 60 * 24));
      
      console.log(`\n${user.subscription_type.toUpperCase()} Subscription:`);
      console.log(`  Email: ${user.email}`);
      console.log(`  Amount Paid: $${user.amount_paid}`);
      console.log(`  Billing Frequency: ${user.billing_frequency_days} days`);
      console.log(`  Expires: ${user.expires_at ? new Date(user.expires_at).toLocaleDateString() : 'N/A'}`);
      console.log(`  Days Until Expiry: ${daysUntilExpiry}`);
      console.log(`  Status: ${user.status}`);
      
      // Validate billing frequency matches subscription type
      let expectedFrequency;
      if (user.subscription_type === 'weekly') expectedFrequency = 7;
      else if (user.subscription_type === 'monthly') expectedFrequency = 30;
      else if (user.subscription_type === 'yearly') expectedFrequency = 365;
      
      if (user.billing_frequency_days === expectedFrequency) {
        console.log(`  ✓ Billing frequency correct`);
      } else {
        console.log(`  ✗ Billing frequency incorrect (expected ${expectedFrequency})`);
      }
    }
    
    console.log('\n2. ACCESS VALIDATION:');
    console.log('----------------------');
    
    // Test access for each user
    for (const user of result.rows) {
      const accessStatus = await subscriptionService.checkSubscriptionStatus(user.email);
      console.log(`\n${user.email}:`);
      console.log(`  Has Access: ${accessStatus.hasAccess ? '✓' : '✗'}`);
      console.log(`  Status: ${accessStatus.status}`);
      
      if (accessStatus.status === 'expired') {
        console.log(`  Message: ${accessStatus.message}`);
      }
    }
    
    console.log('\n3. RENEWAL URL VALIDATION:');
    console.log('---------------------------');
    
    const renewalUrls = {
      weekly: 'https://aeon.thrivecart.com/weekly-subscription',
      monthly: 'https://aeon.thrivecart.com/monthly-subscription',
      yearly: 'https://aeon.thrivecart.com/annual-subscription'
    };
    
    for (const user of result.rows) {
      const renewalMsg = subscriptionService.createRenewalMessage(user);
      const expectedUrl = renewalUrls[user.subscription_type];
      
      console.log(`\n${user.subscription_type.toUpperCase()}:`);
      console.log(`  Generated URL: ${renewalMsg.renewalUrl}`);
      console.log(`  Expected URL: ${expectedUrl}`);
      console.log(`  ${renewalMsg.renewalUrl === expectedUrl ? '✓ URL Correct' : '✗ URL Incorrect'}`);
    }
    
    console.log('\n=== TEST COMPLETE ===\n');
    
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    await pool.end();
    process.exit(0);
  }
}

testSubscriptionValidation();