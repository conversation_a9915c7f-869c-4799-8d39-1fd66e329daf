# 🚀 FastSpring Payment Integration - Complete Setup Guide

## ✅ Implementation Complete

The FastSpring payment integration for the WhatsApp Habit Tracker bot has been successfully implemented with all requested features.

## 🎯 Features Implemented

### Core Payment System
- ✅ FastSpring webhook endpoint (`/webhook/fastspring`)
- ✅ Automatic access code generation (format: `HABIT-XXXXXX`)
- ✅ Monthly ($5/month) and Yearly ($30/year) subscription support
- ✅ Email notification system with welcome templates
- ✅ Bot access verification via `START HABIT-XXXXXX` command
- ✅ Payment status tracking and subscription management

### Affiliate System
- ✅ Automatic affiliate enrollment for yearly subscribers
- ✅ Unique affiliate codes (format: `AFF-XXXXXX`)
- ✅ 30% commission tracking
- ✅ Referral tracking in database
- ✅ Affiliate performance views

### Test Mode
- ✅ Full test mode for development without real payments
- ✅ Test webhook simulator
- ✅ Test data creation and cleanup endpoints
- ✅ Status monitoring endpoint

## 📦 Database Tables Created

- `paid_users` - Subscription and customer data
- `access_codes` - Redeemable access codes
- `payment_transactions` - Transaction history
- `webhook_events` - Webhook audit log
- `email_queue` - Email notification queue
- `affiliate_referrals` - Affiliate commission tracking
- `affiliate_payouts` - Payout management

## 🔧 Configuration

### 1. Environment Variables

Add to your `.env` file:

```bash
# FastSpring Configuration
PAYMENT_TEST_MODE=true  # Set to false for production
FASTSPRING_WEBHOOK_SECRET=your_webhook_secret_here
FASTSPRING_CHECKOUT_URL=https://your-store.fastspring.com/checkout

# Email Configuration (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
EMAIL_FROM=<EMAIL>
```

### 2. FastSpring Setup

1. **Create Products in FastSpring:**
   - Monthly subscription: $5/month
   - Yearly subscription: $30/year

2. **Configure Webhook:**
   - URL: `https://yourdomain.com:3001/webhook/fastspring`
   - Enable events: order.completed, subscription.activated, subscription.deactivated, etc.
   - Copy the webhook secret to your `.env`

3. **Customize Checkout:**
   - Add your branding
   - Configure payment methods
   - Set up tax handling

## 🧪 Testing

### Test Mode Endpoints (when `PAYMENT_TEST_MODE=true`)

```bash
# Check system status
curl http://localhost:3001/test/status

# Create test payment
curl -X POST http://localhost:3001/test/create-payment \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "subscriptionType": "yearly"}'

# Clear test data
curl -X DELETE http://localhost:3001/test/clear-data

# Simulate webhook
curl -X POST http://localhost:3001/test/webhook \
  -H "Content-Type: application/json" \
  -d '{"eventType": "order.completed", "email": "<EMAIL>"}'
```

### Run Complete Test Flow

```bash
node test-payment-flow.js
```

## 📱 User Flow

### New User Journey

1. **User texts bot** → Receives paywall message with checkout link
2. **User purchases** → FastSpring processes payment
3. **Webhook received** → System creates access code and sends email
4. **User receives email** → Contains access code and instructions
5. **User texts:** `START HABIT-ABC123`
6. **Bot activates** → User can now track habits

### Existing User with Payment

- User texts bot → Automatically granted access
- No need to re-enter access code

## 🔄 Webhook Events Handled

- `order.completed` - New purchase
- `subscription.activated` - Subscription active
- `subscription.deactivated` - Cancellation/expiry
- `subscription.charge.completed` - Renewal payment
- `subscription.charge.failed` - Failed payment
- `return.created` - Refund processed

## 📊 Database Queries

### Check Active Subscriptions
```sql
SELECT * FROM active_subscriptions;
```

### View Affiliate Performance
```sql
SELECT * FROM affiliate_performance;
```

### Check Payment Status
```sql
SELECT email, access_code, subscription_type, status 
FROM paid_users 
WHERE status = 'active';
```

### Monitor Email Queue
```sql
SELECT * FROM email_queue 
WHERE status = 'pending';
```

## 🚨 Production Checklist

- [ ] Set `PAYMENT_TEST_MODE=false` in `.env`
- [ ] Configure real FastSpring webhook secret
- [ ] Set up SMTP for email notifications
- [ ] Update checkout URL in environment
- [ ] Test webhook connectivity
- [ ] Monitor initial transactions
- [ ] Set up backup and monitoring

## 📝 Important Notes

1. **Access codes are single-use** - Once activated, they're linked to a phone number
2. **Yearly subscribers automatically become affiliates** with 30% commission
3. **Email service is optional** - System logs emails if SMTP not configured
4. **Test mode allows existing users** to access bot without payment
5. **Subscriptions auto-expire** if not renewed

## 🛠️ Troubleshooting

### Webhook Not Working
- Verify webhook secret in `.env`
- Check FastSpring webhook URL configuration
- Review webhook_events table for errors

### Emails Not Sending
- Configure SMTP credentials in `.env`
- Check email_queue table for failed emails
- Verify EMAIL_FROM address is valid

### Access Code Issues
- Ensure exact format: `START HABIT-XXXXXX`
- Check access_codes table for code status
- Verify code hasn't been used already

## 📞 Support Commands

```bash
# View server logs
pm2 logs lockin

# Check database status
PGPASSWORD=postgres psql -U postgres -h localhost -d lockin -c "SELECT COUNT(*) FROM paid_users"

# Monitor webhook events
PGPASSWORD=postgres psql -U postgres -h localhost -d lockin -c "SELECT * FROM webhook_events ORDER BY created_at DESC LIMIT 10"
```

## ✨ Next Steps

1. **Configure FastSpring account** with your products
2. **Set up production webhook** with HTTPS endpoint
3. **Customize email templates** in `src/services/emailService.js`
4. **Test complete payment flow** in production
5. **Monitor first real transactions** closely

---

**Status:** ✅ Complete and Ready for Production
**Test Coverage:** Full payment flow tested successfully
**Server:** Running on *************:3001
**Bot Number:** +***********