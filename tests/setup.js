// Test setup file
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test';
process.env.TWILIO_ACCOUNT_SID = 'test_account_sid';
process.env.TWILIO_AUTH_TOKEN = 'test_auth_token';
process.env.TWILIO_PHONE_NUMBER = '+**********';

// Mock logger to reduce noise in tests
jest.mock('../src/config/logger', () => ({
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn()
}));

// Clear test user logs before each test suite
// This ensures we always start with a clean slate (all habits showing ⚠️)
beforeAll(async () => {
  if (process.env.NODE_ENV === 'development') {
    const { clearTestUserLogs } = require('./helpers/clearTestUserLogs');
    await clearTestUserLogs('+***********'); // Default test user
  }
});