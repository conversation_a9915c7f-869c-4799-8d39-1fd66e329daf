const stateMachine = require('../../src/services/stateMachine');
const User = require('../../src/models/User');
const Habit = require('../../src/models/Habit');
const AuditLog = require('../../src/models/AuditLog');

// Mock dependencies
jest.mock('../../src/models/User');
jest.mock('../../src/models/Habit');
jest.mock('../../src/models/AuditLog');

describe('StateMachine', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('processMessage', () => {
    it('should update last active and process message', async () => {
      const mockUser = {
        id: 1,
        phone: '+1234567890',
        status: 'LOCKED',
        current_state: 'MAIN_MENU'
      };

      User.updateLastActive.mockResolvedValue();

      const response = await stateMachine.processMessage(mockUser, '1');

      expect(User.updateLastActive).toHaveBeenCalledWith(1);
      expect(response).toHaveProperty('message');
      expect(response).toHaveProperty('newState');
    });

    it('should handle errors gracefully', async () => {
      const mockUser = {
        id: 1,
        current_state: 'MAIN_MENU'
      };

      User.updateLastActive.mockRejectedValue(new Error('Database error'));
      AuditLog.log.mockResolvedValue();

      const response = await stateMachine.processMessage(mockUser, 'test');

      expect(response.message).toContain('Sorry, something went wrong');
      expect(AuditLog.log).toHaveBeenCalled();
    });
  });

  describe('handleMainMenu', () => {
    it('should show unlock prompt for locked users', async () => {
      const mockUser = {
        id: 1,
        status: 'LOCKED',
        current_state: 'MAIN_MENU'
      };

      const response = await stateMachine.handleMainMenu(mockUser, 'test');

      expect(response.message).toContain('LOCKED');
      expect(response.message).toContain('access code');
    });

    it('should transition to access code state when user types 1', async () => {
      const mockUser = {
        id: 1,
        status: 'LOCKED'
      };

      User.updateState.mockResolvedValue();

      const response = await stateMachine.handleMainMenu(mockUser, '1');

      expect(User.updateState).toHaveBeenCalledWith(1, 'AWAITING_ACCESS_CODE');
      expect(response.message).toContain('enter your access code');
    });

    it('should show main menu for active users', async () => {
      const mockUser = {
        id: 1,
        status: 'ACTIVE',
        display_name: 'John',
        timezone: 'America/New_York'
      };

      Habit.findByUserId.mockResolvedValue([
        { habit_number: 1, habit_name: 'Exercise' },
        { habit_number: 2, habit_name: 'Read' }
      ]);

      Habit.getTodayLogs.mockResolvedValue([
        { habit_number: 1, habit_name: 'Exercise', completed: null },
        { habit_number: 2, habit_name: 'Read', completed: null }
      ]);

      const response = await stateMachine.handleMainMenu(mockUser, '');

      expect(response.message).toContain('Hi John!');
      expect(response.message).toContain('Exercise');
      expect(response.message).toContain('Read');
      expect(response.message).toContain('Log today\'s habits');
      expect(response.message).toContain('Make a selection (reply with a number)');
    });

    it('should show "Complete today\'s habits" for partially logged', async () => {
      const mockUser = {
        id: 1,
        status: 'ACTIVE',
        display_name: 'John',
        timezone: 'America/New_York'
      };

      Habit.findByUserId.mockResolvedValue([
        { habit_number: 1, habit_name: 'Exercise' },
        { habit_number: 2, habit_name: 'Read' },
        { habit_number: 3, habit_name: 'Meditate' }
      ]);

      Habit.getTodayLogs.mockResolvedValue([
        { habit_number: 1, habit_name: 'Exercise', completed: true },
        { habit_number: 2, habit_name: 'Read', completed: null },
        { habit_number: 3, habit_name: 'Meditate', completed: false }
      ]);

      const response = await stateMachine.handleMainMenu(mockUser, '');

      expect(response.message).toContain('Complete today\'s habits');
      expect(response.message).toContain('✅1. Exercise');
      expect(response.message).toContain('⚠️2. Read');
      expect(response.message).toContain('❌3. Meditate');
    });

    it('should show "Edit today\'s habits" for fully logged', async () => {
      const mockUser = {
        id: 1,
        status: 'ACTIVE',
        display_name: 'John',
        timezone: 'America/New_York'
      };

      Habit.findByUserId.mockResolvedValue([
        { habit_number: 1, habit_name: 'Exercise' },
        { habit_number: 2, habit_name: 'Read' }
      ]);

      Habit.getTodayLogs.mockResolvedValue([
        { habit_number: 1, habit_name: 'Exercise', completed: true },
        { habit_number: 2, habit_name: 'Read', completed: false }
      ]);

      const response = await stateMachine.handleMainMenu(mockUser, '');

      expect(response.message).toContain('Edit today\'s habits');
      expect(response.message).toContain('✅1. Exercise');
      expect(response.message).toContain('❌2. Read');
    });
  });

  describe('handleAccessCode', () => {
    it('should unlock user with valid code', async () => {
      const mockUser = { id: 1 };

      User.unlockWithCode.mockResolvedValue({
        success: true,
        user: { id: 1, is_unlocked: true }
      });
      User.updateState.mockResolvedValue();
      AuditLog.log.mockResolvedValue();

      const response = await stateMachine.handleAccessCode(mockUser, 'TEST123');

      expect(User.unlockWithCode).toHaveBeenCalledWith(1, 'TEST123');
      expect(response.message).toContain('Access granted');
      expect(AuditLog.log).toHaveBeenCalled();
    });

    it('should reject invalid code', async () => {
      const mockUser = { id: 1 };

      User.unlockWithCode.mockResolvedValue({
        success: false,
        message: 'Invalid or expired access code'
      });

      const response = await stateMachine.handleAccessCode(mockUser, 'INVALID');

      expect(response.message).toContain('Invalid or expired');
    });
  });

  describe('handleLoggingHabits', () => {
    it('should log completed habits', async () => {
      const mockUser = {
        id: 1,
        timezone: 'UTC'
      };

      Habit.findByUserId.mockResolvedValue([
        { id: 1, habit_number: 1, habit_name: 'Exercise' },
        { id: 2, habit_number: 2, habit_name: 'Read' },
        { id: 3, habit_number: 3, habit_name: 'Meditate' }
      ]);

      Habit.logHabit.mockResolvedValue();
      User.updateState.mockResolvedValue();
      AuditLog.log.mockResolvedValue();

      const response = await stateMachine.handleLoggingHabits(mockUser, '1,3');

      expect(Habit.logHabit).toHaveBeenCalledTimes(3);
      expect(response.message).toContain('Habits logged');
      expect(response.message).toContain('1, 3');
    });

    it('should show today habits when no input provided', async () => {
      const mockUser = {
        id: 1,
        timezone: 'UTC'
      };

      Habit.getTodayLogs.mockResolvedValue([
        { habit_number: 1, habit_name: 'Exercise', completed: false },
        { habit_number: 2, habit_name: 'Read', completed: true }
      ]);

      const response = await stateMachine.showTodayHabits(mockUser);

      expect(response.message).toContain("Today's Habits");
      expect(response.message).toContain('⬜ 1. Exercise');
      expect(response.message).toContain('✅ 2. Read');
    });
  });

  describe('showProgress', () => {
    it('should display progress bars for habits', async () => {
      const mockUser = {
        id: 1,
        timezone: 'UTC'
      };

      Habit.getProgress.mockResolvedValue([
        {
          habit_number: 1,
          habit_name: 'Exercise',
          completed_days: 5,
          total_days: 7,
          completion_rate: 71
        },
        {
          habit_number: 2,
          habit_name: 'Read',
          completed_days: 7,
          total_days: 7,
          completion_rate: 100
        }
      ]);

      const response = await stateMachine.showProgress(mockUser);

      expect(response.message).toContain('7-Day Progress');
      expect(response.message).toContain('Exercise');
      expect(response.message).toContain('71%');
      expect(response.message).toContain('Read');
      expect(response.message).toContain('100%');
    });
  });

  describe('createProgressBar', () => {
    it('should create correct progress bar', () => {
      expect(stateMachine.createProgressBar(0)).toBe('░░░░░░░░░░');
      expect(stateMachine.createProgressBar(50)).toBe('▓▓▓▓▓░░░░░');
      expect(stateMachine.createProgressBar(100)).toBe('▓▓▓▓▓▓▓▓▓▓');
    });
  });
});