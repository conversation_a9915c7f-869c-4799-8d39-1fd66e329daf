#!/usr/bin/env node

require('dotenv').config();
const stateMachine = require('./src/services/stateMachine');
const User = require('./src/models/User');
const pool = require('./src/db/connection');

async function testLoggingFixes() {
  try {
    const testPhone = '+27646921984';
    
    console.log('🧪 TESTING LOGGING FIXES FOR "1y 2n 3y"');
    console.log('='.repeat(60));
    
    // Get user
    const user = await User.findByPhone(testPhone);
    
    // Test 1: Show blank slate main menu
    console.log('\n📋 STEP 1: Blank slate main menu');
    console.log('-'.repeat(40));
    const response1 = await stateMachine.handleMainMenu(user, '');
    console.log(response1.message);
    
    // Test 2: Enter logging mode
    console.log('\n📋 STEP 2: Enter logging mode (select "1")');
    console.log('-'.repeat(40));
    const response2 = await stateMachine.processMessage(user, '1');
    console.log(response2.message);
    
    // Test 3: Input "1y 2n 3y" 
    console.log('\n📋 STEP 3: Input "1y 2n 3y" (should log habit 1=✅, 2=❌, 3=✅)');
    console.log('-'.repeat(40));
    const response3 = await stateMachine.processMessage(user, '1y 2n 3y');
    console.log(response3.message);
    
    // Test 4: Verify database state
    console.log('\n📋 STEP 4: Database verification');
    console.log('-'.repeat(40));
    const client = await pool.connect();
    const result = await client.query(`
      SELECT h.habit_number, h.habit_name, hl.completed
      FROM habits h
      LEFT JOIN habit_logs hl ON h.id = hl.habit_id AND hl.log_date = CURRENT_DATE
      WHERE h.user_id = (SELECT id FROM users WHERE phone = $1)
      ORDER BY h.habit_number
    `, [testPhone]);
    
    result.rows.forEach(row => {
      const status = row.completed === null ? '⚠️ (NULL)' : 
                     row.completed === true ? '✅ (TRUE)' : '❌ (FALSE)';
      console.log(`   ${row.habit_number}. ${row.habit_name}: ${status}`);
    });
    
    client.release();
    
    console.log('\n✅ Test complete!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await pool.end();
  }
}

testLoggingFixes().catch(console.error);