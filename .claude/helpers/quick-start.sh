#!/bin/bash
# Quick start guide for Claude Flow

echo "🚀 Claude Flow Quick Start"
echo "=========================="
echo ""
echo "1. Initialize a swarm:"
echo "   npx claude-flow swarm init --topology hierarchical"
echo ""
echo "2. Spawn agents:"
echo "   npx claude-flow agent spawn --type coder --name "API Developer""
echo ""
echo "3. Orchestrate tasks:"
echo "   npx claude-flow task orchestrate --task "Build REST API""
echo ""
echo "4. Monitor progress:"
echo "   npx claude-flow swarm monitor"
echo ""
echo "📚 For more examples, see .claude/commands/"
