# hive-mind

Hive Mind collective intelligence system for advanced swarm coordination.

## Usage
```bash
npx claude-flow hive-mind [subcommand] [options]
```

## Subcommands
- `init` - Initialize hive mind system
- `spawn` - Spawn hive mind swarm
- `status` - Show hive mind status
- `resume` - Resume paused session
- `stop` - Stop running session

## Examples
```bash
# Initialize hive mind
npx claude-flow hive-mind init

# Spawn swarm
npx claude-flow hive-mind spawn "Build microservices"

# Check status
npx claude-flow hive-mind status
```
