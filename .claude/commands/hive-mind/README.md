# Hive-mind Commands

Commands for hive-mind operations in Claude Flow.

## Available Commands

- [hive-mind](./hive-mind.md)
- [hive-mind-init](./hive-mind-init.md)
- [hive-mind-spawn](./hive-mind-spawn.md)
- [hive-mind-status](./hive-mind-status.md)
- [hive-mind-resume](./hive-mind-resume.md)
- [hive-mind-stop](./hive-mind-stop.md)
- [hive-mind-sessions](./hive-mind-sessions.md)
- [hive-mind-consensus](./hive-mind-consensus.md)
- [hive-mind-memory](./hive-mind-memory.md)
- [hive-mind-metrics](./hive-mind-metrics.md)
- [hive-mind-wizard](./hive-mind-wizard.md)
