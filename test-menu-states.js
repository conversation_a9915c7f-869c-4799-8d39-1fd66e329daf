#!/usr/bin/env node

require('dotenv').config();
const stateMachine = require('./src/services/stateMachine');
const User = require('./src/models/User');
const Habit = require('./src/models/Habit');
const pool = require('./src/db/connection');

async function showMenuState(testPhone, description) {
  console.log(`\n📋 ${description}`);
  console.log('='.repeat(60));
  
  const user = await User.findByPhone(testPhone);
  const response = await stateMachine.handleMainMenu(user, '');
  console.log(response.message);
  console.log('='.repeat(60));
  
  // Show database state
  const todayLogs = await Habit.getTodayLogs(user.id, user.timezone);
  console.log('\n🗄️  DATABASE STATE:');
  todayLogs.forEach(log => {
    const status = log.completed === null ? 'NULL (⚠️)' : 
                   log.completed === true ? 'TRUE (✅)' : 'FALSE (❌)';
    console.log(`   Habit ${log.habit_number}: completed = ${status}`);
  });
}

async function test() {
  try {
    const testPhone = '+27646921984';
    
    // Reset user
    console.log('🔄 Resetting user to clean slate...');
    const client = await pool.connect();
    await client.query(
      'DELETE FROM habit_logs WHERE user_id = (SELECT id FROM users WHERE phone = $1) AND log_date = CURRENT_DATE',
      [testPhone]
    );
    await client.query(
      'UPDATE users SET current_state = $1, session_context = $2 WHERE phone = $3',
      ['MAIN_MENU', '{}', testPhone]
    );
    client.release();
    
    // TEST 1: Blank slate
    await showMenuState(testPhone, 'TEST 1: BLANK SLATE (no logs in database)');
    
    // Add partial logs directly to database
    console.log('\n➕ Adding logs to database: Habit 2 = TRUE, Habit 3 = TRUE');
    const user = await User.findByPhone(testPhone);
    const habits = await Habit.findByUserId(user.id);
    const today = new Date().toISOString().split('T')[0];
    
    for (const habit of habits) {
      if (habit.habit_number === 2 || habit.habit_number === 3) {
        await Habit.logHabit(user.id, habit.id, today, true);
      }
    }
    
    // TEST 2: Partially logged
    await showMenuState(testPhone, 'TEST 2: PARTIALLY LOGGED (2 of 5 logged)');
    
    // Complete logging
    console.log('\n➕ Adding remaining logs: Habit 1 = FALSE, Habit 4 = FALSE, Habit 5 = TRUE');
    for (const habit of habits) {
      if (habit.habit_number === 1 || habit.habit_number === 4) {
        await Habit.logHabit(user.id, habit.id, today, false);
      } else if (habit.habit_number === 5) {
        await Habit.logHabit(user.id, habit.id, today, true);
      }
    }
    
    // TEST 3: Fully logged
    await showMenuState(testPhone, 'TEST 3: FULLY LOGGED (all 5 have log entries)');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await pool.end();
  }
}

test().catch(console.error);