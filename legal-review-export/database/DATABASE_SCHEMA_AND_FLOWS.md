# D<PERSON><PERSON><PERSON><PERSON> SCHEMA AND DATA FLOW DOCUMENTATION

## Database Overview

**Database System:** PostgreSQL  
**Location:** Self-hosted on Linux server  
**Access:** Local application only (no external access)  
**Backup Strategy:** [TO BE DOCUMENTED BY OPS TEAM]  

## Core Database Tables

### 1. USERS TABLE
Primary table for user account information.

```sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  phone VARCHAR(20) UNIQUE NOT NULL,      -- WhatsApp phone number
  display_name VARCHAR(50),                -- Optional user nickname
  status VARCHAR(20) DEFAULT 'LOCKED',     -- Account status
  timezone VARCHAR(50) DEFAULT 'UTC',      -- User timezone
  created_at TIMESTAMPTZ DEFAULT NOW(),    -- Account creation
  is_unlocked BOOLEAN DEFAULT FALSE,       -- Payment status
  current_state VARCHAR(50),               -- Bot conversation state
  last_active TIMESTAMPTZ                  -- Last interaction time
);
```

**Data Collected:**
- Phone numbers (PII - required for WhatsApp)
- Display names (PII - optional)
- Usage timestamps
- Account status

### 2. HABITS TABLE
Stores user-defined habits for tracking.

```sql
CREATE TABLE habits (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id),
  habit_number INTEGER (1-5),
  habit_name VARCHAR(100),
  created_at TIMESTAMPTZ
);
```

**Data Collected:**
- User-generated habit descriptions
- Creation timestamps
- Limited to 5 habits per user

### 3. HABIT_LOGS TABLE
Daily tracking of habit completion.

```sql
CREATE TABLE habit_logs (
  id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(id),
  habit_id INTEGER REFERENCES habits(id),
  log_date DATE,
  completed BOOLEAN,
  logged_at TIMESTAMPTZ
);
```

**Data Collected:**
- Daily completion status
- Behavioral patterns
- Time-series data

### 4. PAID_USERS TABLE
Payment and subscription management.

```sql
CREATE TABLE paid_users (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,      -- Payment email (PII)
  phone VARCHAR(20),                       -- Linked phone (PII)
  access_code VARCHAR(20) UNIQUE,          -- Unique access token
  subscription_type VARCHAR(20),           -- monthly/yearly
  subscription_id VARCHAR(100),            -- FastSpring ID
  customer_id VARCHAR(100),                -- FastSpring customer
  status VARCHAR(20),                      -- active/cancelled/expired
  amount_paid DECIMAL(10,2),               -- Payment amount
  currency VARCHAR(3),                     -- Currency code
  is_affiliate BOOLEAN,                    -- Affiliate status
  affiliate_code VARCHAR(20),              -- Unique affiliate code
  commission_rate DECIMAL(5,2),            -- Affiliate percentage
  paid_at TIMESTAMPTZ,                     -- Payment timestamp
  expires_at TIMESTAMPTZ,                  -- Subscription expiry
  fastspring_order_id VARCHAR(100)         -- Order reference
);
```

**Data Collected:**
- Email addresses (PII)
- Payment history
- Subscription details
- Affiliate relationships

### 5. ACCESS_CODES TABLE
Manages subscription access tokens.

```sql
CREATE TABLE access_codes (
  id SERIAL PRIMARY KEY,
  code VARCHAR(20) UNIQUE,
  paid_user_id INTEGER,
  used_by_phone VARCHAR(20),              -- Phone that redeemed
  used_at TIMESTAMPTZ,                    -- Redemption time
  is_active BOOLEAN,
  expires_at TIMESTAMPTZ
);
```

### 6. PAYMENT_TRANSACTIONS TABLE
Detailed payment transaction log.

```sql
CREATE TABLE payment_transactions (
  id SERIAL PRIMARY KEY,
  paid_user_id INTEGER,
  transaction_id VARCHAR(100),
  type VARCHAR(50),                       -- payment/refund/chargeback
  amount DECIMAL(10,2),
  currency VARCHAR(3),
  fastspring_order_id VARCHAR(100),
  status VARCHAR(20),
  transaction_date TIMESTAMPTZ
);
```

### 7. WEBHOOK_EVENTS TABLE
Logs all incoming webhook events.

```sql
CREATE TABLE webhook_events (
  id SERIAL PRIMARY KEY,
  event_id VARCHAR(100),
  event_type VARCHAR(50),
  processed BOOLEAN,
  payload JSONB,                          -- Full webhook data
  headers JSONB,                          -- HTTP headers
  source VARCHAR(20),                     -- fastspring/twilio
  created_at TIMESTAMPTZ
);
```

### 8. EMAIL_QUEUE TABLE
Manages outbound email communications.

```sql
CREATE TABLE email_queue (
  id SERIAL PRIMARY KEY,
  to_email VARCHAR(255),                  -- Recipient (PII)
  subject VARCHAR(255),
  template VARCHAR(50),
  template_data JSONB,                    -- Email variables
  status VARCHAR(20),                     -- pending/sent/failed
  sent_at TIMESTAMPTZ,
  error_message TEXT
);
```

### 9. AFFILIATE_REFERRALS TABLE
Tracks affiliate program relationships.

```sql
CREATE TABLE affiliate_referrals (
  id SERIAL PRIMARY KEY,
  affiliate_id INTEGER,
  referred_user_id INTEGER,
  commission_amount DECIMAL(10,2),
  commission_status VARCHAR(20),
  referral_order_id VARCHAR(100),
  referred_at TIMESTAMPTZ
);
```

## Data Flow Diagrams

### 1. User Registration Flow

```
WhatsApp User → Twilio API → Webhook Controller → User Model
                                                      ↓
                                                 PostgreSQL
                                                 (users table)
```

**Data Points:**
- Phone number captured from WhatsApp
- Timezone detected or user-selected
- Account created with default LOCKED status

### 2. Payment Flow

```
User → FastSpring Checkout → FastSpring Webhook → Payment Controller
                                                        ↓
                                               Payment Service
                                                        ↓
                                    ┌──────────────────┼──────────────────┐
                                    ↓                  ↓                  ↓
                            paid_users table   access_codes      email_queue
                                                                       ↓
                                                              Email Service → User
```

**Data Points:**
- Email address collected at checkout
- Payment details stored
- Access code generated
- Confirmation email sent

### 3. Daily Usage Flow

```
User Message → Twilio → Webhook Controller → State Machine
                                                   ↓
                                            Session Manager
                                                   ↓
                                         ┌─────────┼─────────┐
                                         ↓         ↓         ↓
                                    habits    habit_logs   users
                                                           (status)
```

**Data Points:**
- Message content processed
- Habits created/updated
- Completion status logged
- Last active timestamp updated

### 4. Data Sharing with Third Parties

```
┌─────────────────────────────────────────┐
│           LOCKIN APPLICATION            │
├─────────────────────────────────────────┤
│                                         │
│  ┌──────────┐        ┌──────────┐     │
│  │PostgreSQL│        │  Node.js │     │
│  │    DB    │←──────→│   App    │     │
│  └──────────┘        └──────────┘     │
│                           ↓            │
└───────────────────────────┼────────────┘
                           ↓
        ┌──────────────────┼──────────────────┐
        ↓                  ↓                  ↓
   ┌─────────┐       ┌──────────┐      ┌─────────┐
   │ Twilio  │       │FastSpring│      │  Email  │
   │   API   │       │    API   │      │ Service │
   └─────────┘       └──────────┘      └─────────┘
        ↓                  ↓                  ↓
   Phone Numbers      Payments           Email Addresses
   Message Content    Card Data*         Access Codes
   Timestamps        Email Addresses     
   
   *Card data handled entirely by FastSpring, never touches our servers
```

## Data Retention

**Current Status:** NO FORMAL DATA RETENTION POLICY

### Actual Retention Periods (Technical)
- **User data:** Indefinite (no automatic deletion)
- **Habit logs:** Indefinite accumulation
- **Payment records:** Indefinite (legal requirement unclear)
- **Webhook events:** Indefinite (no cleanup job)
- **Session data:** 30 minutes (in-memory only)
- **Logs:** Rotating (implementation dependent)

## Data Security Measures

### Implemented
- Bcrypt for password hashing (if implemented)
- HTTPS for API endpoints
- Rate limiting (100 req/15 min global, 10 req/min per user)
- SQL injection prevention (parameterized queries)
- Helmet.js security headers

### NOT Implemented
- Encryption at rest
- Database access logging
- Field-level encryption for PII
- Data masking/tokenization
- Backup encryption
- Audit trails for data access

## Cross-Border Data Transfers

**Current Status:**
- Twilio (US-based) receives phone numbers
- FastSpring (US-based) receives payment data
- No documented data localization
- No transfer impact assessments
- No Standard Contractual Clauses

## Data Subject Rights (GDPR/CCPA)

**Current Implementation:** NONE

Required but missing:
- Right to access (data export)
- Right to rectification (data correction)
- Right to erasure (data deletion)
- Right to portability (machine-readable export)
- Right to object (opt-out mechanisms)
- Right to restrict processing

## Critical Data Governance Gaps

1. **No Data Classification:** PII not formally identified
2. **No Access Controls:** Database has single access credential
3. **No Audit Logging:** Data access not tracked
4. **No Breach Detection:** No monitoring for unauthorized access
5. **No Backup Testing:** Recovery procedures undocumented
6. **No Data Inventory:** No comprehensive data mapping
7. **No Privacy by Design:** Features built without privacy review

## Recommendations for Legal Team

1. **Immediate:** Implement data retention policy
2. **Urgent:** Create data deletion procedures
3. **Important:** Document all data flows with third parties
4. **Important:** Implement audit logging
5. **Consider:** Data minimization review
6. **Consider:** Encryption at rest implementation

---

*Generated for Legal Review - [Current Date]*