# USER INTERACTION FLOWS AND DATA COLLECTION POINTS

## Overview
This document maps all user interaction flows within the Lockin application, identifying each data collection point, consent opportunity, and privacy-relevant decision point.

## 1. NEW USER ONBOARDING FLOW

### Flow Diagram
```
[User sends first WhatsApp message]
           ↓
    [Phone number captured]
           ↓
    [User record created]
           ↓
    [Welcome message sent]
           ↓
    [Main menu displayed]
           ↓
    [Payment prompt shown]
```

### Data Collection Points

| Step | Data Collected | Purpose | Consent | Storage |
|------|---------------|---------|---------|---------|
| Initial Message | Phone number | User identification | ❌ Implied | PostgreSQL |
| Initial Message | Timestamp | Session management | ❌ None | PostgreSQL |
| Initial Message | Message content | Process request | ❌ None | Logs |
| Account Creation | User ID (auto) | Database key | N/A | PostgreSQL |
| Account Creation | Status (LOCKED) | Access control | N/A | PostgreSQL |
| Account Creation | Timezone (UTC) | Default setting | ❌ None | PostgreSQL |

### Privacy Issues
- No privacy notice before data collection
- No explicit consent obtained
- No opt-out mechanism provided
- No age verification performed

## 2. PAYMENT/SUBSCRIPTION FLOW

### Flow Diagram
```
[User selects "Unlock Full Version"]
           ↓
    [Payment instructions sent]
           ↓
    [User clicks FastSpring link]
           ↓
    [External: FastSpring checkout]
           ↓
    [Email address collected]
           ↓
    [Payment processed]
           ↓
    [Webhook received]
           ↓
    [Access code generated]
           ↓
    [Email sent with code]
           ↓
    [User enters code in WhatsApp]
           ↓
    [Account unlocked]
```

### Data Collection Points

| Step | Data Collected | Purpose | Consent | Storage |
|------|---------------|---------|---------|---------|
| FastSpring Checkout | Email address | Payment contact | ⚠️ FastSpring's | PostgreSQL |
| FastSpring Checkout | Billing info | Payment processing | ⚠️ FastSpring's | FastSpring only |
| FastSpring Checkout | IP address | Fraud prevention | ⚠️ FastSpring's | FastSpring only |
| Webhook Processing | Order ID | Transaction tracking | N/A | PostgreSQL |
| Webhook Processing | Customer ID | Customer tracking | N/A | PostgreSQL |
| Webhook Processing | Amount paid | Revenue tracking | N/A | PostgreSQL |
| Code Generation | Access code | Authentication | N/A | PostgreSQL |
| Code Redemption | Phone-email link | Account linking | ❌ None | PostgreSQL |

### Privacy Issues
- Email collected without our privacy policy
- No data processing agreement with FastSpring
- Cross-system data linking without notice
- No information about data retention

## 3. HABIT SETUP FLOW

### Flow Diagram
```
[User selects "Set Daily Habits"]
           ↓
    [Prompt for habit 1]
           ↓
    [User enters habit text]
           ↓
    [Habit stored]
           ↓
    [Repeat for habits 2-5]
           ↓
    [Confirmation message]
```

### Data Collection Points

| Step | Data Collected | Purpose | Consent | Storage |
|------|---------------|---------|---------|---------|
| Habit Entry | Habit description | Core functionality | ❌ Implied | PostgreSQL |
| Habit Entry | Creation timestamp | Tracking | ❌ None | PostgreSQL |
| Habit Entry | User ID link | Association | N/A | PostgreSQL |
| Each Interaction | Message content | Processing | ❌ None | Logs |
| Each Interaction | State transitions | Navigation | ❌ None | Session |

### Privacy Issues
- Habit data may reveal sensitive information (health, religion, etc.)
- No warning about sensitive data
- No encryption for potentially sensitive content
- Indefinite retention

## 4. DAILY LOGGING FLOW

### Flow Diagram
```
[User selects "Log Today's Habits"]
           ↓
    [Display habit 1]
           ↓
    [User marks complete/incomplete]
           ↓
    [Log entry created]
           ↓
    [Repeat for all habits]
           ↓
    [Show completion summary]
           ↓
    [Generate shareable message]
           ↓
    [Provide sharing prompt]
```

### Data Collection Points

| Step | Data Collected | Purpose | Consent | Storage |
|------|---------------|---------|---------|---------|
| Each Log | Completion status | Tracking | ❌ Implied | PostgreSQL |
| Each Log | Log date | Timeline | N/A | PostgreSQL |
| Each Log | Log timestamp | Audit | N/A | PostgreSQL |
| Summary Generation | Streak data | Motivation | N/A | Calculated |
| Share Feature | Potential viral spread | Growth | ⚠️ User action | External |

### Privacy Issues
- Creates detailed behavioral profile over time
- No ability to delete historical data
- Shareable content may expose private habits
- No control over shared data

## 5. AFFILIATE FLOW

### Flow Diagram
```
[Paid user becomes affiliate]
           ↓
    [Affiliate code generated]
           ↓
    [Shares code with others]
           ↓
    [New user uses affiliate code]
           ↓
    [Purchase completed]
           ↓
    [Referral tracked]
           ↓
    [Commission calculated]
           ↓
    [Payout processed]
```

### Data Collection Points

| Step | Data Collected | Purpose | Consent | Storage |
|------|---------------|---------|---------|---------|
| Affiliate Creation | Affiliate code | Tracking | ❌ None | PostgreSQL |
| Referral | Referrer-referee link | Commission | ❌ None | PostgreSQL |
| Commission | Financial data | Payments | ❌ None | PostgreSQL |
| Payout | Payment details | Transfer | ⚠️ Required | PostgreSQL |

### Privacy Issues
- Creates social graph of users
- No affiliate terms and conditions
- Tax reporting obligations unclear
- No privacy notice for affiliate data

## 6. SUPPORT/HELP FLOW

### Flow Diagram
```
[User types unrecognized command]
           ↓
    [Error message shown]
           ↓
    [Help menu displayed]
           ↓
    [User selects option]
```

### Data Collection Points

| Step | Data Collected | Purpose | Consent | Storage |
|------|---------------|---------|---------|---------|
| Error State | Invalid inputs | Debugging | ❌ None | Logs |
| Help Request | Support queries | Improvement | ❌ None | Logs |
| Navigation | Menu selections | UX tracking | ❌ None | Session |

## 7. DATA BREACH SCENARIO FLOW

### Current Flow (CRITICAL ISSUE)
```
[Data breach occurs]
           ↓
    [No detection mechanism]
           ↓
    [No notification process]
           ↓
    [No user remediation]
```

### Required Flow
```
[Data breach detected]
           ↓
    [Incident response team activated]
           ↓
    [Breach assessment (72 hours)]
           ↓
    [Regulatory notification if required]
           ↓
    [User notification if required]
           ↓
    [Remediation support]
           ↓
    [Post-incident review]
```

## 8. ACCOUNT DELETION FLOW

### Current Flow (MISSING)
```
[User wants to delete account]
           ↓
    [No mechanism available]
```

### Required Flow
```
[User requests deletion]
           ↓
    [Identity verification]
           ↓
    [Confirmation request]
           ↓
    [Data export option]
           ↓
    [Deletion processing]
           ↓
    [Confirmation sent]
           ↓
    [Audit log entry]
```

## 9. CONSENT MANAGEMENT FLOW

### Current Flow (MISSING)
```
[User data processed]
           ↓
    [No consent recorded]
           ↓
    [No withdrawal option]
```

### Required Flow
```
[User presented with choices]
           ↓
    [Granular consent options]
           ↓
    [Consent recorded with timestamp]
           ↓
    [Easy withdrawal mechanism]
           ↓
    [Processing adjusted per consent]
```

## Critical Missing Flows

1. **Privacy Policy Acceptance**
   - Never presented
   - Never agreed to
   - No versioning

2. **Terms of Service Agreement**
   - No terms exist
   - No acceptance flow
   - No update mechanism

3. **Age Verification**
   - No age gate
   - No parental consent
   - COPPA violation risk

4. **Data Export**
   - No user access to their data
   - No portability option
   - GDPR Article 20 violation

5. **Marketing Preferences**
   - No opt-in for marketing
   - No preference center
   - No unsubscribe mechanism

## Data Minimization Analysis

### Unnecessary Data Collection
- Full message content in logs
- Indefinite session data
- Webhook headers storage
- Test data in production

### Recommended Minimization
1. Truncate logged messages
2. Implement data retention limits
3. Remove unnecessary webhook data
4. Separate test environment

## User Control Assessment

### Current User Controls
- ✅ Can choose habits
- ✅ Can skip daily logging
- ⚠️ Can stop using service (but data remains)

### Missing User Controls
- ❌ Cannot view collected data
- ❌ Cannot correct data
- ❌ Cannot delete data
- ❌ Cannot export data
- ❌ Cannot manage consent
- ❌ Cannot control data sharing
- ❌ Cannot opt-out of processing

## Recommendations for Flow Improvements

### Immediate Priority
1. Add privacy notice at first interaction
2. Implement age verification gate
3. Create account deletion flow
4. Add consent checkboxes

### High Priority
1. Implement user data access portal
2. Create consent management system
3. Add data export functionality
4. Implement retention limits

### Medium Priority
1. Add preference center
2. Implement granular permissions
3. Create data correction flow
4. Add audit logging

## Compliance Impact

**GDPR Impact:** Multiple Article violations (6, 7, 12-22, 25, 32-34)
**CCPA Impact:** Non-compliant with consumer rights
**COPPA Impact:** Collecting from minors without verification
**TCPA Impact:** Messaging without clear consent

---

*User Flow Analysis for Legal Review*  
*Critical Privacy Gaps Identified*  
*Immediate Action Required*