{"name": "whatsapp-habit-tracker", "version": "1.0.0", "description": "Production-ready WhatsApp habit tracker bot with PostgreSQL", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest --coverage", "test:watch": "jest --watch", "migrate": "node src/db/migrate.js", "migrate:rollback": "node src/db/migrate.js rollback"}, "keywords": ["whatsapp", "bot", "habit-tracker", "twi<PERSON>"], "author": "", "license": "MIT", "dependencies": {"bcrypt": "^5.1.1", "crypto-js": "^4.2.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "moment-timezone": "^0.5.43", "nodemailer": "^7.0.5", "pg": "^8.11.3", "twilio": "^4.19.0", "winston": "^3.11.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}}