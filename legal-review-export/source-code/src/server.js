require('dotenv').config();

const express = require('express');
const helmet = require('helmet');
const logger = require('./config/logger');
const webhookController = require('./controllers/webhookController');
const fastspringController = require('./controllers/fastspringController');
const sessionManager = require('./services/sessionManager');
const paymentService = require('./services/paymentService');
const emailService = require('./services/emailService');
const { validateWebhook } = require('./middleware/validation');
const { checkStopKeywords, checkStartKeyword, checkMessageWindow } = require('./middleware/compliance');
const { globalRateLimiter, perUserRateLimiter } = require('./middleware/rateLimiter');

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet());

// Parse URL-encoded bodies (for Twilio webhooks)
app.use(express.urlencoded({ extended: false }));
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// WhatsApp webhook endpoint with all middleware
app.post('/webhook/whatsapp',
  globalRateLimiter,                        // Global rate limiting
  perUserRateLimiter,                       // Per-user rate limiting
  validateWebhook,                          // Input validation
  webhookController.verifyWebhook,          // Twilio signature verification
  checkStopKeywords,                        // STOP keyword compliance
  checkStartKeyword,                        // START keyword handling
  checkMessageWindow,                       // 24-hour window check
  webhookController.handleIncomingMessage.bind(webhookController)
);

// FastSpring webhook endpoint
app.post('/webhook/fastspring',
  express.json({ limit: '10mb' }),
  fastspringController.handleWebhook.bind(fastspringController)
);

// Test endpoints (only available in test mode)
if (process.env.PAYMENT_TEST_MODE === 'true') {
  // Test webhook simulator
  app.post('/test/webhook',
    express.json(),
    fastspringController.testWebhook.bind(fastspringController)
  );

  // Test status endpoint
  app.get('/test/status', async (req, res) => {
    const pool = require('./db/connection');
    try {
      const stats = await pool.query(`
        SELECT 
          (SELECT COUNT(*) FROM paid_users) as paid_users,
          (SELECT COUNT(*) FROM access_codes) as access_codes,
          (SELECT COUNT(*) FROM access_codes WHERE used_by_phone IS NOT NULL) as used_codes,
          (SELECT COUNT(*) FROM email_queue WHERE status = 'pending') as pending_emails
      `);
      
      res.json({
        testMode: true,
        stats: stats.rows[0],
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Create test payment
  app.post('/test/create-payment', express.json(), async (req, res) => {
    const { email, subscriptionType = 'monthly' } = req.body;
    
    if (!email) {
      return res.status(400).json({ error: 'Email required' });
    }

    try {
      // Simulate FastSpring webhook
      const testPayload = {
        events: [{
          type: 'order.completed',
          data: {
            id: `TEST-${Date.now()}`,
            customer: {
              id: `CUST-${Date.now()}`,
              email
            },
            items: [{
              product: subscriptionType === 'yearly' ? 'habit-tracker-yearly' : 'habit-tracker-monthly',
              total: subscriptionType === 'yearly' ? 30.00 : 5.00,
              subscription: `SUB-${Date.now()}`
            }],
            currency: 'USD',
            total: subscriptionType === 'yearly' ? 30.00 : 5.00
          }
        }]
      };

      // Process webhook
      req.body = testPayload;
      await fastspringController.handleWebhook(req, res);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Clear test data
  app.delete('/test/clear-data', async (req, res) => {
    const pool = require('./db/connection');
    try {
      await pool.query("DELETE FROM affiliate_referrals WHERE test_mode = true");
      await pool.query("DELETE FROM email_queue WHERE template_data::text LIKE '%TEST-%'");
      await pool.query("DELETE FROM webhook_events WHERE test_mode = true");
      await pool.query("DELETE FROM payment_transactions WHERE fastspring_order_id LIKE 'TEST-%'");
      await pool.query("DELETE FROM access_codes WHERE code IN (SELECT access_code FROM paid_users WHERE test_mode = true)");
      await pool.query("DELETE FROM paid_users WHERE test_mode = true");
      
      res.json({ message: 'Test data cleared' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });
}

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error('Unhandled error', { 
    error: err.message, 
    stack: err.stack,
    url: req.url,
    method: req.method
  });
  
  res.status(500).json({ 
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Not found' });
});

// Graceful shutdown
const gracefulShutdown = async (signal) => {
  logger.info(`Received ${signal}, starting graceful shutdown...`);
  
  // Stop session manager
  sessionManager.stop();
  
  // Close database connections
  const pool = require('./db/connection');
  await pool.end();
  
  // Stop server
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
  
  // Force shutdown after 10 seconds
  setTimeout(() => {
    logger.error('Forced shutdown after timeout');
    process.exit(1);
  }, 10000);
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception', { error: error.message, stack: error.stack });
  gracefulShutdown('uncaughtException');
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled rejection', { reason, promise });
});

// Start server
const server = app.listen(PORT, () => {
  logger.info(`WhatsApp Habit Tracker server running on port ${PORT}`);
  logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
  
  // Start session manager
  sessionManager.start();
  
  // Start payment services
  paymentService.startSubscriptionChecker();
  emailService.startQueueProcessor();
  
  // Log important configuration
  logger.info('Configuration loaded', {
    database: process.env.DATABASE_URL ? 'Connected' : 'Not configured',
    twilio: process.env.TWILIO_AUTH_TOKEN ? 'Configured' : 'Not configured',
    fastspring: process.env.FASTSPRING_WEBHOOK_SECRET ? 'Configured' : 'Not configured',
    paymentTestMode: process.env.PAYMENT_TEST_MODE === 'true',
    sessionTimeout: '30 minutes',
    rateLimit: '100 requests per 15 minutes'
  });
});

module.exports = app;