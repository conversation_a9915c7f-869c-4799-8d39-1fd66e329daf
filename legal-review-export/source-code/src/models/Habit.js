const pool = require('../db/connection');
const logger = require('../config/logger');

class Habit {
  static async findByUserId(userId) {
    try {
      const result = await pool.query(
        'SELECT * FROM habits WHERE user_id = $1 ORDER BY habit_number',
        [userId]
      );
      return result.rows;
    } catch (error) {
      logger.error('Error finding habits by user', { error: error.message });
      throw error;
    }
  }

  static async findByUserAndNumber(userId, habitNumber) {
    try {
      const result = await pool.query(
        'SELECT * FROM habits WHERE user_id = $1 AND habit_number = $2',
        [userId, habitNumber]
      );
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error finding habit by number', { error: error.message });
      throw error;
    }
  }

  static async upsert(userId, habitNumber, habitName) {
    try {
      const result = await pool.query(
        `INSERT INTO habits (user_id, habit_number, habit_name, created_at, updated_at) 
         VALUES ($1, $2, $3, NOW(), NOW()) 
         ON CONFLICT (user_id, habit_number) 
         DO UPDATE SET habit_name = $3, updated_at = NOW() 
         RETURNING *`,
        [userId, habitNumber, habitName]
      );
      return result.rows[0];
    } catch (error) {
      logger.error('Error upserting habit', { error: error.message });
      throw error;
    }
  }

  static async delete(userId, habitNumber) {
    try {
      const result = await pool.query(
        'DELETE FROM habits WHERE user_id = $1 AND habit_number = $2 RETURNING *',
        [userId, habitNumber]
      );
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error deleting habit', { error: error.message });
      throw error;
    }
  }

  static async logHabit(userId, habitId, date, completed) {
    try {
      const result = await pool.query(
        `INSERT INTO habit_logs (user_id, habit_id, log_date, completed, logged_at) 
         VALUES ($1, $2, $3, $4, NOW()) 
         ON CONFLICT (user_id, habit_id, log_date) 
         DO UPDATE SET completed = $4, logged_at = NOW() 
         RETURNING *`,
        [userId, habitId, date, completed]
      );
      return result.rows[0];
    } catch (error) {
      logger.error('Error logging habit', { error: error.message });
      throw error;
    }
  }

  static async getTodayLogs(userId, timezone = 'UTC') {
    try {
      const result = await pool.query(
        `SELECT h.*, 
                hl.completed as completed,
                hl.logged_at
         FROM habits h
         LEFT JOIN habit_logs hl ON h.id = hl.habit_id 
           AND hl.log_date = (NOW() AT TIME ZONE $2)::date
         WHERE h.user_id = $1
         ORDER BY h.habit_number`,
        [userId, timezone]
      );
      return result.rows;
    } catch (error) {
      logger.error('Error getting today logs', { error: error.message });
      throw error;
    }
  }

  static async getProgress(userId, days = 7, timezone = 'UTC') {
    try {
      const result = await pool.query(
        `WITH date_series AS (
           SELECT generate_series(
             (NOW() AT TIME ZONE $2)::date - INTERVAL '${days - 1} days',
             (NOW() AT TIME ZONE $2)::date,
             '1 day'::interval
           )::date AS log_date
         )
         SELECT 
           h.habit_number,
           h.habit_name,
           COUNT(CASE WHEN hl.completed = true THEN 1 END) as completed_days,
           COUNT(ds.log_date) as total_days,
           ROUND(COUNT(CASE WHEN hl.completed = true THEN 1 END)::numeric / COUNT(ds.log_date) * 100, 0) as completion_rate
         FROM habits h
         CROSS JOIN date_series ds
         LEFT JOIN habit_logs hl ON h.id = hl.habit_id AND hl.log_date = ds.log_date
         WHERE h.user_id = $1
         GROUP BY h.id, h.habit_number, h.habit_name
         ORDER BY h.habit_number`,
        [userId, timezone]
      );
      return result.rows;
    } catch (error) {
      logger.error('Error getting progress', { error: error.message });
      throw error;
    }
  }

  static async getStreak(userId, habitId, timezone = 'UTC') {
    try {
      const result = await pool.query(
        `WITH RECURSIVE streak_calc AS (
           SELECT 
             log_date,
             completed,
             1 as streak_length
           FROM habit_logs
           WHERE user_id = $1 AND habit_id = $2 
             AND log_date = (NOW() AT TIME ZONE $3)::date
             AND completed = true
           
           UNION ALL
           
           SELECT 
             hl.log_date,
             hl.completed,
             sc.streak_length + 1
           FROM habit_logs hl
           JOIN streak_calc sc ON hl.log_date = sc.log_date - INTERVAL '1 day'
           WHERE hl.user_id = $1 
             AND hl.habit_id = $2 
             AND hl.completed = true
         )
         SELECT COALESCE(MAX(streak_length), 0) as current_streak
         FROM streak_calc`,
        [userId, habitId, timezone]
      );
      return result.rows[0]?.current_streak || 0;
    } catch (error) {
      logger.error('Error getting streak', { error: error.message });
      throw error;
    }
  }

  static async getWeekLogs(userId, timezone = 'UTC') {
    try {
      const result = await pool.query(
        `SELECT h.habit_number, h.habit_name, hl.completed, hl.log_date
         FROM habits h
         LEFT JOIN habit_logs hl ON h.id = hl.habit_id 
           AND hl.log_date >= (NOW() AT TIME ZONE $2)::date - INTERVAL '6 days'
           AND hl.log_date <= (NOW() AT TIME ZONE $2)::date
         WHERE h.user_id = $1
         ORDER BY h.habit_number, hl.log_date`,
        [userId, timezone]
      );
      return result.rows;
    } catch (error) {
      logger.error('Error getting week logs', { error: error.message });
      throw error;
    }
  }

  static async getMonthLogs(userId, timezone = 'UTC') {
    try {
      const result = await pool.query(
        `SELECT h.habit_number, h.habit_name, hl.completed, hl.log_date
         FROM habits h
         LEFT JOIN habit_logs hl ON h.id = hl.habit_id 
           AND hl.log_date >= date_trunc('month', (NOW() AT TIME ZONE $2)::date)
           AND hl.log_date <= (NOW() AT TIME ZONE $2)::date
         WHERE h.user_id = $1
         ORDER BY h.habit_number, hl.log_date`,
        [userId, timezone]
      );
      return result.rows;
    } catch (error) {
      logger.error('Error getting month logs', { error: error.message });
      throw error;
    }
  }

  static async getYearLogs(userId, timezone = 'UTC') {
    try {
      const result = await pool.query(
        `SELECT h.habit_number, h.habit_name, hl.completed, hl.log_date
         FROM habits h
         LEFT JOIN habit_logs hl ON h.id = hl.habit_id 
           AND hl.log_date >= date_trunc('year', (NOW() AT TIME ZONE $2)::date)
           AND hl.log_date <= (NOW() AT TIME ZONE $2)::date
         WHERE h.user_id = $1
         ORDER BY h.habit_number, hl.log_date`,
        [userId, timezone]
      );
      return result.rows;
    } catch (error) {
      logger.error('Error getting year logs', { error: error.message });
      throw error;
    }
  }

  static async getAllLogs(userId) {
    try {
      const result = await pool.query(
        `SELECT h.habit_number, h.habit_name, hl.completed, hl.log_date
         FROM habits h
         LEFT JOIN habit_logs hl ON h.id = hl.habit_id
         WHERE h.user_id = $1
         ORDER BY h.habit_number, hl.log_date`,
        [userId]
      );
      return result.rows;
    } catch (error) {
      logger.error('Error getting all logs', { error: error.message });
      throw error;
    }
  }

  // HABIT COMPLETION STATS METHODS
  static async getCurrentStreak(userId, timezone = 'UTC') {
    try {
      const result = await pool.query(
        `WITH daily_completions AS (
           SELECT 
             hl.log_date,
             COUNT(CASE WHEN hl.completed = true THEN 1 END) as completed_habits,
             COUNT(h.id) as total_habits
           FROM habit_logs hl
           JOIN habits h ON h.id = hl.habit_id
           WHERE h.user_id = $1
           GROUP BY hl.log_date
           HAVING COUNT(h.id) = COUNT(CASE WHEN hl.completed = true THEN 1 END)
         ),
         streak_calc AS (
           SELECT 
             log_date,
             ROW_NUMBER() OVER (ORDER BY log_date DESC) as day_rank
           FROM daily_completions
           WHERE log_date <= (NOW() AT TIME ZONE $2)::date
         )
         SELECT COUNT(*) as current_streak
         FROM streak_calc s1
         WHERE NOT EXISTS (
           SELECT 1 FROM streak_calc s2 
           WHERE s2.day_rank = s1.day_rank + 1 
           AND s2.log_date != s1.log_date - INTERVAL '1 day'
         )`,
        [userId, timezone]
      );
      return result.rows[0]?.current_streak || 0;
    } catch (error) {
      logger.error('Error calculating current streak', { error: error.message });
      return 0;
    }
  }

  static async getBestStreak(userId) {
    try {
      const result = await pool.query(
        `WITH daily_completions AS (
           SELECT 
             hl.log_date,
             COUNT(CASE WHEN hl.completed = true THEN 1 END) as completed_habits,
             COUNT(h.id) as total_habits
           FROM habit_logs hl
           JOIN habits h ON h.id = hl.habit_id
           WHERE h.user_id = $1
           GROUP BY hl.log_date
           HAVING COUNT(h.id) = COUNT(CASE WHEN hl.completed = true THEN 1 END)
         ),
         streak_groups AS (
           SELECT 
             log_date,
             log_date - ROW_NUMBER() OVER (ORDER BY log_date)::int * INTERVAL '1 day' as streak_group
           FROM daily_completions
         )
         SELECT COALESCE(MAX(cnt), 0) as best_streak
         FROM (
           SELECT COUNT(*) as cnt
           FROM streak_groups
           GROUP BY streak_group
         ) subq`,
        [userId]
      );
      return result.rows[0]?.best_streak || 0;
    } catch (error) {
      logger.error('Error calculating best streak', { error: error.message });
      return 0;
    }
  }

  static async getPerfectDaysStats(userId, timezone = 'UTC') {
    try {
      const result = await pool.query(
        `WITH daily_completions AS (
           SELECT 
             hl.log_date,
             COUNT(CASE WHEN hl.completed = true THEN 1 END) as completed_habits,
             COUNT(h.id) as total_habits
           FROM habit_logs hl
           JOIN habits h ON h.id = hl.habit_id
           WHERE h.user_id = $1
           GROUP BY hl.log_date
         )
         SELECT 
           COUNT(CASE WHEN completed_habits = total_habits THEN 1 END) as perfect_days,
           COUNT(*) as total_days,
           ROUND(
             COUNT(CASE WHEN completed_habits = total_habits THEN 1 END)::numeric / 
             NULLIF(COUNT(*), 0) * 100, 0
           ) as completion_rate
         FROM daily_completions`,
        [userId]
      );
      return {
        perfect_days: result.rows[0]?.perfect_days || 0,
        total_days: result.rows[0]?.total_days || 0,
        completion_rate: result.rows[0]?.completion_rate || 0
      };
    } catch (error) {
      logger.error('Error calculating perfect days stats', { error: error.message });
      return { perfect_days: 0, total_days: 0, completion_rate: 0 };
    }
  }

  static async getWeeklyPerformance(userId, timezone = 'UTC') {
    try {
      const result = await pool.query(
        `WITH weekly_data AS (
           SELECT 
             hl.log_date,
             CASE 
               WHEN hl.log_date >= (NOW() AT TIME ZONE $2)::date - INTERVAL '6 days'
                    AND hl.log_date <= (NOW() AT TIME ZONE $2)::date
               THEN 'this_week'
               WHEN hl.log_date >= (NOW() AT TIME ZONE $2)::date - INTERVAL '13 days'
                    AND hl.log_date <= (NOW() AT TIME ZONE $2)::date - INTERVAL '7 days'
               THEN 'last_week'
               ELSE 'other'
             END as week_type,
             COUNT(CASE WHEN hl.completed = true THEN 1 END) as completed_habits,
             COUNT(h.id) as total_habits
           FROM habit_logs hl
           JOIN habits h ON h.id = hl.habit_id
           WHERE h.user_id = $1
             AND hl.log_date >= (NOW() AT TIME ZONE $2)::date - INTERVAL '13 days'
           GROUP BY hl.log_date
         )
         SELECT 
           week_type,
           COUNT(CASE WHEN completed_habits = total_habits THEN 1 END) as perfect_days,
           COUNT(*) as total_days,
           ROUND(
             COUNT(CASE WHEN completed_habits = total_habits THEN 1 END)::numeric / 
             NULLIF(COUNT(*), 0) * 100, 0
           ) as completion_rate
         FROM weekly_data
         WHERE week_type IN ('this_week', 'last_week')
         GROUP BY week_type`,
        [userId, timezone]
      );
      
      const thisWeek = result.rows.find(r => r.week_type === 'this_week') || 
        { perfect_days: 0, total_days: 0, completion_rate: 0 };
      const lastWeek = result.rows.find(r => r.week_type === 'last_week') || 
        { perfect_days: 0, total_days: 0, completion_rate: 0 };
      
      return { thisWeek, lastWeek };
    } catch (error) {
      logger.error('Error calculating weekly performance', { error: error.message });
      return { 
        thisWeek: { perfect_days: 0, total_days: 0, completion_rate: 0 },
        lastWeek: { perfect_days: 0, total_days: 0, completion_rate: 0 }
      };
    }
  }

  static async getHabitBreakdown(userId) {
    try {
      const result = await pool.query(
        `SELECT 
           h.habit_number,
           h.habit_name,
           COUNT(CASE WHEN hl.completed = true THEN 1 END) as completed_count,
           COUNT(hl.log_date) as total_logged,
           ROUND(
             COUNT(CASE WHEN hl.completed = true THEN 1 END)::numeric / 
             NULLIF(COUNT(hl.log_date), 0) * 100, 0
           ) as completion_rate
         FROM habits h
         LEFT JOIN habit_logs hl ON h.id = hl.habit_id
         WHERE h.user_id = $1
         GROUP BY h.id, h.habit_number, h.habit_name
         ORDER BY h.habit_number`,
        [userId]
      );
      return result.rows;
    } catch (error) {
      logger.error('Error calculating habit breakdown', { error: error.message });
      return [];
    }
  }

  static async getTotalDaysLogged(userId) {
    try {
      const result = await pool.query(
        `SELECT COUNT(DISTINCT hl.log_date) as total_days
         FROM habit_logs hl
         JOIN habits h ON h.id = hl.habit_id
         WHERE h.user_id = $1`,
        [userId]
      );
      return result.rows[0]?.total_days || 0;
    } catch (error) {
      logger.error('Error getting total days logged', { error: error.message });
      return 0;
    }
  }
}

module.exports = Habit;