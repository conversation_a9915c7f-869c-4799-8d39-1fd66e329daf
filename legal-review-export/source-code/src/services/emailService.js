const nodemailer = require('nodemailer');
const pool = require('../db/connection');
const logger = require('../config/logger');

class EmailService {
  constructor() {
    this.transporter = null;
    this.templates = {
      welcome_monthly: this.getWelcomeMonthlyTemplate,
      welcome_yearly: this.getWelcomeYearlyTemplate,
      payment_failed: this.getPaymentFailedTemplate,
      subscription_cancelled: this.getSubscriptionCancelledTemplate
    };
    
    this.initializeTransporter();
  }

  /**
   * Initialize email transporter
   */
  initializeTransporter() {
    if (process.env.SMTP_HOST && process.env.SMTP_USER) {
      this.transporter = nodemailer.createTransport({
        host: process.env.SMTP_HOST,
        port: parseInt(process.env.SMTP_PORT || '587'),
        secure: process.env.SMTP_PORT === '465',
        auth: {
          user: process.env.SMTP_USER,
          pass: process.env.SMTP_PASS
        }
      });

      // Verify transporter configuration
      this.transporter.verify((error) => {
        if (error) {
          logger.error('Email transporter verification failed', { error: error.message });
          this.transporter = null;
        } else {
          logger.info('Email service ready');
        }
      });
    } else {
      logger.warn('Email service not configured - emails will be logged only');
    }
  }

  /**
   * Process email queue
   */
  async processQueue() {
    try {
      // Get pending emails
      const result = await pool.query(
        `SELECT * FROM email_queue 
         WHERE status = 'pending' 
         AND retry_count < 3
         ORDER BY priority ASC, created_at ASC 
         LIMIT 10`
      );

      for (const email of result.rows) {
        await this.sendEmail(email);
      }
    } catch (error) {
      logger.error('Error processing email queue', { error: error.message });
    }
  }

  /**
   * Send individual email
   */
  async sendEmail(emailRecord) {
    try {
      // Get template
      const templateFunc = this.templates[emailRecord.template];
      if (!templateFunc) {
        throw new Error(`Unknown email template: ${emailRecord.template}`);
      }

      // Generate email content
      const templateData = JSON.parse(emailRecord.template_data);
      const { subject, html, text } = templateFunc(templateData);

      // Send email
      if (this.transporter) {
        await this.transporter.sendMail({
          from: process.env.EMAIL_FROM || '<EMAIL>',
          to: emailRecord.to_email,
          subject: emailRecord.subject || subject,
          html,
          text
        });

        // Mark as sent
        await pool.query(
          `UPDATE email_queue 
           SET status = 'sent', sent_at = NOW(), updated_at = NOW()
           WHERE id = $1`,
          [emailRecord.id]
        );

        logger.info('Email sent successfully', {
          to: emailRecord.to_email,
          template: emailRecord.template
        });
      } else {
        // Log email content if transporter not available
        logger.info('Email (logged only - no SMTP configured)', {
          to: emailRecord.to_email,
          subject: emailRecord.subject || subject,
          template: emailRecord.template,
          content: text
        });

        // Mark as sent (logged)
        await pool.query(
          `UPDATE email_queue 
           SET status = 'sent', sent_at = NOW(), updated_at = NOW()
           WHERE id = $1`,
          [emailRecord.id]
        );
      }
    } catch (error) {
      logger.error('Error sending email', {
        error: error.message,
        emailId: emailRecord.id
      });

      // Update retry count
      await pool.query(
        `UPDATE email_queue 
         SET retry_count = retry_count + 1, 
             error_message = $1,
             updated_at = NOW()
         WHERE id = $2`,
        [error.message, emailRecord.id]
      );

      // Mark as failed if max retries reached
      if (emailRecord.retry_count >= 2) {
        await pool.query(
          `UPDATE email_queue 
           SET status = 'failed', updated_at = NOW()
           WHERE id = $1`,
          [emailRecord.id]
        );
      }
    }
  }

  /**
   * Welcome email template for monthly subscribers
   */
  getWelcomeMonthlyTemplate(data) {
    const { accessCode, botPhone } = data;
    
    return {
      subject: 'Welcome to Habit Tracker - Your Access Code',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #4CAF50; color: white; padding: 20px; text-align: center; border-radius: 5px; }
            .code-box { background: #f4f4f4; border: 2px dashed #4CAF50; padding: 15px; margin: 20px 0; text-align: center; }
            .code { font-size: 24px; font-weight: bold; color: #4CAF50; letter-spacing: 2px; }
            .steps { background: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0; }
            .step { margin: 10px 0; padding-left: 20px; }
            .footer { text-align: center; color: #666; margin-top: 30px; font-size: 12px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Welcome to Habit Tracker!</h1>
              <p>Your Monthly Subscription is Active</p>
            </div>
            
            <p>Hi there!</p>
            
            <p>Thank you for subscribing to Habit Tracker! Your monthly subscription ($5/month) is now active.</p>
            
            <div class="code-box">
              <p>Your Access Code:</p>
              <div class="code">${accessCode}</div>
            </div>
            
            <div class="steps">
              <h3>How to Get Started:</h3>
              <div class="step">1. Open WhatsApp on your phone</div>
              <div class="step">2. Send a message to: <strong>${botPhone}</strong></div>
              <div class="step">3. Type exactly: <strong>START ${accessCode}</strong></div>
              <div class="step">4. Follow the setup instructions to add your habits</div>
              <div class="step">5. Start tracking your daily progress!</div>
            </div>
            
            <p><strong>Important:</strong> Keep this access code safe. You'll need it to activate your account.</p>
            
            <p>If you have any questions, feel free to reach out to our support team.</p>
            
            <p>Here's to building better habits! 🎯</p>
            
            <div class="footer">
              <p>© 2025 Habit Tracker. All rights reserved.</p>
              <p>You're receiving this email because you subscribed to Habit Tracker.</p>
            </div>
          </div>
        </body>
        </html>
      `,
      text: `
Welcome to Habit Tracker!

Thank you for subscribing! Your monthly subscription ($5/month) is now active.

YOUR ACCESS CODE: ${accessCode}

How to Get Started:
1. Open WhatsApp on your phone
2. Send a message to: ${botPhone}
3. Type exactly: START ${accessCode}
4. Follow the setup instructions to add your habits
5. Start tracking your daily progress!

Important: Keep this access code safe. You'll need it to activate your account.

If you have any questions, feel free to reach out to our support team.

Here's to building better habits!

© 2025 Habit Tracker. All rights reserved.
      `
    };
  }

  /**
   * Welcome email template for yearly subscribers (with affiliate info)
   */
  getWelcomeYearlyTemplate(data) {
    const { accessCode, affiliateCode, botPhone } = data;
    
    return {
      subject: 'Welcome to Habit Tracker - Your Access Code & Affiliate Program',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #4CAF50; color: white; padding: 20px; text-align: center; border-radius: 5px; }
            .code-box { background: #f4f4f4; border: 2px dashed #4CAF50; padding: 15px; margin: 20px 0; text-align: center; }
            .code { font-size: 24px; font-weight: bold; color: #4CAF50; letter-spacing: 2px; }
            .affiliate-box { background: #fff3cd; border: 2px solid #ffc107; padding: 15px; margin: 20px 0; border-radius: 5px; }
            .steps { background: #f9f9f9; padding: 20px; border-radius: 5px; margin: 20px 0; }
            .step { margin: 10px 0; padding-left: 20px; }
            .footer { text-align: center; color: #666; margin-top: 30px; font-size: 12px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Welcome to Habit Tracker!</h1>
              <p>Your Yearly Subscription is Active + You're Now an Affiliate!</p>
            </div>
            
            <p>Hi there!</p>
            
            <p>Thank you for subscribing to Habit Tracker! Your yearly subscription ($30/year) is now active.</p>
            
            <div class="code-box">
              <p>Your Access Code:</p>
              <div class="code">${accessCode}</div>
            </div>
            
            <div class="steps">
              <h3>How to Get Started:</h3>
              <div class="step">1. Open WhatsApp on your phone</div>
              <div class="step">2. Send a message to: <strong>${botPhone}</strong></div>
              <div class="step">3. Type exactly: <strong>START ${accessCode}</strong></div>
              <div class="step">4. Follow the setup instructions to add your habits</div>
              <div class="step">5. Start tracking your daily progress!</div>
            </div>
            
            <div class="affiliate-box">
              <h3>🎉 You're Automatically an Affiliate!</h3>
              <p>As a yearly subscriber, you're enrolled in our affiliate program with a 30% commission!</p>
              <p><strong>Your Affiliate Code:</strong> <span style="font-size: 18px; font-weight: bold;">${affiliateCode}</span></p>
              <p>Share this code with friends and earn 30% commission on every sale!</p>
            </div>
            
            <p><strong>Important:</strong> Keep both codes safe. The access code activates your account, and the affiliate code tracks your referrals.</p>
            
            <p>If you have any questions, feel free to reach out to our support team.</p>
            
            <p>Here's to building better habits and earning rewards! 🎯💰</p>
            
            <div class="footer">
              <p>© 2025 Habit Tracker. All rights reserved.</p>
              <p>You're receiving this email because you subscribed to Habit Tracker.</p>
            </div>
          </div>
        </body>
        </html>
      `,
      text: `
Welcome to Habit Tracker!

Thank you for subscribing! Your yearly subscription ($30/year) is now active.

YOUR ACCESS CODE: ${accessCode}

How to Get Started:
1. Open WhatsApp on your phone
2. Send a message to: ${botPhone}
3. Type exactly: START ${accessCode}
4. Follow the setup instructions to add your habits
5. Start tracking your daily progress!

🎉 YOU'RE AUTOMATICALLY AN AFFILIATE!
As a yearly subscriber, you're enrolled in our affiliate program with a 30% commission!

Your Affiliate Code: ${affiliateCode}
Share this code with friends and earn 30% commission on every sale!

Important: Keep both codes safe. The access code activates your account, and the affiliate code tracks your referrals.

If you have any questions, feel free to reach out to our support team.

Here's to building better habits and earning rewards!

© 2025 Habit Tracker. All rights reserved.
      `
    };
  }

  /**
   * Payment failed email template
   */
  getPaymentFailedTemplate(data) {
    return {
      subject: 'Payment Failed - Action Required',
      html: `
        <p>Hi,</p>
        <p>We were unable to process your payment for Habit Tracker.</p>
        <p>Please update your payment method to continue using the service.</p>
        <p>Thank you!</p>
      `,
      text: 'Payment failed. Please update your payment method.'
    };
  }

  /**
   * Subscription cancelled email template
   */
  getSubscriptionCancelledTemplate(data) {
    return {
      subject: 'Subscription Cancelled',
      html: `
        <p>Hi,</p>
        <p>Your Habit Tracker subscription has been cancelled.</p>
        <p>You can resubscribe anytime to continue tracking your habits.</p>
        <p>Thank you for using Habit Tracker!</p>
      `,
      text: 'Your subscription has been cancelled.'
    };
  }

  /**
   * Start email queue processor (runs every minute)
   */
  startQueueProcessor() {
    setInterval(() => {
      this.processQueue();
    }, 60000); // Process every minute
    
    // Process immediately on startup
    this.processQueue();
  }
}

module.exports = new EmailService();