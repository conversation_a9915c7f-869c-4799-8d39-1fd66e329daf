const User = require('../models/User');
const Habit = require('../models/Habit');
const AuditLog = require('../models/AuditLog');
const { STATES, USER_STATUS, AUDIT_EVENTS, HABITS } = require('../config/constants');
const logger = require('../config/logger');
const moment = require('moment-timezone');
const motivationalQuotes = require('../utils/motivationalQuotes');

class StateMachine {
  constructor() {
    this.transitions = {
      [STATES.MAIN_MENU]: this.handleMainMenu.bind(this),
      [STATES.AWAITING_NAME]: this.handleName.bind(this),
      [STATES.AWAITING_TIMEZONE]: this.handleTimezone.bind(this),
      [STATES.ONBOARDING_MENU]: this.handleOnboardingMenu.bind(this),
      [STATES.SETTING_HABIT]: this.handleSettingHabit.bind(this),
      [STATES.LOGGING_HABITS]: this.handleLoggingHabits.bind(this),
      [STATES.VIEWING_PROGRESS]: this.handleViewingProgress.bind(this),
      [STATES.STATS_MENU]: this.handleStatsMenu.bind(this),
      [STATES.STATS_30_DAY]: this.handleStats30Day.bind(this),
      [STATES.STATS_100_DAY]: this.handleStats100Day.bind(this),
      [STATES.SETTINGS_MENU]: this.handleSettingsMenu.bind(this),
      [STATES.COMPLETION_SCREEN]: this.handleCompletionScreen.bind(this)
    };
  }

  async processMessage(user, message) {
    try {
      // Update last active
      await User.updateLastActive(user.id);

      // DEVELOPER TEST COMMAND: Reset user to clean slate
      if (message.trim().toUpperCase() === 'RESET_TEST') {
        return await this.handleResetTest(user);
      }

      // Get the handler for current state
      const handler = this.transitions[user.current_state] || this.handleMainMenu.bind(this);
      const response = await handler(user, message);

      return response;
    } catch (error) {
      logger.error('State machine error', { 
        userId: user.id, 
        state: user.current_state, 
        error: error.message 
      });
      
      await AuditLog.log(user.id, AUDIT_EVENTS.ERROR, {
        state: user.current_state,
        error: error.message
      });

      return {
        message: "Sorry, something went wrong. Please try again or type 'menu' to return to the main menu.",
        newState: user.current_state
      };
    }
  }

  // ⚠️  MAIN MENU FORMAT IS FINAL - DO NOT MODIFY ⚠️
  // This function contains LOCKED main menu format. 
  // Backup: lockin-backup-2025-08-15-1535-working-main-menu
  async handleMainMenu(user, message) {
    const input = message.toLowerCase().trim();

    // Check if user is locked
    if (user.status === USER_STATUS.LOCKED) {

      // Start onboarding for new users
      await User.updateStatus(user.id, USER_STATUS.ONBOARDING);
      await User.updateState(user.id, STATES.AWAITING_NAME);
      
      return {
        message: `Welcome to Habit Tracker! 🎯

Let's set up your profile.
What should I call you? (Enter your first name)`,
        newState: STATES.AWAITING_NAME
      };
    }

    // User is unlocked - check if in onboarding flow
    if (user.status === USER_STATUS.ONBOARDING && user.current_state === STATES.SETTING_HABIT) {
      return await this.handleSettingHabit(user, message);
    } else if (user.status === USER_STATUS.ONBOARDING) {
      // Start proper onboarding flow with welcome message
      await User.updateState(user.id, STATES.SETTING_HABIT, { habitNumber: 1 });
      return {
        message: `🎯 Welcome to Habit Tracker!

Let's set up your habits to track. You can add up to 5 habits.

📝 Habit 1 of 5

What's the first habit you want to track?

Examples:
• Drink 8 glasses of water
• Exercise for 30 minutes
• Read for 20 minutes
• Meditate
• No social media before noon

Enter habit 1:`,
        newState: STATES.SETTING_HABIT
      };
    }

    // Check if user has any habits - if not, force them into onboarding
    const habits = await Habit.findByUserId(user.id);
    if (habits.length === 0) {
      // User has no habits - force them into habit setup
      await User.updateStatus(user.id, USER_STATUS.ONBOARDING);
      await User.updateState(user.id, STATES.SETTING_HABIT, { habitNumber: 1 });
      return {
        message: `🎯 Let's set up your habits!

You need to add at least one habit to start tracking.

📝 Habit 1 of 5

What's the first habit you want to track?

Examples:
• Drink 8 glasses of water
• Exercise for 30 minutes
• Read for 20 minutes
• Meditate
• No social media before noon

Enter habit 1:`,
        newState: STATES.SETTING_HABIT
      };
    }

    // Active user menu
    if (input === '1' || input === 'log') {
      await User.updateState(user.id, STATES.LOGGING_HABITS);
      return await this.showTodayHabits(user);
    } else if (input === '2' || input === 'stats' || input === 'progress') {
      await User.updateState(user.id, STATES.STATS_MENU);
      return await this.handleStatsMenu(user, '');
    } else if (input === '3' || input === 'settings') {
      await User.updateState(user.id, STATES.SETTINGS_MENU);
      return await this.showSettingsMenu(user);
    }

    // Get today's logs to determine completion status
    const todayLogs = await Habit.getTodayLogs(user.id, user.timezone);
    
    // Determine logging status
    let loggedCount = 0;
    let totalCount = 0;
    todayLogs.forEach(log => {
      totalCount++;
      if (log.completed !== null) {
        loggedCount++;
      }
    });
    
    // Determine option 1 text based on logging status
    let option1Text = 'Log today\'s habits';
    if (loggedCount === 0) {
      option1Text = 'Log today\'s habits';
    } else if (loggedCount < totalCount) {
      option1Text = 'Complete today\'s habits';
    } else {
      option1Text = 'Edit today\'s habits';
    }
    
    // Format habit list with status indicators
    const habitsList = todayLogs.map(h => {
      let statusIcon = '⚠️'; // Not logged (warning)
      if (h.completed === true) {
        statusIcon = '✅'; // Completed
      } else if (h.completed === false) {
        statusIcon = '❌'; // Not completed
      }
      return `${statusIcon}${h.habit_number}. ${h.habit_name}`;
    }).join('\n');

    const greeting = user.display_name ? `Hi ${user.display_name}!` : 'Welcome back!';

    // ############################################################################
    // ⚠️  CRITICAL WARNING: MAIN MENU FORMAT IS FINAL - DO NOT MODIFY ⚠️
    // ############################################################################
    // 
    // 🔒 LOCKED: August 15, 2025 - This main menu format is FINAL and FROZEN
    // 🔒 BACKUP: lockin-backup-2025-08-15-1535-working-main-menu
    // 
    // ❌ ABSOLUTELY PROHIBITED CHANGES:
    // - Modifying status icons (⚠️, ✅, ❌)
    // - Changing habit list format "${statusIcon}${habit_number}. ${habit_name}"
    // - Altering greeting structure "${greeting} 👋"
    // - Modifying header "📋 Your Habits (${todayLogs.length}/5):"
    // - Changing footer "Make a selection (reply with a number)."
    // - Breaking dynamic option1Text logic
    // - Rearranging menu structure or layout
    // - Adding/removing emoji elements
    // - Changing indentation or spacing
    // 
    // ✅ ONLY ALLOWED CHANGES:
    // - Minor text updates to option labels (e.g., "Check stats" → "View stats")
    // - Critical bug fixes that preserve exact visual format
    // 
    // 🚨 CONSEQUENCES OF MODIFICATION:
    // - Will break working WhatsApp interface
    // - Violates locked specification
    // - Requires rollback from backup
    // 
    // ⚠️  IF YOU NEED TO MODIFY THIS MENU: STOP! RESTORE FROM BACKUP INSTEAD!
    // ############################################################################
    
    return {
      message: `${greeting} 👋

📋 Your Habits (${todayLogs.length}/5):
${habitsList}

Choose an option:
1️⃣ ${option1Text}
2️⃣ Check stats
3️⃣ Settings

Make a selection (reply with a number).`,
      newState: STATES.MAIN_MENU
    };
  }


  async handleName(user, message) {
    const name = message.trim().substring(0, 50);

    if (name.length < 1) {
      return {
        message: "Please enter a valid name (at least 1 character):",
        newState: STATES.AWAITING_NAME
      };
    }

    await User.setName(user.id, name);
    await User.updateState(user.id, STATES.AWAITING_TIMEZONE);

    // List common timezones
    return {
      message: `Nice to meet you, ${name}! 👋

Now, what's your timezone? This helps with daily reminders.

Common timezones:
• America/New_York (EST/EDT)
• America/Chicago (CST/CDT)
• America/Denver (MST/MDT)
• America/Los_Angeles (PST/PDT)
• Europe/London (GMT/BST)
• Europe/Paris (CET/CEST)
• Asia/Tokyo (JST)
• Australia/Sydney (AEDT/AEST)

Enter your timezone (or type 'UTC' for default):`,
      newState: STATES.AWAITING_TIMEZONE
    };
  }

  async handleTimezone(user, message) {
    const timezone = message.trim();

    // Validate timezone
    if (!moment.tz.zone(timezone)) {
      return {
        message: `❌ Invalid timezone: "${timezone}"

Please enter a valid timezone from the list above, or type 'UTC' for default:`,
        newState: STATES.AWAITING_TIMEZONE
      };
    }

    await User.setTimezone(user.id, timezone);
    // Start with habit 1 automatically
    await User.updateState(user.id, STATES.SETTING_HABIT, { habitNumber: 1 });

    return {
      message: `✅ Timezone set to ${timezone}

Great! Now let's set up your habits to track.

📝 Habit 1 of 5

What's the first habit you want to track?

Examples:
• Drink 8 glasses of water
• Exercise for 30 minutes
• Read for 20 minutes
• Meditate
• No social media before noon

Enter habit 1:`,
      newState: STATES.SETTING_HABIT
    };
  }

  async handleOnboardingMenu(user, message) {
    // This state is no longer used in the sequential flow
    // but kept for backward compatibility
    await User.updateStatus(user.id, USER_STATUS.ACTIVE);
    await User.updateState(user.id, STATES.MAIN_MENU);
    return await this.handleMainMenu(user, '');
  }

  async handleSettingHabit(user, message) {
    const habitName = message.trim().substring(0, HABITS.MAX_NAME_LENGTH);
    const { habitNumber } = user.session_context;

    // Allow skipping habits by typing 'skip' or leaving empty during onboarding
    if (user.status === USER_STATUS.ONBOARDING && 
        (message.toLowerCase().trim() === 'skip' || message.trim() === '')) {
      // Move to next habit or finish if at habit 5
      if (habitNumber < 5) {
        const nextHabitNumber = habitNumber + 1;
        await User.updateState(user.id, STATES.SETTING_HABIT, { habitNumber: nextHabitNumber });
        return {
          message: `⏭️ Skipped habit ${habitNumber}

📝 Habit ${nextHabitNumber} of 5

What habit do you want to track?
(Type 'skip' to skip this one)

Enter habit ${nextHabitNumber}:`,
          newState: STATES.SETTING_HABIT
        };
      } else {
        // Finished all 5 habits, check if any were added
        const habits = await Habit.findByUserId(user.id);
        if (habits.length === 0) {
          // Must set at least one habit
          await User.updateState(user.id, STATES.SETTING_HABIT, { habitNumber: 1 });
          return {
            message: `❌ You need at least one habit to continue!

📝 Habit 1 of 5

Please enter at least one habit to track:

Enter habit 1:`,
            newState: STATES.SETTING_HABIT
          };
        }
        // Complete onboarding
        await User.updateStatus(user.id, USER_STATUS.ACTIVE);
        await User.updateState(user.id, STATES.MAIN_MENU);
        return {
          message: `🎉 Excellent! You're all set up!

Your ${habits.length} habit${habits.length > 1 ? 's are' : ' is'} ready to track:
${habits.map(h => `${h.habit_number}. ${h.habit_name}`).join('\n')}

Type 'menu' anytime to see your options.`,
          newState: STATES.MAIN_MENU
        };
      }
    }

    if (habitName.length < 1) {
      return {
        message: `Please enter a valid habit name (at least 1 character):

${user.status === USER_STATUS.ONBOARDING ? '(Or type "skip" to skip this habit)' : ''}`,
        newState: STATES.SETTING_HABIT
      };
    }

    await Habit.upsert(user.id, habitNumber, habitName);
    await AuditLog.log(user.id, AUDIT_EVENTS.HABIT_CREATED, { habitNumber, habitName });

    // If in onboarding, automatically progress to next habit
    if (user.status === USER_STATUS.ONBOARDING) {
      if (habitNumber < 5) {
        // Move to next habit
        const nextHabitNumber = habitNumber + 1;
        await User.updateState(user.id, STATES.SETTING_HABIT, { habitNumber: nextHabitNumber });
        return {
          message: `✅ Habit ${habitNumber} saved: "${habitName}"

📝 Habit ${nextHabitNumber} of 5

What's your next habit to track?
(Type 'skip' to skip this one)

Enter habit ${nextHabitNumber}:`,
          newState: STATES.SETTING_HABIT
        };
      } else {
        // Finished all 5 habits
        const habits = await Habit.findByUserId(user.id);
        await User.updateStatus(user.id, USER_STATUS.ACTIVE);
        await User.updateState(user.id, STATES.MAIN_MENU);
        return {
          message: `✅ Habit ${habitNumber} saved: "${habitName}"

🎉 Excellent! All habits set up!

Your ${habits.length} habit${habits.length > 1 ? 's' : ''} to track:
${habits.map(h => `${h.habit_number}. ${h.habit_name}`).join('\n')}

Type 'menu' to see your options or start logging!`,
          newState: STATES.MAIN_MENU
        };
      }
    } else {
      // Not in onboarding (editing from settings)
      await User.updateState(user.id, STATES.SETTINGS_MENU);
      return {
        message: `✅ Habit #${habitNumber} updated: "${habitName}"

Type 'menu' to return to the main menu.`,
        newState: STATES.SETTINGS_MENU
      };
    }
  }

  /*******************************************************************
   * LOCKED: HABIT LOGGING INPUT PARSER - DO NOT MODIFY
   * This parsing logic handles both "1y 2n 3y" and "1,3,5" formats.
   * Critical for user experience - any changes break habit tracking.
   *******************************************************************/
  async handleLoggingHabits(user, message) {
    const input = message.toLowerCase().trim();

    if (input === 'menu' || input === 'back') {
      await User.updateState(user.id, STATES.MAIN_MENU);
      return await this.handleMainMenu(user, '');
    }

    // Parse habit completions - support both formats:
    // Format 1: "1y 2n 3y" (y=yes/completed, n=no/not completed)
    // Format 2: "1,3,5" or "135" (numbers only = completed)
    
    const habits = await Habit.findByUserId(user.id);
    const today = moment.tz(user.timezone).format('YYYY-MM-DD');
    let logsToCreate = [];
    
    // Check for "1y 2n 3y" format first
    const ynMatches = input.match(/(\d+)([yn])/g);
    if (ynMatches && ynMatches.length > 0) {
      // Process "1y 2n 3y" format
      ynMatches.forEach(match => {
        const [, num, yn] = match.match(/(\d+)([yn])/);
        const habitNumber = parseInt(num);
        if (habitNumber >= 1 && habitNumber <= 5) {
          logsToCreate.push({
            habitNumber,
            completed: yn === 'y'
          });
        }
      });
    } else {
      // Fall back to numbers-only format "1,3,5" or "135"
      const completed = new Set();
      const numbers = input.match(/\d/g);
      if (numbers) {
        numbers.forEach(n => {
          const num = parseInt(n);
          if (num >= 1 && num <= 5) {
            completed.add(num);
          }
        });
      }
      
      // Create logs for all habits (completed=true for numbers in set, false for others)
      for (const habit of habits) {
        logsToCreate.push({
          habitNumber: habit.habit_number,
          completed: completed.has(habit.habit_number)
        });
      }
    }

    if (logsToCreate.length > 0) {
      // Apply the logs
      for (const logEntry of logsToCreate) {
        const habit = habits.find(h => h.habit_number === logEntry.habitNumber);
        if (habit) {
          await Habit.logHabit(user.id, habit.id, today, logEntry.completed);
        }
      }

      await AuditLog.log(user.id, AUDIT_EVENTS.HABIT_LOGGED, { 
        date: today, 
        completed: logsToCreate.filter(log => log.completed).map(log => log.habitNumber) 
      });

      /*******************************************************************
       * LOCKED: COMPLETION SCREEN TRIGGER LOGIC - DO NOT MODIFY
       * Triggers completion screen ONLY when ALL habits are logged.
       * This exact logic ensures users see progress screen at right time.
       *******************************************************************/
      // Check if all habits are fully logged (completion screen)
      const updatedLogs = await Habit.getTodayLogs(user.id, user.timezone);
      const hasUnloggedHabits = updatedLogs.some(h => h.completed === null);
      
      // DEBUG: Log completion check details
      console.log('🔍 COMPLETION CHECK:', {
        userId: user.id,
        totalHabits: updatedLogs.length,
        unloggedCount: updatedLogs.filter(h => h.completed === null).length,
        hasUnloggedHabits,
        habitStatuses: updatedLogs.map(h => ({ 
          number: h.habit_number, 
          name: h.habit_name, 
          completed: h.completed 
        }))
      });
      
      if (!hasUnloggedHabits) {
        // All habits logged - show completion screen
        console.log('🎉 TRIGGERING COMPLETION SCREEN');
        return await this.showCompletionScreen(user, updatedLogs);
      }
      
      // Stay in logging submenu and show updated status
      return await this.showTodayHabits(user);
    }

    return await this.showTodayHabits(user);
  }

  /*******************************************************************
   * LOCKED: LOGGING SUBMENU DISPLAY - DO NOT MODIFY
   * Shows real-time status with ✅/❌/⚠️ icons.
   * Format is critical for user experience.
   *******************************************************************/
  async showTodayHabits(user) {
    const todayLogs = await Habit.getTodayLogs(user.id, user.timezone);
    
    if (todayLogs.length === 0) {
      // No habits - redirect to habit setup
      await User.updateStatus(user.id, USER_STATUS.ONBOARDING);
      await User.updateState(user.id, STATES.SETTING_HABIT, { habitNumber: 1 });
      return {
        message: `🎯 You need to set up habits first!

📝 Habit 1 of 5

What's the first habit you want to track?

Examples:
• Drink 8 glasses of water
• Exercise for 30 minutes
• Read for 20 minutes
• Meditate
• No social media before noon

Enter habit 1:`,
        newState: STATES.SETTING_HABIT
      };
    }

    /*******************************************************************
     * LOCKED: REAL-TIME STATUS ICONS - DO NOT MODIFY
     * ⚠️ = Not logged, ✅ = Completed, ❌ = Not completed
     * These icons provide instant visual feedback to users.
     *******************************************************************/
    const habitList = todayLogs.map(h => {
      let statusIcon = '⚠️'; // Not logged (warning)
      if (h.completed === true) {
        statusIcon = '✅'; // Completed
      } else if (h.completed === false) {
        statusIcon = '❌'; // Not completed
      }
      return `${statusIcon} ${h.habit_number}. ${h.habit_name}`;
    }).join('\n');

    return {
      message: `📋 Today's Habits:
${habitList}

Which habits did you complete today?
Reply in format: "1y 2n 3y" (y=yes, n=no)
Type 'menu' to go back.`,
      newState: STATES.LOGGING_HABITS
    };
  }

  async handleViewingProgress(user, message) {
    await User.updateState(user.id, STATES.MAIN_MENU);
    return await this.showProgress(user);
  }

  async showProgress(user) {
    const progress = await Habit.getProgress(user.id, 7, user.timezone);
    
    if (progress.length === 0) {
      // No habits - redirect to habit setup
      await User.updateStatus(user.id, USER_STATUS.ONBOARDING);
      await User.updateState(user.id, STATES.SETTING_HABIT, { habitNumber: 1 });
      return {
        message: `🎯 No habits to show progress for!

Let's set up your first habit:

📝 Habit 1 of 5

What's the first habit you want to track?

Examples:
• Drink 8 glasses of water
• Exercise for 30 minutes
• Read for 20 minutes
• Meditate
• No social media before noon

Enter habit 1:`,
        newState: STATES.SETTING_HABIT
      };
    }

    const progressText = progress.map(h => {
      const bar = this.createProgressBar(h.completion_rate);
      return `${h.habit_number}. ${h.habit_name}
   ${bar} ${h.completion_rate}% (${h.completed_days}/${h.total_days} days)`;
    }).join('\n\n');

    return {
      message: `📊 Your 7-Day Progress:

${progressText}

Keep building those habits! 🚀

Type 'menu' to return.`,
      newState: STATES.MAIN_MENU
    };
  }

  createProgressBar(percentage) {
    const filled = Math.round(percentage / 10);
    const empty = 10 - filled;
    return '▓'.repeat(filled) + '░'.repeat(empty);
  }

  async handleSettingsMenu(user, message) {
    const input = message.toLowerCase().trim();

    if (input === 'menu' || input === 'back') {
      await User.updateState(user.id, STATES.MAIN_MENU);
      return await this.handleMainMenu(user, '');
    }

    const habitNumber = parseInt(input);
    if (habitNumber >= 1 && habitNumber <= 5) {
      await User.updateState(user.id, STATES.SETTING_HABIT, { habitNumber });
      return {
        message: `Enter new name for habit #${habitNumber} (or 'delete' to remove it):`,
        newState: STATES.SETTING_HABIT
      };
    }

    if (input === '6' || input === 'timezone') {
      await User.updateState(user.id, STATES.AWAITING_TIMEZONE);
      return {
        message: `Current timezone: ${user.timezone}

Enter new timezone (e.g., America/New_York, Europe/London):`,
        newState: STATES.AWAITING_TIMEZONE
      };
    }

    const habits = await Habit.findByUserId(user.id);
    return await this.showSettingsMenu(user, habits);
  }

  async showSettingsMenu(user, habits = null) {
    if (!habits) {
      habits = await Habit.findByUserId(user.id);
    }

    return {
      message: `⚙️ Settings

📝 Edit Habits:
${Array.from({ length: 5 }, (_, i) => {
  const habit = habits.find(h => h.habit_number === i + 1);
  return `${i + 1}️⃣ ${habit ? habit.habit_name : '[empty]'}`;
}).join('\n')}

6️⃣ Change timezone (current: ${user.timezone})

Reply with a number to edit, or 'menu' to go back.`,
      newState: STATES.SETTINGS_MENU
    };
  }

  // DEVELOPER TEST COMMAND: Reset user to clean slate for testing
  async handleResetTest(user) {
    try {
      const pool = require('../db/connection');
      
      // Clear ALL habit logs for this user
      const result = await pool.query(
        'DELETE FROM habit_logs WHERE user_id = $1',
        [user.id]
      );
      
      // Reset user state to MAIN_MENU
      await User.updateState(user.id, STATES.MAIN_MENU, {});
      
      // Return main menu with clean slate
      const response = await this.handleMainMenu(user, '');
      
      // Add reset confirmation to the message
      const resetMessage = `🧪 TEST RESET COMPLETE
Cleared ${result.rowCount} habit logs

${response.message}`;
      
      return {
        message: resetMessage,
        newState: response.newState
      };
      
    } catch (error) {
      logger.error('Reset test error', { error: error.message });
      return {
        message: '❌ Reset failed. Please try again.',
        newState: user.current_state
      };
    }
  }

  /*******************************************************************
   * LOCKED: COMPLETION SCREEN - DO NOT MODIFY
   * Dual-message system: Message 1 = shareable content,
   * Message 2 = navigation. sendFollowUp flag triggers Message 2.
   *******************************************************************/
  // COMPLETION SCREEN: Shows when all habits are fully logged
  async showCompletionScreen(user, todayLogs) {
    try {
      // Format habit list with final status
      const habitsList = todayLogs.map(h => {
        const statusIcon = h.completed === true ? '✅' : '❌';
        return `${statusIcon} ${h.habit_number}. ${h.habit_name}`;
      }).join('\n');

      // Calculate streak and weekly progress (placeholder values for now)
      const currentStreak = 3; // TODO: Calculate actual streak
      const weeklyProgress = '5/7'; // TODO: Calculate actual weekly progress

      // MESSAGE 1: Shareable completion content
      const completionMessage = `🎉 DAY COMPLETE! 

📋 Your Habits (5/5):
${habitsList}

💪 "Success is the sum of small efforts repeated day in and day out." - Robert Collier

🔥 Current streak: ${currentStreak} days
📈 This week: ${weeklyProgress} days complete`;

      // Set state for completion screen navigation
      await User.updateState(user.id, STATES.COMPLETION_SCREEN || 'COMPLETION_SCREEN');

      return {
        message: completionMessage,
        newState: 'COMPLETION_SCREEN',
        sendFollowUp: true // Flag to send MESSAGE 2 immediately
      };

    } catch (error) {
      logger.error('Completion screen error', { error: error.message });
      // Fallback to regular submenu if error
      return await this.showTodayHabits(user);
    }
  }

  /*******************************************************************
   * LOCKED: COMPLETION SCREEN NAVIGATION - DO NOT MODIFY
   * Handles user input from completion screen.
   * Options: 1 = Edit habits, 2 = Back to menu
   *******************************************************************/
  // Handle completion screen navigation (MESSAGE 2)
  async handleCompletionScreen(user, message) {
    const input = message.toLowerCase().trim();

    if (input === '1' || input === 'edit') {
      // Go back to logging submenu to edit habits
      await User.updateState(user.id, STATES.LOGGING_HABITS);
      return await this.showTodayHabits(user);
    } else if (input === '2' || input === 'menu') {
      // Go back to main menu
      await User.updateState(user.id, STATES.MAIN_MENU);
      return await this.handleMainMenu(user, '');
    }

    // Show MESSAGE 2: Instructions + navigation
    return {
      message: `Click and hold the previous message to forward or share.

1️⃣ Edit today's habits
2️⃣ Back to menu

Make a selection (reply with a number).`,
      newState: STATES.COMPLETION_SCREEN
    };
  }

  // MAIN STATS MENU HANDLER
  async handleStatsMenu(user, message) {
    const input = message.toLowerCase().trim();

    // Check total days first to determine menu structure
    const totalDays = await Habit.getTotalDaysLogged(user.id);
    const has100Days = totalDays >= 100;

    if (input === '1' || input === '30') {
      await User.updateState(user.id, STATES.STATS_30_DAY);
      return await this.handleStats30Day(user, '');
    } else if (input === '2') {
      if (has100Days) {
        // User has 100+ days and selected option 2 = 100 Day Overview
        await User.updateState(user.id, STATES.STATS_100_DAY);
        return await this.handleStats100Day(user, '');
      } else {
        // User has < 100 days and selected option 2 = Back to Menu
        await User.updateState(user.id, STATES.MAIN_MENU);
        return await this.handleMainMenu(user, '');
      }
    } else if (input === '3' && has100Days) {
      // Option 3 only exists when user has 100+ days = Back to Menu
      await User.updateState(user.id, STATES.MAIN_MENU);
      return await this.handleMainMenu(user, '');
    } else if (input === '100') {
      // Direct "100" command - only allow if user has 100+ days
      if (has100Days) {
        await User.updateState(user.id, STATES.STATS_100_DAY);
        return await this.handleStats100Day(user, '');
      }
    } else if (input === 'back' || input === 'menu') {
      await User.updateState(user.id, STATES.MAIN_MENU);
      return await this.handleMainMenu(user, '');
    }

    // Get all stats for main menu (totalDays already fetched above)
    const currentStreak = await Habit.getCurrentStreak(user.id, user.timezone);
    const bestStreak = await Habit.getBestStreak(user.id);
    const perfectDaysStats = await Habit.getPerfectDaysStats(user.id, user.timezone);
    const weeklyPerformance = await Habit.getWeeklyPerformance(user.id, user.timezone);
    const habitBreakdown = await Habit.getHabitBreakdown(user.id);
    
    // Get current day number
    const currentDay = moment().tz(user.timezone || 'UTC').format('D');
    
    // Generate motivational quote from database
    const motivationalQuote = '"Success is the sum of small efforts repeated day in and day out." - Robert Collier';

    // Build habit breakdown text with completion status icons
    const habitBreakdownText = habitBreakdown.map(habit => {
      const completionRate = habit.completion_rate || 0;
      const icon = completionRate >= 70 ? '✅' : '❌';
      return `${icon} ${habit.habit_name}: ${completionRate}%`;
    }).join('\n');

    // Determine if 100-day option should be shown (has100Days already declared above)
    const optionText = has100Days ? 
      `1️⃣ 30-Day Stats
2️⃣ 100 Day Overview
3️⃣ Back to Menu` :
      `1️⃣ 30-Day Stats
2️⃣ Back to Menu`;

    return {
      message: `🎯 MY STATS

📅 Day ${currentDay}
🔥 Current Streak: ${currentStreak} days
🏆 Best Streak: ${bestStreak} days
✅ Perfect Days: ${perfectDaysStats.perfect_days}/${perfectDaysStats.total_days} (${perfectDaysStats.completion_rate}%)

📈 RECENT PERFORMANCE
This Week: ${weeklyPerformance.thisWeek.perfect_days}/7 days (${weeklyPerformance.thisWeek.completion_rate}%)
Last Week: ${weeklyPerformance.lastWeek.perfect_days}/7 days (${weeklyPerformance.lastWeek.completion_rate}%)

🎯 HABIT BREAKDOWN
${habitBreakdownText}

💪 ${motivationalQuote}

⚔️ Powered by Lock In

${optionText}

Make a selection (reply with a number).`,
      newState: STATES.STATS_MENU
    };
  }

  // 30-DAY STATS SUBMENU HANDLER
  async handleStats30Day(user, message) {
    const input = message.toLowerCase().trim();

    if (input === '1' || input === 'back') {
      await User.updateState(user.id, STATES.STATS_MENU);
      return await this.handleStatsMenu(user, '');
    }

    // Get 30-day specific stats
    const perfectDaysStats = await Habit.getPerfectDaysStats(user.id, user.timezone);
    const habitBreakdown = await Habit.getHabitBreakdown(user.id);
    const bestStreak = await Habit.getBestStreak(user.id);
    
    // Find top and worst performing habits
    const sortedHabits = habitBreakdown.sort((a, b) => (b.completion_rate || 0) - (a.completion_rate || 0));
    const topHabit = sortedHabits[0] || { habit_name: 'N/A', completion_rate: 0 };
    const worstHabit = sortedHabits[sortedHabits.length - 1] || { habit_name: 'N/A', completion_rate: 0 };
    
    // Calculate recovery rate (simplified)
    const recoveryRate = Math.min(95, Math.max(0, perfectDaysStats.completion_rate));

    return {
      message: `📅 30-DAY STATS

Completion: ${perfectDaysStats.completion_rate}%
Perfect Days: ${perfectDaysStats.perfect_days}/30
Longest Streak: ${bestStreak >= 30 ? '30+' : bestStreak}
Recovery after misses: ${recoveryRate}%
Top Habit: ${topHabit.habit_name} ${Math.round((topHabit.completion_rate || 0) * 30 / 100)}/30
Needs Work: ${worstHabit.habit_name} ${Math.round((worstHabit.completion_rate || 0) * 30 / 100)}/30

1️⃣ Back to Stats

Make a selection (reply with a number).`,
      newState: STATES.STATS_30_DAY
    };
  }

  // 100-DAY OVERVIEW SUBMENU HANDLER
  async handleStats100Day(user, message) {
    const input = message.toLowerCase().trim();

    if (input === '1' || input === 'back') {
      await User.updateState(user.id, STATES.STATS_MENU);
      return await this.handleStatsMenu(user, '');
    }

    // Get 100-day stats
    const perfectDaysStats = await Habit.getPerfectDaysStats(user.id, user.timezone);
    const habitBreakdown = await Habit.getHabitBreakdown(user.id);
    const currentStreak = await Habit.getCurrentStreak(user.id, user.timezone);
    const bestStreak = await Habit.getBestStreak(user.id);

    // Generate 10x10 grid (simplified for demo - would need actual daily data)
    let gridText = '';
    for (let row = 0; row < 10; row++) {
      let rowText = '';
      for (let col = 0; col < 10; col++) {
        // Simplified: use completion rate to determine grid
        const dayIndex = row * 10 + col + 1;
        const isComplete = Math.random() < (perfectDaysStats.completion_rate / 100);
        rowText += isComplete ? '✅' : '❌';
      }
      gridText += rowText + '\n';
    }

    // Build mastery levels text
    const masteryText = habitBreakdown.map(habit => {
      const completionCount = Math.round((habit.completion_rate || 0));
      let masteryLevel = 'BEGINNER';
      if (completionCount >= 90) masteryLevel = 'EXPERT';
      else if (completionCount >= 70) masteryLevel = 'ADVANCED';
      else if (completionCount >= 50) masteryLevel = 'INTERMEDIATE';
      
      return `🎯 ${habit.habit_name}: ${completionCount}/100 (${masteryLevel})`;
    }).join('\n');

    return {
      message: `🏆 100-DAY MILESTONE

Total Days: 100
Perfect Days: ${perfectDaysStats.perfect_days}/100 (${perfectDaysStats.completion_rate}%)
Longest Streak: ${bestStreak} days
Current Streak: ${currentStreak} days

📊 YOUR 100-DAY JOURNEY
(1 checkmark = perfect day with all tasks)

${gridText}

🎯 MASTERY LEVELS
${masteryText}

💪 "100 days of progress!"

1️⃣ Back to Stats

Make a selection (reply with a number).`,
      newState: STATES.STATS_100_DAY
    };
  }
}

module.exports = new StateMachine();