const pool = require('../db/connection');
const logger = require('../config/logger');
const { validateAccessCode } = require('../utils/codeGenerator');

class PaymentService {
  constructor() {
    this.testMode = process.env.PAYMENT_TEST_MODE === 'true';
  }

  /**
   * Check if user has valid payment/access
   */
  async checkUserAccess(phone) {
    try {
      // ALWAYS check for active payment first - no test mode bypass
      // Check if phone is linked to a paid user
      const result = await pool.query(
        `SELECT pu.*, ac.used_at 
         FROM paid_users pu
         LEFT JOIN access_codes ac ON pu.id = ac.paid_user_id
         WHERE pu.phone = $1 AND pu.status = 'active'`,
        [phone]
      );

      if (result.rows.length > 0) {
        logger.info('User has active paid subscription', { phone });
        return { 
          hasAccess: true, 
          paidUser: result.rows[0],
          testMode: this.testMode 
        };
      }

      // No payment found - deny access regardless of test mode
      logger.info('User has no active subscription', { phone, testMode: this.testMode });
      return { hasAccess: false, testMode: this.testMode };
      
    } catch (error) {
      logger.error('Error checking user access', { error: error.message });
      // In case of error, deny access
      return { hasAccess: false, error: true };
    }
  }

  /**
   * Activate access code and link to phone number
   */
  async activateAccessCode(phone, code) {
    const client = await pool.connect();
    
    try {
      // Validate code format
      if (!validateAccessCode(code)) {
        return {
          success: false,
          message: 'Invalid access code format. Please check and try again.'
        };
      }

      await client.query('BEGIN');

      // Find the access code and associated paid user
      const codeResult = await client.query(
        `SELECT ac.*, pu.* 
         FROM access_codes ac
         JOIN paid_users pu ON ac.paid_user_id = pu.id
         WHERE UPPER(ac.code) = UPPER($1) AND ac.is_active = true`,
        [code]
      );

      if (codeResult.rows.length === 0) {
        await client.query('ROLLBACK');
        return {
          success: false,
          message: 'Access code not found or already used. Please check your code.'
        };
      }

      const accessCode = codeResult.rows[0];

      // Check if code is already used
      if (accessCode.used_by_phone) {
        await client.query('ROLLBACK');
        return {
          success: false,
          message: 'This access code has already been activated.'
        };
      }

      // Update access code as used
      await client.query(
        `UPDATE access_codes 
         SET used_by_phone = $1, used_at = NOW()
         WHERE id = $2`,
        [phone, accessCode.id]
      );

      // Link phone to paid user
      await client.query(
        `UPDATE paid_users 
         SET phone = $1, updated_at = NOW()
         WHERE id = $2`,
        [phone, accessCode.paid_user_id]
      );

      // Update user status to unlocked
      await client.query(
        `UPDATE users 
         SET status = 'ONBOARDING',
             updated_at = NOW()
         WHERE phone = $1`,
        [phone]
      );

      await client.query('COMMIT');

      logger.info('Access code activated successfully', {
        phone,
        code,
        subscriptionType: accessCode.subscription_type
      });

      return {
        success: true,
        message: `Success! Your ${accessCode.subscription_type} subscription is now active. Let's set up your habits!`,
        subscriptionType: accessCode.subscription_type,
        isAffiliate: accessCode.is_affiliate,
        affiliateCode: accessCode.affiliate_code
      };
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error activating access code', { error: error.message });
      return {
        success: false,
        message: 'An error occurred while activating your code. Please try again.'
      };
    } finally {
      client.release();
    }
  }

  /**
   * Get paywall message for locked users
   */
  getPaywallMessage() {
    const checkoutUrl = process.env.FASTSPRING_CHECKOUT_URL || 'https://habittracker.fastspring.com/checkout';
    
    return `🚫 ACCESS REQUIRED

Habit Tracker is a premium service that helps you build lasting habits with daily tracking and progress insights.

💰 **PRICING:**
• Monthly: $5/month
• Yearly: $30/year (Save $30!)

🎉 **YEARLY BONUS:** 
Automatic enrollment in our 30% affiliate program!

🔐 **GET ACCESS:**
1. Purchase subscription: ${checkoutUrl}
2. Check your email for access code
3. Text: START HABIT-XXXXXX

❓ Questions? <EMAIL>

⚠️ All bot functions require active subscription`;
  }

  /**
   * Process affiliate referral
   */
  async processAffiliateReferral(affiliateCode, newUserId, orderAmount) {
    try {
      // Find affiliate by code
      const affiliateResult = await pool.query(
        'SELECT id, commission_rate FROM paid_users WHERE UPPER(affiliate_code) = UPPER($1) AND is_affiliate = true',
        [affiliateCode]
      );

      if (affiliateResult.rows.length === 0) {
        logger.warn('Affiliate code not found', { affiliateCode });
        return false;
      }

      const affiliate = affiliateResult.rows[0];
      const commissionAmount = (orderAmount * affiliate.commission_rate) / 100;

      // Create referral record
      await pool.query(
        `INSERT INTO affiliate_referrals (
          affiliate_id, referred_user_id, commission_amount, 
          referral_amount, commission_status
        ) VALUES ($1, $2, $3, $4, 'pending')`,
        [affiliate.id, newUserId, commissionAmount, orderAmount]
      );

      logger.info('Affiliate referral processed', {
        affiliateId: affiliate.id,
        commissionAmount,
        orderAmount
      });

      return true;
    } catch (error) {
      logger.error('Error processing affiliate referral', { error: error.message });
      return false;
    }
  }

  /**
   * Get user subscription details
   */
  async getSubscriptionDetails(phone) {
    try {
      const result = await pool.query(
        `SELECT 
          subscription_type,
          status,
          paid_at,
          expires_at,
          is_affiliate,
          affiliate_code
         FROM paid_users
         WHERE phone = $1 AND status = 'active'`,
        [phone]
      );

      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error getting subscription details', { error: error.message });
      return null;
    }
  }

  /**
   * Check and handle expired subscriptions
   */
  async checkExpiredSubscriptions() {
    try {
      const result = await pool.query(
        `UPDATE paid_users 
         SET status = 'expired', updated_at = NOW()
         WHERE status = 'active' 
         AND expires_at IS NOT NULL 
         AND expires_at < NOW()
         RETURNING id, email`
      );

      if (result.rows.length > 0) {
        logger.info('Expired subscriptions marked', { count: result.rows.length });
        
        // Queue expiration emails
        for (const user of result.rows) {
          await pool.query(
            `INSERT INTO email_queue (to_email, subject, template, template_data)
             VALUES ($1, 'Subscription Expired', 'subscription_expired', '{}')`,
            [user.email]
          );
        }
      }
    } catch (error) {
      logger.error('Error checking expired subscriptions', { error: error.message });
    }
  }

  /**
   * Start subscription checker (runs daily)
   */
  startSubscriptionChecker() {
    // Check on startup
    this.checkExpiredSubscriptions();
    
    // Check every 24 hours
    setInterval(() => {
      this.checkExpiredSubscriptions();
    }, 24 * 60 * 60 * 1000);
  }
}

module.exports = new PaymentService();