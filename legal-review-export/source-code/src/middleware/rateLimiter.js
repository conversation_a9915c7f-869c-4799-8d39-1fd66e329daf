const rateLimit = require('express-rate-limit');
const User = require('../models/User');
const AuditLog = require('../models/AuditLog');
const { RATE_LIMIT, AUDIT_EVENTS } = require('../config/constants');
const logger = require('../config/logger');

// Create rate limiter
const createRateLimiter = () => {
  return rateLimit({
    windowMs: RATE_LIMIT.WINDOW_MS,
    max: RATE_LIMIT.MAX_REQUESTS,
    message: RATE_LIMIT.MESSAGE,
    standardHeaders: true,
    legacyHeaders: false,
    
    // Custom key generator based on phone number
    keyGenerator: (req) => {
      return req.body?.From || req.ip;
    },
    
    // Custom handler for rate limit exceeded
    handler: async (req, res) => {
      const phone = req.body?.From;
      
      if (phone) {
        try {
          const user = await User.findByPhone(phone);
          if (user) {
            await AuditLog.log(user.id, AUDIT_EVENTS.RATE_LIMITED, {
              ip: req.ip,
              attempts: req.rateLimit.current
            });
          }
        } catch (error) {
          logger.error('Error logging rate limit', { error: error.message });
        }
      }
      
      logger.warn('Rate limit exceeded', { 
        phone: phone ? '[REDACTED]' : 'unknown',
        ip: req.ip 
      });
      
      const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Message>${RATE_LIMIT.MESSAGE}</Message>
</Response>`;
      
      res.status(429).type('text/xml').send(twiml);
    },
    
    // Skip successful requests from counting
    skipSuccessfulRequests: false,
    
    // Skip failed requests from counting
    skipFailedRequests: true
  });
};

// Per-user rate limiting (more granular)
const perUserRateLimiter = async (req, res, next) => {
  const phone = req.body?.From;
  
  if (!phone) {
    return next();
  }
  
  try {
    const user = await User.findByPhone(phone);
    
    if (user) {
      // Check if user has been making too many requests
      const recentLogs = await AuditLog.getByUser(user.id, 10);
      const rateLimitLogs = recentLogs.filter(log => 
        log.event_type === AUDIT_EVENTS.RATE_LIMITED &&
        new Date(log.timestamp) > new Date(Date.now() - 3600000) // Last hour
      );
      
      if (rateLimitLogs.length >= 3) {
        // User has been rate limited 3+ times in the last hour - apply stricter limits
        logger.warn('Applying strict rate limit to user', { userId: user.id });
        
        const twiml = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Message>You have been temporarily restricted due to excessive requests. Please try again later.</Message>
</Response>`;
        
        return res.status(429).type('text/xml').send(twiml);
      }
    }
  } catch (error) {
    logger.error('Error in per-user rate limiter', { error: error.message });
  }
  
  next();
};

module.exports = {
  globalRateLimiter: createRateLimiter(),
  perUserRateLimiter
};