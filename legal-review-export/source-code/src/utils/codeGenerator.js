const crypto = require('crypto');
const pool = require('../db/connection');

/**
 * Generate a unique access code in format HABIT-XXXXXX
 */
async function generateAccessCode() {
  let code;
  let isUnique = false;
  let attempts = 0;
  const maxAttempts = 10;

  while (!isUnique && attempts < maxAttempts) {
    // Generate random 6-character alphanumeric string
    const randomPart = crypto.randomBytes(3).toString('hex').toUpperCase();
    code = `HABIT-${randomPart}`;

    // Check if code already exists
    const result = await pool.query(
      'SELECT id FROM paid_users WHERE access_code = $1',
      [code]
    );

    if (result.rows.length === 0) {
      isUnique = true;
    }

    attempts++;
  }

  if (!isUnique) {
    throw new Error('Failed to generate unique access code');
  }

  return code;
}

/**
 * Generate a unique affiliate code in format AFF-XXXXXX
 */
async function generateAffiliateCode() {
  let code;
  let isUnique = false;
  let attempts = 0;
  const maxAttempts = 10;

  while (!isUnique && attempts < maxAttempts) {
    // Generate random 6-character alphanumeric string
    const randomPart = crypto.randomBytes(3).toString('hex').toUpperCase();
    code = `AFF-${randomPart}`;

    // Check if code already exists
    const result = await pool.query(
      'SELECT id FROM paid_users WHERE affiliate_code = $1',
      [code]
    );

    if (result.rows.length === 0) {
      isUnique = true;
    }

    attempts++;
  }

  if (!isUnique) {
    throw new Error('Failed to generate unique affiliate code');
  }

  return code;
}

/**
 * Validate access code format
 */
function validateAccessCode(code) {
  if (!code || typeof code !== 'string') {
    return false;
  }

  // Check format: HABIT-XXXXXX (6 alphanumeric characters after HABIT-)
  const pattern = /^HABIT-[A-Z0-9]{6}$/;
  return pattern.test(code.toUpperCase());
}

/**
 * Validate affiliate code format
 */
function validateAffiliateCode(code) {
  if (!code || typeof code !== 'string') {
    return false;
  }

  // Check format: AFF-XXXXXX (6 alphanumeric characters after AFF-)
  const pattern = /^AFF-[A-Z0-9]{6}$/;
  return pattern.test(code.toUpperCase());
}

module.exports = {
  generateAccessCode,
  generateAffiliateCode,
  validateAccessCode,
  validateAffiliateCode
};