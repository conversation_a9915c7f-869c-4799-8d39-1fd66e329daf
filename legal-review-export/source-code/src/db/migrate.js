const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

async function migrate() {
  const client = await pool.connect();
  
  try {
    console.log('Running database migrations...');
    
    // Read and execute schema
    const schemaPath = path.join(__dirname, 'schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    await client.query(schema);
    
    console.log('✅ Database migrations completed successfully');
    
    // Insert sample access codes for testing (only in development)
    if (process.env.NODE_ENV !== 'production') {
      const testCodes = [
        'TEST123',
        'DEMO456',
        'TRIAL789'
      ];
      
      for (const code of testCodes) {
        await client.query(
          `INSERT INTO access_codes (code, expires_at) 
           VALUES ($1, NOW() + INTERVAL '30 days')
           ON CONFLICT (code) DO NOTHING`,
          [code]
        );
      }
      
      console.log('✅ Test access codes created');
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

async function rollback() {
  const client = await pool.connect();
  
  try {
    console.log('Rolling back database...');
    
    await client.query(`
      DROP TABLE IF EXISTS audit_logs CASCADE;
      DROP TABLE IF EXISTS habit_logs CASCADE;
      DROP TABLE IF EXISTS habits CASCADE;
      DROP TABLE IF EXISTS access_codes CASCADE;
      DROP TABLE IF EXISTS users CASCADE;
      DROP FUNCTION IF EXISTS update_updated_at_column CASCADE;
    `);
    
    console.log('✅ Database rollback completed');
    
  } catch (error) {
    console.error('❌ Rollback failed:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// Run migration or rollback based on command line argument
const command = process.argv[2];

if (command === 'rollback') {
  rollback().catch(console.error);
} else {
  migrate().catch(console.error);
}