const crypto = require('crypto');
const pool = require('../db/connection');
const logger = require('../config/logger');
const emailService = require('../services/emailService');
const { generateAccessCode, generateAffiliateCode } = require('../utils/codeGenerator');

class FastSpringController {
  constructor() {
    this.testMode = process.env.PAYMENT_TEST_MODE === 'true';
    this.webhookSecret = process.env.FASTSPRING_WEBHOOK_SECRET;
  }

  /**
   * Main webhook handler for FastSpring events
   */
  async handleWebhook(req, res) {
    try {
      const signature = req.headers['x-fs-signature'];
      const payload = req.body;

      // Log webhook event
      await this.logWebhookEvent(payload, req.headers);

      // Verify webhook signature in production mode
      if (!this.testMode && !this.verifySignature(payload, signature)) {
        logger.error('Invalid FastSpring webhook signature');
        return res.status(401).json({ error: 'Invalid signature' });
      }

      // Process different event types
      const eventType = payload.events?.[0]?.type || payload.type;
      logger.info('Processing FastSpring webhook', { eventType, testMode: this.testMode });

      switch (eventType) {
        case 'order.completed':
          await this.handleOrderCompleted(payload);
          break;
        case 'subscription.activated':
          await this.handleSubscriptionActivated(payload);
          break;
        case 'subscription.deactivated':
          await this.handleSubscriptionDeactivated(payload);
          break;
        case 'subscription.charge.completed':
          await this.handleSubscriptionCharge(payload);
          break;
        case 'subscription.charge.failed':
          await this.handlePaymentFailed(payload);
          break;
        case 'return.created':
          await this.handleRefund(payload);
          break;
        default:
          logger.info('Unhandled FastSpring event type', { eventType });
      }

      res.status(200).json({ received: true });
    } catch (error) {
      logger.error('FastSpring webhook error', { error: error.message, stack: error.stack });
      res.status(500).json({ error: 'Webhook processing failed' });
    }
  }

  /**
   * Verify FastSpring webhook signature
   */
  verifySignature(payload, signature) {
    if (!this.webhookSecret || !signature) {
      return false;
    }

    const computedSignature = crypto
      .createHmac('sha256', this.webhookSecret)
      .update(JSON.stringify(payload))
      .digest('base64');

    return computedSignature === signature;
  }

  /**
   * Handle completed order (new purchase)
   */
  async handleOrderCompleted(payload) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      const orderData = payload.events?.[0]?.data || payload.data;
      const customer = orderData.customer;
      const items = orderData.items || [];

      for (const item of items) {
        // Determine subscription type from product
        const subscriptionType = this.getSubscriptionType(item.product);
        
        // Generate unique access code
        const accessCode = await generateAccessCode();
        
        // Generate affiliate code for yearly subscribers
        const affiliateCode = subscriptionType === 'yearly' ? await generateAffiliateCode() : null;

        // Create paid user record
        const paidUserResult = await client.query(
          `INSERT INTO paid_users (
            email, access_code, subscription_type, subscription_id,
            customer_id, amount_paid, currency, status,
            is_affiliate, affiliate_code, paid_at,
            fastspring_order_id, test_mode
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
          RETURNING *`,
          [
            customer.email,
            accessCode,
            subscriptionType,
            item.subscription,
            customer.id,
            item.total,
            orderData.currency,
            'active',
            subscriptionType === 'yearly',
            affiliateCode,
            new Date(),
            orderData.id,
            this.testMode
          ]
        );

        const paidUser = paidUserResult.rows[0];

        // Create access code record
        await client.query(
          `INSERT INTO access_codes (code, paid_user_id, is_active)
           VALUES ($1, $2, $3)`,
          [accessCode, paidUser.id, true]
        );

        // Log transaction
        await client.query(
          `INSERT INTO payment_transactions (
            paid_user_id, transaction_id, type, amount, currency,
            fastspring_order_id, transaction_date
          ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
          [
            paidUser.id,
            orderData.id,
            'payment',
            item.total,
            orderData.currency,
            orderData.id,
            new Date()
          ]
        );

        // Queue welcome email
        await this.queueWelcomeEmail(paidUser);

        logger.info('Created paid user from FastSpring order', {
          email: customer.email,
          subscriptionType,
          orderId: orderData.id
        });
      }

      // Mark webhook as processed
      await this.markWebhookProcessed(payload);

      await client.query('COMMIT');
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error processing order completed', { error: error.message });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Handle subscription activation
   */
  async handleSubscriptionActivated(payload) {
    try {
      const subscriptionData = payload.events?.[0]?.data || payload.data;
      
      await pool.query(
        `UPDATE paid_users 
         SET status = 'active', 
             fastspring_subscription_id = $1,
             updated_at = NOW()
         WHERE customer_id = $2`,
        [subscriptionData.id, subscriptionData.customer.id]
      );

      logger.info('Subscription activated', {
        subscriptionId: subscriptionData.id,
        customerId: subscriptionData.customer.id
      });
    } catch (error) {
      logger.error('Error handling subscription activation', { error: error.message });
      throw error;
    }
  }

  /**
   * Handle subscription deactivation (cancellation/expiry)
   */
  async handleSubscriptionDeactivated(payload) {
    try {
      const subscriptionData = payload.events?.[0]?.data || payload.data;
      const reason = subscriptionData.deactivationReason || 'cancelled';
      
      await pool.query(
        `UPDATE paid_users 
         SET status = $1,
             expires_at = NOW(),
             updated_at = NOW()
         WHERE fastspring_subscription_id = $2`,
        [reason === 'expired' ? 'expired' : 'cancelled', subscriptionData.id]
      );

      logger.info('Subscription deactivated', {
        subscriptionId: subscriptionData.id,
        reason
      });
    } catch (error) {
      logger.error('Error handling subscription deactivation', { error: error.message });
      throw error;
    }
  }

  /**
   * Handle successful subscription charge (renewal)
   */
  async handleSubscriptionCharge(payload) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      const chargeData = payload.events?.[0]?.data || payload.data;
      
      // Update subscription status
      await client.query(
        `UPDATE paid_users 
         SET status = 'active',
             updated_at = NOW()
         WHERE fastspring_subscription_id = $1`,
        [chargeData.subscription]
      );

      // Log transaction
      const userResult = await client.query(
        'SELECT id FROM paid_users WHERE fastspring_subscription_id = $1',
        [chargeData.subscription]
      );

      if (userResult.rows[0]) {
        await client.query(
          `INSERT INTO payment_transactions (
            paid_user_id, transaction_id, type, amount, currency,
            fastspring_reference, transaction_date
          ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
          [
            userResult.rows[0].id,
            chargeData.id,
            'payment',
            chargeData.total,
            chargeData.currency,
            chargeData.reference,
            new Date()
          ]
        );
      }

      await client.query('COMMIT');
      
      logger.info('Subscription charge processed', {
        subscriptionId: chargeData.subscription,
        amount: chargeData.total
      });
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error handling subscription charge', { error: error.message });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Handle failed payment
   */
  async handlePaymentFailed(payload) {
    try {
      const failureData = payload.events?.[0]?.data || payload.data;
      
      // Update user status
      await pool.query(
        `UPDATE paid_users 
         SET status = 'expired',
             updated_at = NOW()
         WHERE fastspring_subscription_id = $1`,
        [failureData.subscription]
      );

      // TODO: Send payment failure email
      
      logger.info('Payment failed', {
        subscriptionId: failureData.subscription,
        reason: failureData.reason
      });
    } catch (error) {
      logger.error('Error handling payment failure', { error: error.message });
      throw error;
    }
  }

  /**
   * Handle refund
   */
  async handleRefund(payload) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      const refundData = payload.events?.[0]?.data || payload.data;
      
      // Update user status
      await client.query(
        `UPDATE paid_users 
         SET status = 'expired',
             updated_at = NOW()
         WHERE fastspring_order_id = $1`,
        [refundData.original.id]
      );

      // Log refund transaction
      const userResult = await client.query(
        'SELECT id FROM paid_users WHERE fastspring_order_id = $1',
        [refundData.original.id]
      );

      if (userResult.rows[0]) {
        await client.query(
          `INSERT INTO payment_transactions (
            paid_user_id, transaction_id, type, amount, currency,
            fastspring_order_id, transaction_date
          ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
          [
            userResult.rows[0].id,
            refundData.id,
            'refund',
            -Math.abs(refundData.total),
            refundData.currency,
            refundData.id,
            new Date()
          ]
        );

        // Deactivate access code
        await client.query(
          `UPDATE access_codes 
           SET is_active = false 
           WHERE paid_user_id = $1`,
          [userResult.rows[0].id]
        );
      }

      await client.query('COMMIT');
      
      logger.info('Refund processed', {
        orderId: refundData.original.id,
        amount: refundData.total
      });
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error('Error handling refund', { error: error.message });
      throw error;
    } finally {
      client.release();
    }
  }

  /**
   * Determine subscription type from product ID
   */
  getSubscriptionType(productPath) {
    // Check product path or ID for monthly/yearly
    if (productPath.toLowerCase().includes('year')) {
      return 'yearly';
    }
    return 'monthly';
  }

  /**
   * Queue welcome email for new subscriber
   */
  async queueWelcomeEmail(paidUser) {
    try {
      const templateData = {
        accessCode: paidUser.access_code,
        subscriptionType: paidUser.subscription_type,
        affiliateCode: paidUser.affiliate_code,
        botPhone: process.env.TWILIO_PHONE_NUMBER || '+19035155547'
      };

      await pool.query(
        `INSERT INTO email_queue (to_email, subject, template, template_data, priority)
         VALUES ($1, $2, $3, $4, $5)`,
        [
          paidUser.email,
          'Welcome to Habit Tracker - Your Access Code',
          paidUser.subscription_type === 'yearly' ? 'welcome_yearly' : 'welcome_monthly',
          JSON.stringify(templateData),
          1
        ]
      );

      // Process email queue immediately
      await emailService.processQueue();
    } catch (error) {
      logger.error('Error queueing welcome email', { error: error.message });
    }
  }

  /**
   * Log webhook event for auditing
   */
  async logWebhookEvent(payload, headers) {
    try {
      await pool.query(
        `INSERT INTO webhook_events (event_type, payload, headers, source, test_mode)
         VALUES ($1, $2, $3, $4, $5)`,
        [
          payload.events?.[0]?.type || payload.type || 'unknown',
          JSON.stringify(payload),
          JSON.stringify(headers),
          'fastspring',
          this.testMode
        ]
      );
    } catch (error) {
      logger.error('Error logging webhook event', { error: error.message });
    }
  }

  /**
   * Mark webhook as processed
   */
  async markWebhookProcessed(payload) {
    try {
      const eventId = payload.events?.[0]?.id || payload.id;
      await pool.query(
        `UPDATE webhook_events 
         SET processed = true, processed_at = NOW()
         WHERE event_id = $1 OR payload->>'id' = $1`,
        [eventId]
      );
    } catch (error) {
      logger.error('Error marking webhook processed', { error: error.message });
    }
  }

  /**
   * Test endpoint to simulate webhook events
   */
  async testWebhook(req, res) {
    if (!this.testMode) {
      return res.status(403).json({ error: 'Test mode not enabled' });
    }

    try {
      const { eventType, email, subscriptionType = 'monthly' } = req.body;

      const testPayload = {
        events: [{
          type: eventType || 'order.completed',
          data: {
            id: `TEST-${Date.now()}`,
            customer: {
              id: `CUST-${Date.now()}`,
              email: email || '<EMAIL>'
            },
            items: [{
              product: subscriptionType === 'yearly' ? 'habit-tracker-yearly' : 'habit-tracker-monthly',
              total: subscriptionType === 'yearly' ? 30.00 : 5.00,
              subscription: `SUB-${Date.now()}`
            }],
            currency: 'USD',
            total: subscriptionType === 'yearly' ? 30.00 : 5.00
          }
        }]
      };

      // Process as regular webhook
      req.body = testPayload;
      await this.handleWebhook(req, res);
    } catch (error) {
      logger.error('Test webhook error', { error: error.message });
      res.status(500).json({ error: error.message });
    }
  }
}

module.exports = new FastSpringController();