const User = require('../models/User');
const stateMachine = require('../services/stateMachinePaymentEnforced');
const sessionManager = require('../services/sessionManager');
const logger = require('../config/logger');
const twilio = require('twilio');

class WebhookController {
  async handleIncomingMessage(req, res) {
    const { Body: message, From: phone } = req.body;
    
    // Log incoming message details
    logger.info('Incoming WhatsApp message', {
      from: phone,
      message: message.substring(0, 50),
      rawFrom: req.body.From
    });
    
    try {
      // Find or create user
      const user = await User.findOrCreate(phone);
      
      // Validate session timeout
      const sessionValid = await sessionManager.validateSession(user);
      
      // Process message through state machine
      let response;
      if (!sessionValid) {
        response = {
          message: "Your session has timed out. Let's start fresh!\n\nType 'menu' to see your options.",
          newState: user.current_state
        };
      } else {
        response = await stateMachine.processMessage(user, message);
      }
      
      // Send response
      const twiml = new twilio.twiml.MessagingResponse();
      
      /*******************************************************************
       * LOCKED: TWO-MESSAGE COMPLETION FLOW - DO NOT MODIFY
       * This exact two-message sequence is critical for the habit logging
       * completion experience. The first message contains shareable content,
       * the second contains navigation instructions.
       * Any changes will break the user experience.
       *******************************************************************/
      // Check if we need to send a follow-up message (for completion screen)
      if (response.sendFollowUp) {
        // MESSAGE 1: Send shareable content first
        const messages = this.splitMessage(response.message);
        messages.forEach(msg => {
          twiml.message(msg);
        });
        
        // MESSAGE 2: Send instructions second
        const followUpMessage = `Click and hold the previous message to forward or share.

1️⃣ Edit today's habits
2️⃣ Back to menu

Make a selection (reply with a number).`;
        
        twiml.message(followUpMessage);
      } else {
        // Normal single message
        const messages = this.splitMessage(response.message);
        messages.forEach(msg => {
          twiml.message(msg);
        });
      }
      
      res.type('text/xml');
      res.send(twiml.toString());
      
    } catch (error) {
      logger.error('Error handling webhook', { 
        error: error.message,
        phone: '[REDACTED]',
        stack: error.stack
      });
      
      // Send error response
      const twiml = new twilio.twiml.MessagingResponse();
      twiml.message("Sorry, something went wrong. Please try again later or contact support if the issue persists.");
      
      res.type('text/xml');
      res.send(twiml.toString());
    }
  }
  
  // Split messages that exceed WhatsApp's character limit
  splitMessage(text, maxLength = 4000) {
    if (text.length <= maxLength) {
      return [text];
    }
    
    const messages = [];
    let currentMessage = '';
    const lines = text.split('\n');
    
    for (const line of lines) {
      if (currentMessage.length + line.length + 1 > maxLength) {
        messages.push(currentMessage.trim());
        currentMessage = line;
      } else {
        currentMessage += (currentMessage ? '\n' : '') + line;
      }
    }
    
    if (currentMessage) {
      messages.push(currentMessage.trim());
    }
    
    return messages;
  }
  
  // Verify webhook signature (for production)
  verifyWebhook(req, res, next) {
    if (process.env.NODE_ENV === 'production' && process.env.TWILIO_AUTH_TOKEN) {
      const twilioSignature = req.headers['x-twilio-signature'];
      const url = `${req.protocol}://${req.get('host')}${req.originalUrl}`;
      
      const isValid = twilio.validateRequest(
        process.env.TWILIO_AUTH_TOKEN,
        twilioSignature,
        url,
        req.body
      );
      
      if (!isValid) {
        logger.warn('Invalid Twilio signature', { ip: req.ip });
        return res.status(403).send('Forbidden');
      }
    }
    
    next();
  }
}

module.exports = new WebhookController();