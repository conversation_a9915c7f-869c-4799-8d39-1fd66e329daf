# TECHNICAL DOCUMENTATION FOR LEGAL TEAM

## System Architecture Overview

### Application Type
- **Category:** Software as a Service (SaaS)
- **Platform:** WhatsApp Business messaging bot
- **Architecture:** Monolithic Node.js application
- **Database:** PostgreSQL (relational)
- **Deployment:** Self-hosted Linux server

### Technology Stack

| Layer | Technology | Version | Purpose |
|-------|------------|---------|---------|
| Runtime | Node.js | Not specified | JavaScript runtime |
| Framework | Express.js | 4.18.2 | Web framework |
| Database | PostgreSQL | Not specified | Data persistence |
| Messaging | Twilio API | 4.19.0 | WhatsApp integration |
| Payments | FastSpring | API v2 | Payment processing |
| Email | Nodemailer | 7.0.5 | Email delivery |
| Security | Helmet.js | 7.1.0 | Security headers |
| Validation | Joi | 17.11.0 | Input validation |
| Encryption | bcrypt | 5.1.1 | Password hashing |
| Logging | Winston | 3.11.0 | Application logging |

## Data Architecture

### Data Classification

**Category A: Personally Identifiable Information (PII)**
- Phone numbers (WhatsApp identifiers)
- Email addresses (payment accounts)
- Display names (user preferences)
- IP addresses (connection logs)

**Category B: Sensitive Personal Data**
- Habit descriptions (may reveal health/religious/personal info)
- Behavioral patterns (completion tracking)
- Location data (timezone, inferred from phone)

**Category C: Financial Data**
- Transaction IDs
- Subscription status
- Payment amounts
- Affiliate relationships

**Category D: System Data**
- User IDs
- Session tokens
- System states
- Timestamps

### Data Lifecycle

```
Creation → Collection → Processing → Storage → Sharing → Retention → Deletion
   ↓           ↓           ↓          ↓         ↓           ↓          ↓
No notice  No consent  No privacy  Unencrypted  No DPAs  Indefinite  No process
```

### Data Storage Locations

| Data Type | Primary Storage | Backup | Encryption | Access Control |
|-----------|----------------|--------|------------|----------------|
| User Data | PostgreSQL | Unknown | ❌ None at rest | Single credential |
| Logs | File System | None | ❌ None | File permissions |
| Sessions | Memory | None | N/A | Application only |
| Payments | PostgreSQL | Unknown | ❌ None at rest | Single credential |
| Webhooks | PostgreSQL | Unknown | ❌ None at rest | Single credential |

## Security Architecture

### Current Security Measures

**Implemented:**
- TLS/HTTPS for API endpoints
- Rate limiting (100 req/15min global, 10 req/min per user)
- SQL injection prevention (parameterized queries)
- Helmet.js security headers
- Input validation with Joi
- Twilio webhook signature verification
- Session timeout (30 minutes)

**NOT Implemented:**
- Encryption at rest ❌
- Database access logging ❌
- Application firewall ❌
- Intrusion detection ❌
- Security monitoring ❌
- Penetration testing ❌
- Vulnerability scanning ❌
- Security incident response ❌

### Authentication & Authorization

**User Authentication:**
- Phone number as identifier
- No password system
- Access codes for payment verification
- No multi-factor authentication

**System Authentication:**
- Single database credential
- Environment variables for API keys
- No role-based access control
- No audit trail

### Network Architecture

```
Internet → Firewall(?) → Load Balancer(?) → Application Server → Database
              ↓                                    ↓
         [Unknown]                          Twilio API
                                           FastSpring API
                                           Email Service
```

## API Endpoints and Data Flows

### Public Endpoints

| Endpoint | Method | Purpose | Authentication | Data Collected |
|----------|--------|---------|---------------|----------------|
| /webhook/whatsapp | POST | Twilio webhook | Signature | Phone, messages |
| /webhook/fastspring | POST | Payment webhook | Secret | Email, payment |
| /health | GET | Health check | None | None |
| /test/* | Various | Test endpoints | None | Various |

### Data Flow Patterns

**Inbound Data:**
1. WhatsApp → Twilio → Webhook → Application → Database
2. FastSpring → Webhook → Application → Database

**Outbound Data:**
1. Application → Twilio → WhatsApp → User
2. Application → Email Service → User
3. Application → Logs → File System

**Internal Data:**
1. Application ↔ PostgreSQL
2. Application ↔ Session Store (memory)
3. Application → Winston → Logs

## Third-Party Data Sharing

### Data Shared with Each Service

**Twilio (Critical):**
- Every phone number
- Every message content
- All timestamps
- User states
- Session data

**FastSpring (Payment):**
- Email addresses
- Payment information
- Phone numbers (linked)
- Transaction data
- Subscription status

**Email Service:**
- Email addresses
- Access codes
- Transaction confirmations
- Template data

## Code Security Analysis

### Potential Vulnerabilities

1. **No Encryption at Rest**
   - Risk: Data readable if database compromised
   - Location: All database tables
   - Severity: Critical

2. **Environment Variables**
   - Risk: API keys in plain text
   - Location: .env file
   - Severity: High

3. **Single Database Credential**
   - Risk: No principle of least privilege
   - Location: db/connection.js
   - Severity: High

4. **No Input Sanitization for Habits**
   - Risk: XSS if habits displayed in web context
   - Location: Habit creation flow
   - Severity: Medium

5. **Test Endpoints in Production**
   - Risk: Unintended access to test functions
   - Location: server.js lines 52-136
   - Severity: Medium

### Compliance-Relevant Code Sections

**Data Collection Points:**
- `/src/controllers/webhookController.js`: Phone number collection
- `/src/models/User.js`: User data persistence
- `/src/controllers/fastspringController.js`: Payment data handling
- `/src/services/emailService.js`: Email processing

**Missing Compliance Features:**
- No consent collection mechanism
- No data export functionality
- No data deletion capability
- No audit logging
- No privacy controls

## Infrastructure and Deployment

### Current Infrastructure

**Server Environment:**
- OS: Linux 5.15.0-143-generic
- Platform: Self-hosted
- Location: Unknown (compliance issue)
- Backup: Not documented
- Disaster Recovery: None documented

**Database Configuration:**
- System: PostgreSQL
- Connection: Pool-based
- Backup: Not documented
- Replication: None apparent
- Encryption: None

### Deployment Process

**Current Method:**
- Manual deployment apparent
- No CI/CD pipeline documented
- No deployment audit trail
- No rollback procedure

**Security Concerns:**
- No deployment signing
- No integrity verification
- No change management
- No security scanning

## Data Retention and Deletion

### Current Retention (Actual)

| Data Type | Retention Period | Deletion Method | Legal Requirement |
|-----------|-----------------|-----------------|-------------------|
| User accounts | Indefinite | None | Should be defined |
| Habits | Indefinite | None | Should be limited |
| Logs | Indefinite | None | Should be 6-12 months |
| Payment records | Indefinite | None | 7 years typical |
| Webhooks | Indefinite | None | Should be 30-90 days |
| Sessions | 30 minutes | Automatic | Appropriate |

### Required Deletion Capabilities

**Not Implemented:**
- User-requested deletion
- Automated retention limits
- Cascading deletion
- Secure overwrite
- Deletion verification
- Audit trail of deletions

## Monitoring and Logging

### Current Logging

**What's Logged:**
- Application errors (Winston)
- Incoming webhooks
- Database errors
- Some user actions

**What's NOT Logged:**
- Data access
- Configuration changes
- Security events
- User consent
- Data exports/deletions
- Admin actions

### Required Logging for Compliance

1. **Access Logs**
   - Who accessed what data when
   - Purpose of access
   - Changes made

2. **Security Logs**
   - Failed authentication
   - Authorization failures
   - Suspicious patterns

3. **Compliance Logs**
   - Consent records
   - User rights requests
   - Data deletions
   - Policy acceptance

## Incident Response Capabilities

### Current Capabilities

**Detection:** ❌ None
**Response:** ❌ No process
**Recovery:** ❌ No plan
**Communication:** ❌ No process
**Documentation:** ❌ None

### Required Capabilities

1. **Detection Systems**
   - Real-time monitoring
   - Anomaly detection
   - Breach detection

2. **Response Process**
   - Incident team
   - Escalation path
   - Containment procedures

3. **Notification Requirements**
   - 72-hour regulatory (GDPR)
   - User notification
   - Partner notification

## Technical Recommendations for Compliance

### Immediate Technical Changes (48 hours)

1. **Add Consent Collection**
```javascript
// Add to User model
consent_given: BOOLEAN
consent_timestamp: TIMESTAMPTZ
consent_version: VARCHAR(10)
```

2. **Implement Basic Deletion**
```javascript
// Add deletion endpoint
async deleteUser(userId) {
  // Delete habits, logs, then user
  // Log the deletion
  // Notify user
}
```

3. **Add Age Verification**
```javascript
// Add to signup flow
if (age < 13) {
  return "Service not available for users under 13"
}
```

### Week 1 Technical Tasks

1. **Encryption at Rest**
   - Enable PostgreSQL encryption
   - Encrypt backup files
   - Secure key management

2. **Audit Logging**
   - Implement access logging
   - Add compliance event logging
   - Create audit trail table

3. **User Rights Portal**
   - Data export functionality
   - Data correction interface
   - Deletion request system

### Month 1 Technical Goals

1. **Security Hardening**
   - Vulnerability scanning
   - Penetration testing
   - Security monitoring

2. **Privacy Engineering**
   - Data minimization
   - Purpose limitation
   - Privacy by design

3. **Compliance Automation**
   - Automated retention
   - Consent management
   - Rights fulfillment

## Development Best Practices for Legal Compliance

### Required Development Changes

1. **Privacy by Design**
   - Privacy impact assessment for new features
   - Data minimization principle
   - Purpose limitation
   - Security first approach

2. **Documentation Requirements**
   - Document all data flows
   - Maintain processing records
   - Version control for policies
   - Change management logs

3. **Testing Requirements**
   - Security testing
   - Privacy testing
   - Compliance validation
   - Penetration testing

### Code Review Checklist for Legal

- [ ] Does it collect new personal data?
- [ ] Is consent obtained?
- [ ] Is data encrypted?
- [ ] Is access logged?
- [ ] Can user delete this data?
- [ ] Is retention defined?
- [ ] Are third parties involved?
- [ ] Is it documented?

## Questions for Technical Team

The legal team should ask:

1. **Data Location**
   - Where exactly is the server hosted?
   - Which country/jurisdiction?
   - Who has physical access?

2. **Backup and Recovery**
   - How are backups performed?
   - Where are backups stored?
   - How quickly can we recover?
   - Are backups encrypted?

3. **Access Control**
   - Who has database access?
   - Who has server access?
   - How is access logged?
   - How are credentials managed?

4. **Vendor Details**
   - Which email service provider?
   - Any other third parties?
   - Subprocessor list?

5. **Security Measures**
   - Last security audit?
   - Penetration testing?
   - Vulnerability scanning?
   - Incident history?

---

*Technical Documentation for Legal Review*  
*Prepared for Compliance Assessment*  
*Critical Technical Gaps Identified*