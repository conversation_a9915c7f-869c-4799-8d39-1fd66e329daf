# Application Environment
NODE_ENV=development
PORT=3000

# Database Configuration
DATABASE_URL=postgresql://habituser:yourpassword@localhost:5432/habittracker

# For Docker Compose
POSTGRES_DB=habittracker
POSTGRES_USER=habituser
POSTGRES_PASSWORD=yourpassword

# Twilio Configuration
TWILIO_ACCOUNT_SID=your_account_sid_here
TWILIO_AUTH_TOKEN=your_auth_token_here
TWILIO_PHONE_NUMBER=+**********

# Logging
LOG_LEVEL=info
LOG_DIR=/var/log

# Optional: pgAdmin (for Docker Compose debug profile)
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin