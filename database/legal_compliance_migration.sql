-- Legal Compliance Database Migration
-- Adds necessary fields and tables for GDPR/CCPA compliance
-- Run this migration before deploying compliance features

-- Add consent and legal fields to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS 
    consent_given BOOLEAN DEFAULT FALSE,
    consent_timestamp TIMESTAMPTZ,
    consent_version VARCHAR(10) DEFAULT '1.0',
    age_verified BOOLEAN DEFAULT FALSE,
    age_verification_date TIMESTAMPTZ,
    terms_accepted BOOLEAN DEFAULT FALSE,
    terms_version VARCHAR(10) DEFAULT '1.0',
    terms_accepted_date TIMESTAMPTZ,
    data_retention_date TIMESTAMPTZ,
    opt_out_communications BOOLEAN DEFAULT FALSE,
    opt_out_date TIMESTAMPTZ,
    account_deletion_requested BOOLEAN DEFAULT FALSE,
    deletion_request_date TIMESTAMPTZ,
    data_export_requests INTEGER DEFAULT 0,
    last_export_date TIMESTAMPTZ;

-- Create user_consents table for detailed consent tracking
CREATE TABLE IF NOT EXISTS user_consents (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    consent_type VARCHAR(50) NOT NULL, -- 'privacy', 'terms', 'marketing', 'age'
    consent_given BOOLEAN NOT NULL,
    consent_method VARCHAR(50) NOT NULL, -- 'whatsapp', 'web', 'email'
    consent_version VARCHAR(10) NOT NULL DEFAULT '1.0',
    consent_timestamp TIMESTAMPTZ DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT,
    withdrawn_date TIMESTAMPTZ,
    notes TEXT
);

-- Create data_requests table for GDPR/CCPA request tracking
CREATE TABLE IF NOT EXISTS data_requests (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    request_type VARCHAR(50) NOT NULL, -- 'access', 'export', 'delete', 'correct', 'restrict'
    request_method VARCHAR(50) NOT NULL, -- 'whatsapp', 'email', 'web'
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'rejected'
    request_date TIMESTAMPTZ DEFAULT NOW(),
    completion_date TIMESTAMPTZ,
    processed_by VARCHAR(100),
    notes TEXT,
    verification_code VARCHAR(50),
    expiry_date TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '30 days')
);

-- Create data_exports table to track export history
CREATE TABLE IF NOT EXISTS data_exports (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    export_type VARCHAR(50) NOT NULL, -- 'full', 'habits', 'logs', 'account'
    export_format VARCHAR(20) DEFAULT 'json', -- 'json', 'csv', 'xml'
    file_size BIGINT,
    export_date TIMESTAMPTZ DEFAULT NOW(),
    download_count INTEGER DEFAULT 0,
    last_download TIMESTAMPTZ,
    expiry_date TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '7 days'),
    file_hash VARCHAR(64)
);

-- Create legal_documents table for version tracking
CREATE TABLE IF NOT EXISTS legal_documents (
    id SERIAL PRIMARY KEY,
    document_type VARCHAR(50) NOT NULL, -- 'privacy_policy', 'terms_of_service', 'cookie_policy'
    version VARCHAR(10) NOT NULL,
    content TEXT,
    effective_date TIMESTAMPTZ DEFAULT NOW(),
    created_date TIMESTAMPTZ DEFAULT NOW(),
    created_by VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    change_summary TEXT
);

-- Create audit_log table for compliance tracking
CREATE TABLE IF NOT EXISTS audit_log (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50), -- 'user', 'habit', 'log', 'consent'
    resource_id INTEGER,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    session_id VARCHAR(100),
    notes TEXT
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_consents_user_id ON user_consents(user_id);
CREATE INDEX IF NOT EXISTS idx_user_consents_type ON user_consents(consent_type);
CREATE INDEX IF NOT EXISTS idx_user_consents_timestamp ON user_consents(consent_timestamp);

CREATE INDEX IF NOT EXISTS idx_data_requests_user_id ON data_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_data_requests_status ON data_requests(status);
CREATE INDEX IF NOT EXISTS idx_data_requests_type ON data_requests(request_type);

CREATE INDEX IF NOT EXISTS idx_data_exports_user_id ON data_exports(user_id);
CREATE INDEX IF NOT EXISTS idx_data_exports_date ON data_exports(export_date);

CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_action ON audit_log(action);
CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON audit_log(timestamp);

-- Insert initial legal document versions
INSERT INTO legal_documents (document_type, version, content, change_summary) 
VALUES 
    ('privacy_policy', '1.0', 'Initial Privacy Policy with GDPR/CCPA compliance', 'Initial version'),
    ('terms_of_service', '1.0', 'Initial Terms of Service with liability limitations', 'Initial version')
ON CONFLICT DO NOTHING;

-- Function to automatically set data retention date
CREATE OR REPLACE FUNCTION set_data_retention_date()
RETURNS TRIGGER AS $$
BEGIN
    NEW.data_retention_date = NOW() + INTERVAL '1 year';
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to set retention date on user creation
DROP TRIGGER IF EXISTS set_retention_date_trigger ON users;
CREATE TRIGGER set_retention_date_trigger
    BEFORE INSERT ON users
    FOR EACH ROW
    EXECUTE FUNCTION set_data_retention_date();

-- Function to log consent changes
CREATE OR REPLACE FUNCTION log_consent_change()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO audit_log (
        user_id, 
        action, 
        resource_type, 
        resource_id, 
        old_values, 
        new_values,
        timestamp
    ) VALUES (
        COALESCE(NEW.user_id, OLD.user_id),
        CASE 
            WHEN TG_OP = 'INSERT' THEN 'consent_granted'
            WHEN TG_OP = 'UPDATE' THEN 'consent_updated'
            WHEN TG_OP = 'DELETE' THEN 'consent_withdrawn'
        END,
        'consent',
        COALESCE(NEW.id, OLD.id),
        CASE WHEN TG_OP = 'DELETE' THEN row_to_json(OLD) ELSE NULL END,
        CASE WHEN TG_OP = 'DELETE' THEN NULL ELSE row_to_json(NEW) END,
        NOW()
    );
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Trigger to log consent changes
DROP TRIGGER IF EXISTS log_consent_trigger ON user_consents;
CREATE TRIGGER log_consent_trigger
    AFTER INSERT OR UPDATE OR DELETE ON user_consents
    FOR EACH ROW
    EXECUTE FUNCTION log_consent_change();

-- Function to handle data retention cleanup
CREATE OR REPLACE FUNCTION cleanup_expired_data()
RETURNS void AS $$
BEGIN
    -- Delete users who requested deletion more than 30 days ago
    DELETE FROM users 
    WHERE account_deletion_requested = TRUE 
    AND deletion_request_date < NOW() - INTERVAL '30 days';
    
    -- Delete inactive users (no activity for 1 year, no consent)
    DELETE FROM users 
    WHERE last_active < NOW() - INTERVAL '1 year' 
    AND consent_given = FALSE;
    
    -- Clean up expired export files
    DELETE FROM data_exports 
    WHERE expiry_date < NOW();
    
    -- Clean up old audit logs (keep for 2 years)
    DELETE FROM audit_log 
    WHERE timestamp < NOW() - INTERVAL '2 years';
    
    RAISE NOTICE 'Data cleanup completed';
END;
$$ LANGUAGE plpgsql;

-- View for compliance reporting
CREATE OR REPLACE VIEW compliance_dashboard AS
SELECT 
    COUNT(*) as total_users,
    COUNT(CASE WHEN consent_given = TRUE THEN 1 END) as consented_users,
    COUNT(CASE WHEN age_verified = TRUE THEN 1 END) as age_verified_users,
    COUNT(CASE WHEN terms_accepted = TRUE THEN 1 END) as terms_accepted_users,
    COUNT(CASE WHEN opt_out_communications = TRUE THEN 1 END) as opted_out_users,
    COUNT(CASE WHEN account_deletion_requested = TRUE THEN 1 END) as deletion_requests,
    AVG(EXTRACT(DAYS FROM NOW() - last_active)) as avg_days_inactive
FROM users;

-- Migration completion log
INSERT INTO audit_log (action, resource_type, notes, timestamp)
VALUES ('database_migration', 'system', 'Legal compliance migration completed', NOW());

COMMIT;