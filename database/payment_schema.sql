-- FastSpring Payment Integration Schema
-- Complete payment system for WhatsApp Habit Tracker

-- Drop existing payment tables if they exist
DROP TABLE IF EXISTS affiliate_payouts CASCADE;
DROP TABLE IF EXISTS affiliate_referrals CASCADE;
DROP TABLE IF EXISTS email_queue CASCADE;
DROP TABLE IF EXISTS webhook_events CASCADE;
DROP TABLE IF EXISTS payment_transactions CASCADE;
DROP TABLE IF EXISTS access_codes CASCADE;
DROP TABLE IF EXISTS paid_users CASCADE;

-- Main paid users table
CREATE TABLE paid_users (
  id SERIAL PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  phone VARCHAR(20),
  access_code VARCHAR(20) UNIQUE NOT NULL,
  
  -- Subscription details
  subscription_type VARCHAR(20) NOT NULL CHECK (subscription_type IN ('monthly', 'yearly')),
  subscription_id VARCHAR(100),
  customer_id VARCHAR(100),
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'expired', 'pending')),
  
  -- Payment info
  amount_paid DECIMAL(10,2),
  currency VARCHAR(3) DEFAULT 'USD',
  
  -- Affiliate info
  is_affiliate BOOLEAN DEFAULT FALSE,
  affiliate_code VARCHAR(20) UNIQUE,
  commission_rate DECIMAL(5,2) DEFAULT 30.00,
  
  -- Dates
  paid_at TIMESTAMPTZ NOT NULL,
  expires_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Metadata
  fastspring_order_id VARCHAR(100),
  fastspring_subscription_id VARCHAR(100),
  test_mode BOOLEAN DEFAULT FALSE
);

-- Access codes table for redemption
CREATE TABLE access_codes (
  id SERIAL PRIMARY KEY,
  code VARCHAR(20) UNIQUE NOT NULL,
  paid_user_id INTEGER REFERENCES paid_users(id),
  
  -- Usage tracking
  used_by_phone VARCHAR(20),
  used_at TIMESTAMPTZ,
  
  -- Validity
  is_active BOOLEAN DEFAULT TRUE,
  expires_at TIMESTAMPTZ,
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  notes TEXT
);

-- Payment transactions log
CREATE TABLE payment_transactions (
  id SERIAL PRIMARY KEY,
  paid_user_id INTEGER REFERENCES paid_users(id),
  
  -- Transaction details
  transaction_id VARCHAR(100) UNIQUE NOT NULL,
  type VARCHAR(50) NOT NULL, -- payment, refund, chargeback
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  
  -- FastSpring details
  fastspring_order_id VARCHAR(100),
  fastspring_reference VARCHAR(100),
  
  -- Status
  status VARCHAR(20) DEFAULT 'completed',
  
  -- Dates
  transaction_date TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Webhook events tracking
CREATE TABLE webhook_events (
  id SERIAL PRIMARY KEY,
  event_id VARCHAR(100) UNIQUE,
  event_type VARCHAR(50) NOT NULL,
  
  -- Processing
  processed BOOLEAN DEFAULT FALSE,
  processed_at TIMESTAMPTZ,
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  
  -- Raw data
  payload JSONB NOT NULL,
  headers JSONB,
  
  -- Metadata
  source VARCHAR(20) DEFAULT 'fastspring',
  test_mode BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Email queue for notifications
CREATE TABLE email_queue (
  id SERIAL PRIMARY KEY,
  to_email VARCHAR(255) NOT NULL,
  subject VARCHAR(255) NOT NULL,
  template VARCHAR(50) NOT NULL,
  
  -- Template variables
  template_data JSONB NOT NULL,
  
  -- Status
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'failed', 'cancelled')),
  sent_at TIMESTAMPTZ,
  error_message TEXT,
  retry_count INTEGER DEFAULT 0,
  
  -- Priority
  priority INTEGER DEFAULT 5,
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Affiliate referrals tracking
CREATE TABLE affiliate_referrals (
  id SERIAL PRIMARY KEY,
  affiliate_id INTEGER REFERENCES paid_users(id),
  referred_user_id INTEGER REFERENCES paid_users(id),
  
  -- Commission details
  commission_amount DECIMAL(10,2),
  commission_status VARCHAR(20) DEFAULT 'pending',
  
  -- Payment info
  referral_order_id VARCHAR(100),
  referral_amount DECIMAL(10,2),
  
  -- Dates
  referred_at TIMESTAMPTZ DEFAULT NOW(),
  paid_out_at TIMESTAMPTZ,
  
  UNIQUE(affiliate_id, referred_user_id)
);

-- Affiliate payouts tracking
CREATE TABLE affiliate_payouts (
  id SERIAL PRIMARY KEY,
  affiliate_id INTEGER REFERENCES paid_users(id),
  
  -- Payout details
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  method VARCHAR(50), -- paypal, bank_transfer, credit
  
  -- Status
  status VARCHAR(20) DEFAULT 'pending',
  
  -- Reference
  transaction_reference VARCHAR(100),
  notes TEXT,
  
  -- Dates
  payout_date TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_paid_users_email ON paid_users(email);
CREATE INDEX idx_paid_users_phone ON paid_users(phone);
CREATE INDEX idx_paid_users_access_code ON paid_users(access_code);
CREATE INDEX idx_paid_users_status ON paid_users(status);
CREATE INDEX idx_paid_users_affiliate_code ON paid_users(affiliate_code);
CREATE INDEX idx_access_codes_code ON access_codes(code);
CREATE INDEX idx_access_codes_active ON access_codes(is_active);
CREATE INDEX idx_webhook_events_processed ON webhook_events(processed);
CREATE INDEX idx_webhook_events_type ON webhook_events(event_type);
CREATE INDEX idx_email_queue_status ON email_queue(status);
CREATE INDEX idx_affiliate_referrals_affiliate ON affiliate_referrals(affiliate_id);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_payment_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER update_paid_users_updated_at BEFORE UPDATE ON paid_users
  FOR EACH ROW EXECUTE FUNCTION update_payment_updated_at();

CREATE TRIGGER update_email_queue_updated_at BEFORE UPDATE ON email_queue
  FOR EACH ROW EXECUTE FUNCTION update_payment_updated_at();

-- View for active subscriptions
CREATE VIEW active_subscriptions AS
SELECT 
  pu.id,
  pu.email,
  pu.phone,
  pu.access_code,
  pu.subscription_type,
  pu.status,
  pu.paid_at,
  pu.expires_at,
  pu.is_affiliate,
  pu.affiliate_code
FROM paid_users pu
WHERE pu.status = 'active'
  AND (pu.expires_at IS NULL OR pu.expires_at > NOW());

-- View for affiliate performance
CREATE VIEW affiliate_performance AS
SELECT 
  pu.id as affiliate_id,
  pu.email,
  pu.affiliate_code,
  COUNT(ar.id) as total_referrals,
  SUM(ar.commission_amount) as total_commissions,
  SUM(CASE WHEN ar.commission_status = 'paid' THEN ar.commission_amount ELSE 0 END) as paid_commissions,
  SUM(CASE WHEN ar.commission_status = 'pending' THEN ar.commission_amount ELSE 0 END) as pending_commissions
FROM paid_users pu
LEFT JOIN affiliate_referrals ar ON pu.id = ar.affiliate_id
WHERE pu.is_affiliate = true
GROUP BY pu.id, pu.email, pu.affiliate_code;