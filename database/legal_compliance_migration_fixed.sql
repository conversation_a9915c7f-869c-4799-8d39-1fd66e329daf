-- Legal Compliance Database Migration - Fixed Version
-- Adds necessary fields and tables for GDPR/CCPA compliance

BEGIN;

-- Add consent and legal fields to users table one by one
ALTER TABLE users ADD COLUMN IF NOT EXISTS consent_given BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS consent_timestamp TIMESTAMPTZ;
ALTER TABLE users ADD COLUMN IF NOT EXISTS consent_version VARCHAR(10) DEFAULT '1.0';
ALTER TABLE users ADD COLUMN IF NOT EXISTS age_verified BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS age_verification_date TIMESTAMPTZ;
ALTER TABLE users ADD COLUMN IF NOT EXISTS terms_accepted BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS terms_version VARCHAR(10) DEFAULT '1.0';
ALTER TABLE users ADD COLUMN IF NOT EXISTS terms_accepted_date TIMESTAMPTZ;
ALTER TABLE users ADD COLUMN IF NOT EXISTS data_retention_date TIMESTAMPTZ;
ALTER TABLE users ADD COLUMN IF NOT EXISTS opt_out_communications BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS opt_out_date TIMESTAMPTZ;
ALTER TABLE users ADD COLUMN IF NOT EXISTS account_deletion_requested BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS deletion_request_date TIMESTAMPTZ;
ALTER TABLE users ADD COLUMN IF NOT EXISTS data_export_requests INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS last_export_date TIMESTAMPTZ;

-- Create user_consents table for detailed consent tracking
CREATE TABLE IF NOT EXISTS user_consents (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    consent_type VARCHAR(50) NOT NULL,
    consent_given BOOLEAN NOT NULL,
    consent_method VARCHAR(50) NOT NULL,
    consent_version VARCHAR(10) NOT NULL DEFAULT '1.0',
    consent_timestamp TIMESTAMPTZ DEFAULT NOW(),
    ip_address INET,
    user_agent TEXT,
    withdrawn_date TIMESTAMPTZ,
    notes TEXT
);

-- Create data_requests table for GDPR/CCPA request tracking
CREATE TABLE IF NOT EXISTS data_requests (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    request_type VARCHAR(50) NOT NULL,
    request_method VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    request_date TIMESTAMPTZ DEFAULT NOW(),
    completion_date TIMESTAMPTZ,
    processed_by VARCHAR(100),
    notes TEXT,
    verification_code VARCHAR(50),
    expiry_date TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '30 days')
);

-- Create data_exports table to track export history
CREATE TABLE IF NOT EXISTS data_exports (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    export_type VARCHAR(50) NOT NULL,
    export_format VARCHAR(20) DEFAULT 'json',
    file_size BIGINT,
    export_date TIMESTAMPTZ DEFAULT NOW(),
    download_count INTEGER DEFAULT 0,
    last_download TIMESTAMPTZ,
    expiry_date TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '7 days'),
    file_hash VARCHAR(64)
);

-- Create legal_documents table for version tracking
CREATE TABLE IF NOT EXISTS legal_documents (
    id SERIAL PRIMARY KEY,
    document_type VARCHAR(50) NOT NULL,
    version VARCHAR(10) NOT NULL,
    content TEXT,
    effective_date TIMESTAMPTZ DEFAULT NOW(),
    created_date TIMESTAMPTZ DEFAULT NOW(),
    created_by VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    change_summary TEXT
);

-- Create audit_log table for compliance tracking
CREATE TABLE IF NOT EXISTS audit_log (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id INTEGER,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    session_id VARCHAR(100),
    notes TEXT
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_consents_user_id ON user_consents(user_id);
CREATE INDEX IF NOT EXISTS idx_user_consents_type ON user_consents(consent_type);
CREATE INDEX IF NOT EXISTS idx_user_consents_timestamp ON user_consents(consent_timestamp);

CREATE INDEX IF NOT EXISTS idx_data_requests_user_id ON data_requests(user_id);
CREATE INDEX IF NOT EXISTS idx_data_requests_status ON data_requests(status);
CREATE INDEX IF NOT EXISTS idx_data_requests_type ON data_requests(request_type);

CREATE INDEX IF NOT EXISTS idx_data_exports_user_id ON data_exports(user_id);
CREATE INDEX IF NOT EXISTS idx_data_exports_date ON data_exports(export_date);

CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_log_action ON audit_log(action);
CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON audit_log(timestamp);

-- Insert initial legal document versions
INSERT INTO legal_documents (document_type, version, content, change_summary) 
VALUES 
    ('privacy_policy', '1.0', 'Initial Privacy Policy with GDPR/CCPA compliance', 'Initial version'),
    ('terms_of_service', '1.0', 'Initial Terms of Service with liability limitations', 'Initial version')
ON CONFLICT DO NOTHING;

-- Function to automatically set data retention date
CREATE OR REPLACE FUNCTION set_data_retention_date()
RETURNS TRIGGER AS $$
BEGIN
    NEW.data_retention_date = NOW() + INTERVAL '1 year';
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to log consent changes
CREATE OR REPLACE FUNCTION log_consent_change()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO audit_log (
        user_id, 
        action, 
        resource_type, 
        resource_id, 
        old_values, 
        new_values,
        timestamp
    ) VALUES (
        COALESCE(NEW.user_id, OLD.user_id),
        CASE 
            WHEN TG_OP = 'INSERT' THEN 'consent_granted'
            WHEN TG_OP = 'UPDATE' THEN 'consent_updated'
            WHEN TG_OP = 'DELETE' THEN 'consent_withdrawn'
        END,
        'consent',
        COALESCE(NEW.id, OLD.id),
        CASE WHEN TG_OP = 'DELETE' THEN row_to_json(OLD) ELSE NULL END,
        CASE WHEN TG_OP = 'DELETE' THEN NULL ELSE row_to_json(NEW) END,
        NOW()
    );
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Function to handle data retention cleanup
CREATE OR REPLACE FUNCTION cleanup_expired_data()
RETURNS void AS $$
BEGIN
    -- Delete users who requested deletion more than 30 days ago
    DELETE FROM users 
    WHERE account_deletion_requested = TRUE 
    AND deletion_request_date < NOW() - INTERVAL '30 days';
    
    -- Delete inactive users (no activity for 1 year, no consent)
    DELETE FROM users 
    WHERE last_active < NOW() - INTERVAL '1 year' 
    AND consent_given = FALSE;
    
    -- Clean up expired export files
    DELETE FROM data_exports 
    WHERE expiry_date < NOW();
    
    -- Clean up old audit logs (keep for 2 years)
    DELETE FROM audit_log 
    WHERE timestamp < NOW() - INTERVAL '2 years';
    
    RAISE NOTICE 'Data cleanup completed';
END;
$$ LANGUAGE plpgsql;

-- Migration completion log
INSERT INTO audit_log (action, resource_type, notes, timestamp)
VALUES ('database_migration', 'system', 'Legal compliance migration completed', NOW());

COMMIT;