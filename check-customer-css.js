#!/usr/bin/env node

require('dotenv').config();
const pool = require('./src/db/connection');
const emailService = require('./src/services/emailService');

(async () => {
  try {
    // Get the most recent actual customer email (not test)
    const result = await pool.query("SELECT id, to_email, template, template_data FROM email_queue WHERE to_email NOT LIKE '%test%' AND to_email NOT LIKE '%example%' ORDER BY id DESC LIMIT 1");
    
    if (result.rows.length === 0) {
      console.log('No customer emails found');
      return;
    }
    
    const email = result.rows[0];
    console.log('🔍 CHECKING MOST RECENT CUSTOMER EMAIL:');
    console.log('ID:', email.id);
    console.log('Email:', email.to_email);
    console.log('Template:', email.template);
    
    // Generate the actual content
    let emailContent;
    if (email.template === 'welcome_dynamic') {
      emailContent = emailService.getWelcomeDynamicTemplate(email.template_data);
    } else if (email.template === 'welcome_multi_code') {
      emailContent = emailService.getWelcomeMultiCodeTemplate(email.template_data);
    }
    
    const html = emailContent.html;
    
    // Look for the specific issue you mentioned
    const brokenCSS = [];
    
    // Search for 'align-items: cent' specifically  
    const centMatches = html.match(/align-items:\s*cent[^e]/g);
    if (centMatches) {
      brokenCSS.push('❌ FOUND: align-items: cent (truncated)');
      centMatches.forEach(match => brokenCSS.push(`   ${match}`));
    }
    
    // Search for other broken CSS patterns
    const styleMatches = html.match(/style="[^"]*"/g);
    if (styleMatches) {
      styleMatches.forEach((style, i) => {
        // Check for truncated align-items
        if (style.includes('align-items: cent') && !style.includes('align-items: center')) {
          brokenCSS.push(`❌ Style ${i+1}: ${style}`);
        }
        
        // Check for unclosed quotes
        const content = style.match(/style="([^"]*)"/)[1];
        if (content.includes("'") && (content.match(/'/g) || []).length % 2 !== 0) {
          brokenCSS.push(`❌ Unclosed quotes ${i+1}: ${style}`);
        }
      });
    }
    
    if (brokenCSS.length > 0) {
      console.log('\n🚨 BROKEN CSS FOUND:');
      brokenCSS.forEach(issue => console.log(issue));
    } else {
      console.log('\n✅ No broken CSS found in customer email');
    }
    
    // Also check for step 5 truncation
    const step5Index = html.indexOf('Start building unstoppable habits!');
    if (step5Index !== -1) {
      const afterStep5 = html.substring(step5Index + 'Start building unstoppable habits!'.length);
      console.log(`\n📊 Characters after step 5: ${afterStep5.length}`);
      
      if (afterStep5.length < 1000) {
        console.log('🚨 POSSIBLE TRUNCATION - Very few characters after step 5');
      }
    }
    
    // Save the customer email for inspection
    require('fs').writeFileSync(`/var/www/lockin/customer-email-${email.id}.html`, html);
    console.log(`\n💾 Customer email saved to: customer-email-${email.id}.html`);
    
  } catch (error) {
    console.error('Error:', error.message);
  }
  await pool.end();
})();